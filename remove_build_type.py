import sys
import pathlib

TARGET_LINE = "set(CMAKE_BUILD_TYPE Release)"

def remove_cmake_build_type(file_path: pathlib.Path) -> None:
    """
    删除文件中完全匹配的 TARGET_LINE 并覆盖原文件
    """
    lines = file_path.read_text(encoding="utf-8").splitlines(keepends=True)

    # 逐行比较，保留不匹配的
    new_lines = [ln for ln in lines if ln.rstrip("\r\n") != TARGET_LINE]

    if len(new_lines) == len(lines):
        print(f"{file_path}: 没有发现需要删除的行。")
        return

    # 写回文件
    file_path.write_text("".join(new_lines), encoding="utf-8")
    print(f"{file_path}: 已删除 'set(CMAKE_BUILD_TYPE Release)' 行。")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python remove_build_type.py <CMakeLists.txt>")
        sys.exit(1)

    path = pathlib.Path(sys.argv[1]).expanduser()
    if not path.is_file():
        print(f"错误: {path} 不是有效文件。")
        sys.exit(1)

    remove_cmake_build_type(path)

