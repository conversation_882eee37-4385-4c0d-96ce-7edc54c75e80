cmake_minimum_required(VERSION 3.5)

#========================
# Project
#========================

set(CUR_LIB ros_app)
project(${CUR_LIB})

add_definitions(-std=c++17)
# Platform-specific settings
if(WIN32)
    # Windows-specific settings
    add_compile_options(/W4)  # Warning level 4 on Windows
elseif(UNIX AND NOT APPLE)
    # Linux/Ubuntu-specific settings
    add_compile_options(-Wall -Wextra -pedantic)  # Enable common warnings on Linux
endif()
add_compile_options(-std=c++17)
add_definitions(-DDEBUG_LEVEL=0)

include(cmake/srcs.cmake)

set(${CUR_LIB} PARENT_SCOPE)

add_executable(driver_data_publisher driver_data_publisher.cpp)
target_link_libraries(driver_data_publisher PRIVATE ros_app interface)

add_executable(front_end_debug_node front_end_debug_node.cpp)
target_link_libraries(front_end_debug_node PRIVATE ros_app)

add_executable(front_end_ros_app front_end_ros_app.cpp)
target_link_libraries(front_end_ros_app PRIVATE ros_app)

#find_package(Qt5 QUIET COMPONENTS Widgets)
#if (Qt5_FOUND)
#    # 设置包含目录
#    include_directories(${Qt5Widgets_INCLUDE_DIRS})
#
#    # 添加可执行文件
#    set(CMAKE_AUTOMOC ON)  # 启用自动 MOC
#    set(CMAKE_AUTORCC ON)  # 启用自动资源编译
#    set(CMAKE_AUTOUIC ON)  # 启用自动 UI 编译
#
#    add_executable(qt_ros_ui_node qt_ros_ui_node.cpp)
#    target_link_libraries(qt_ros_ui_node PRIVATE Qt5::Widgets ros_app)
#endif ()