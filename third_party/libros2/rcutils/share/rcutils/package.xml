<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>rcutils</name>
  <version>5.1.6</version>
  <description>Package containing various utility types and functions for C</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author email="<EMAIL>"><PERSON><PERSON></author>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>
  <buildtool_depend>python3-empy</buildtool_depend>

  <depend>libatomic</depend>

  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>mimick_vendor</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>osrf_testing_tools_cpp</test_depend>
  <test_depend>performance_test_fixture</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
