// Copyright 2017 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/// \file

#ifndef RCUTILS__GET_ENV_H_
#define RCUTILS__GET_ENV_H_

// TODO(christophebedard) remove this header completely in I-turtle

#ifdef _MSC_VER
#pragma message ("rcutils/get_env.h has been deprecated, please include rcutils/env.h instead")
#else
#warning rcutils/get_env.h has been deprecated, please include rcutils/env.h instead
#endif

#include "rcutils/env.h"

#endif  // RCUTILS__GET_ENV_H_
