// generated from
// rosidl_typesupport_fastrtps_cpp/resource/rosidl_typesupport_fastrtps_cpp__visibility_control.h.in
// generated code does not contain a copyright notice

#ifndef @PROJECT_NAME_UPPER@__MSG__ROSIDL_TYPESUPPORT_FASTRTPS_CPP__VISIBILITY_CONTROL_H_
#define @PROJECT_NAME_UPPER@__MSG__ROSIDL_TYPESUPPORT_FASTRTPS_CPP__VISIBILITY_CONTROL_H_

#if __cplusplus
extern "C"
{
#endif

// This logic was borrowed (then namespaced) from the examples on the gcc wiki:
//     https://gcc.gnu.org/wiki/Visibility

#if defined _WIN32 || defined __CYGWIN__
  #ifdef __GNUC__
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_EXPORT_@PROJECT_NAME@ __attribute__ ((dllexport))
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_IMPORT_@PROJECT_NAME@ __attribute__ ((dllimport))
  #else
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_EXPORT_@PROJECT_NAME@ __declspec(dllexport)
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_IMPORT_@PROJECT_NAME@ __declspec(dllimport)
  #endif
  #ifdef ROSIDL_TYPESUPPORT_FASTRTPS_CPP_BUILDING_DLL_@PROJECT_NAME@
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_@PROJECT_NAME@ ROSIDL_TYPESUPPORT_FASTRTPS_CPP_EXPORT_@PROJECT_NAME@
  #else
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_@PROJECT_NAME@ ROSIDL_TYPESUPPORT_FASTRTPS_CPP_IMPORT_@PROJECT_NAME@
  #endif
#else
  #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_EXPORT_@PROJECT_NAME@ __attribute__ ((visibility("default")))
  #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_IMPORT_@PROJECT_NAME@
  #if __GNUC__ >= 4
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_@PROJECT_NAME@ __attribute__ ((visibility("default")))
  #else
    #define ROSIDL_TYPESUPPORT_FASTRTPS_CPP_PUBLIC_@PROJECT_NAME@
  #endif
#endif

#if __cplusplus
}
#endif

#endif  // @PROJECT_NAME_UPPER@__MSG__ROSIDL_TYPESUPPORT_FASTRTPS_CPP__VISIBILITY_CONTROL_H_
