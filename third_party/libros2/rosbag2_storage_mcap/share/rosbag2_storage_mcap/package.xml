<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rosbag2_storage_mcap</name>
  <version>0.15.13</version>
  <description>rosbag2 storage plugin using the MCAP file format</description>
  <maintainer email="<EMAIL>">Foxglove</maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">ROS Tooling Working Group</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>ament_index_cpp</depend>
  <depend>mcap_vendor</depend>
  <depend>pluginlib</depend>
  <depend>rcutils</depend>
  <depend>rosbag2_storage</depend>

  <test_depend>ament_cmake_clang_format</test_depend>
  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>rcpputils</test_depend>
  <test_depend>rosbag2_test_common</test_depend>
  <test_depend>std_msgs</test_depend>
  <test_depend>rosbag2_storage_mcap_testdata</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
