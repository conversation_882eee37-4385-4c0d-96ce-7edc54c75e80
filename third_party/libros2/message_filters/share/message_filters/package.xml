<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>message_filters</name>
  <version>4.3.5</version>
  <description>
    A set of ROS2 message filters which take in messages and may output those messages at a later time, based on the conditions that filter needs met.
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>
  <url>https://github.com/intel/ros2_message_filters</url>

  <author><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>

  <buildtool_depend>ament_cmake_python</buildtool_depend>
  <buildtool_depend>ament_cmake_ros</buildtool_depend>
  <buildtool_depend>python_cmake_module</buildtool_depend>

  <build_depend>rclcpp</build_depend>
  <build_depend>rcutils</build_depend>
  <build_export_depend>rclcpp</build_export_depend>
  <build_export_depend>rcutils</build_export_depend>

  <exec_depend>builtin_interfaces</exec_depend>
  <exec_depend>rclpy</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>sensor_msgs</test_depend>
  <test_depend>std_msgs</test_depend>
  <test_depend>rclcpp_lifecycle</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
