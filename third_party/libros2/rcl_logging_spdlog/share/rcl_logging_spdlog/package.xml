<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rcl_logging_spdlog</name>
  <version>2.3.1</version>
  <description>Implementation of rcl_logging API for an spdlog backend.</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>Apache License 2.0</license>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>

  <build_depend>spdlog_vendor</build_depend>
  <build_depend>spdlog</build_depend>

  <depend>rcl_logging_interface</depend>
  <depend>rcpputils</depend>
  <depend>rcutils</depend>

  <exec_depend>spdlog_vendor</exec_depend>
  <exec_depend>spdlog</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>performance_test_fixture</test_depend>

  <member_of_group>rcl_logging_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
