# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_ament_index_cpp_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED ament_index_cpp_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(ament_index_cpp_FOUND FALSE)
  elseif(NOT ament_index_cpp_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(ament_index_cpp_FOUND FALSE)
  endif()
  return()
endif()
set(_ament_index_cpp_CONFIG_INCLUDED TRUE)

# output package information
if(NOT ament_index_cpp_FIND_QUIETLY)
  message(STATUS "Found ament_index_cpp: 1.4.0 (${ament_index_cpp_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'ament_index_cpp' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${ament_index_cpp_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(ament_index_cpp_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "ament_cmake_export_targets-extras.cmake")
foreach(_extra ${_extras})
  include("${ament_index_cpp_DIR}/${_extra}")
endforeach()
