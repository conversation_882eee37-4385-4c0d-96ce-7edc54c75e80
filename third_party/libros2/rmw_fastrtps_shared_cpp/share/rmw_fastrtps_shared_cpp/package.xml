<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rmw_fastrtps_shared_cpp</name>
  <version>6.2.7</version>
  <description>Code shared on static and dynamic type support of rmw_fastrtps_cpp.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>
  <buildtool_depend>fastrtps_cmake_module</buildtool_depend>

  <buildtool_export_depend>ament_cmake</buildtool_export_depend>

  <build_depend>fastcdr</build_depend>
  <build_depend>fastrtps</build_depend>
  <build_depend>fastrtps_cmake_module</build_depend>
  <build_depend>rcpputils</build_depend>
  <build_depend>rcutils</build_depend>
  <build_depend>rmw</build_depend>
  <build_depend>rmw_dds_common</build_depend>
  <build_depend>rosidl_typesupport_introspection_c</build_depend>
  <build_depend>rosidl_typesupport_introspection_cpp</build_depend>
  <build_depend>tracetools</build_depend>

  <build_export_depend>fastcdr</build_export_depend>
  <build_export_depend>fastrtps</build_export_depend>
  <build_export_depend>fastrtps_cmake_module</build_export_depend>
  <build_export_depend>rcpputils</build_export_depend>
  <build_export_depend>rcutils</build_export_depend>
  <build_export_depend>rmw</build_export_depend>
  <build_export_depend>rmw_dds_common</build_export_depend>
  <build_export_depend>rosidl_typesupport_introspection_c</build_export_depend>
  <build_export_depend>rosidl_typesupport_introspection_cpp</build_export_depend>
  <build_export_depend>tracetools</build_export_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>osrf_testing_tools_cpp</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
