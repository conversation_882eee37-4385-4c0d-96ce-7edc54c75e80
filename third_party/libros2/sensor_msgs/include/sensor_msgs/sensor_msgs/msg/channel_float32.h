// generated from rosidl_generator_c/resource/idl.h.em
// with input from sensor_msgs:msg/ChannelFloat32.idl
// generated code does not contain a copyright notice

#ifndef SENSOR_MSGS__MSG__CHANNEL_FLOAT32_H_
#define SENSOR_MSGS__MSG__CHANNEL_FLOAT32_H_

#include "sensor_msgs/msg/detail/channel_float32__struct.h"
#include "sensor_msgs/msg/detail/channel_float32__functions.h"
#include "sensor_msgs/msg/detail/channel_float32__type_support.h"

#endif  // SENSOR_MSGS__MSG__CHANNEL_FLOAT32_H_
