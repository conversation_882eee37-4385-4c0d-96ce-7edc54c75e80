// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef SENSOR_MSGS__MSG__CHANNEL_FLOAT32_HPP_
#define SENSOR_MSGS__MSG__CHANNEL_FLOAT32_HPP_

#include "sensor_msgs/msg/detail/channel_float32__struct.hpp"
#include "sensor_msgs/msg/detail/channel_float32__builder.hpp"
#include "sensor_msgs/msg/detail/channel_float32__traits.hpp"
#include "sensor_msgs/msg/detail/channel_float32__type_support.hpp"

#endif  // SENSOR_MSGS__MSG__CHANNEL_FLOAT32_HPP_
