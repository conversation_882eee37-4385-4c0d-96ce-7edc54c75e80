// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from sensor_msgs:msg/Range.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "sensor_msgs/msg/detail/range__struct.h"
#include "sensor_msgs/msg/detail/range__functions.h"

ROSIDL_GENERATOR_C_IMPORT
bool std_msgs__msg__header__convert_from_py(PyObject * _pymsg, void * _ros_message);
ROSIDL_GENERATOR_C_IMPORT
PyObject * std_msgs__msg__header__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool sensor_msgs__msg__range__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[29];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("sensor_msgs.msg._range.Range", full_classname_dest, 28) == 0);
  }
  sensor_msgs__msg__Range * ros_message = _ros_message;
  {  // header
    PyObject * field = PyObject_GetAttrString(_pymsg, "header");
    if (!field) {
      return false;
    }
    if (!std_msgs__msg__header__convert_from_py(field, &ros_message->header)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // radiation_type
    PyObject * field = PyObject_GetAttrString(_pymsg, "radiation_type");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->radiation_type = (uint8_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // field_of_view
    PyObject * field = PyObject_GetAttrString(_pymsg, "field_of_view");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->field_of_view = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // min_range
    PyObject * field = PyObject_GetAttrString(_pymsg, "min_range");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->min_range = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // max_range
    PyObject * field = PyObject_GetAttrString(_pymsg, "max_range");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->max_range = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // range
    PyObject * field = PyObject_GetAttrString(_pymsg, "range");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->range = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * sensor_msgs__msg__range__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of Range */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("sensor_msgs.msg._range");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "Range");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  sensor_msgs__msg__Range * ros_message = (sensor_msgs__msg__Range *)raw_ros_message;
  {  // header
    PyObject * field = NULL;
    field = std_msgs__msg__header__convert_to_py(&ros_message->header);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "header", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // radiation_type
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->radiation_type);
    {
      int rc = PyObject_SetAttrString(_pymessage, "radiation_type", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // field_of_view
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->field_of_view);
    {
      int rc = PyObject_SetAttrString(_pymessage, "field_of_view", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // min_range
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->min_range);
    {
      int rc = PyObject_SetAttrString(_pymessage, "min_range", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // max_range
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->max_range);
    {
      int rc = PyObject_SetAttrString(_pymessage, "max_range", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // range
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->range);
    {
      int rc = PyObject_SetAttrString(_pymessage, "range", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
