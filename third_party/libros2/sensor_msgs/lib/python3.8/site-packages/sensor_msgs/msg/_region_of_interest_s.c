// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from sensor_msgs:msg/RegionOfInterest.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "sensor_msgs/msg/detail/region_of_interest__struct.h"
#include "sensor_msgs/msg/detail/region_of_interest__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool sensor_msgs__msg__region_of_interest__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[53];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("sensor_msgs.msg._region_of_interest.RegionOfInterest", full_classname_dest, 52) == 0);
  }
  sensor_msgs__msg__RegionOfInterest * ros_message = _ros_message;
  {  // x_offset
    PyObject * field = PyObject_GetAttrString(_pymsg, "x_offset");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->x_offset = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // y_offset
    PyObject * field = PyObject_GetAttrString(_pymsg, "y_offset");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->y_offset = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // height
    PyObject * field = PyObject_GetAttrString(_pymsg, "height");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->height = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // width
    PyObject * field = PyObject_GetAttrString(_pymsg, "width");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->width = PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // do_rectify
    PyObject * field = PyObject_GetAttrString(_pymsg, "do_rectify");
    if (!field) {
      return false;
    }
    assert(PyBool_Check(field));
    ros_message->do_rectify = (Py_True == field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * sensor_msgs__msg__region_of_interest__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of RegionOfInterest */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("sensor_msgs.msg._region_of_interest");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "RegionOfInterest");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  sensor_msgs__msg__RegionOfInterest * ros_message = (sensor_msgs__msg__RegionOfInterest *)raw_ros_message;
  {  // x_offset
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->x_offset);
    {
      int rc = PyObject_SetAttrString(_pymessage, "x_offset", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // y_offset
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->y_offset);
    {
      int rc = PyObject_SetAttrString(_pymessage, "y_offset", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // height
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->height);
    {
      int rc = PyObject_SetAttrString(_pymessage, "height", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // width
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->width);
    {
      int rc = PyObject_SetAttrString(_pymessage, "width", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // do_rectify
    PyObject * field = NULL;
    field = PyBool_FromLong(ros_message->do_rectify ? 1 : 0);
    {
      int rc = PyObject_SetAttrString(_pymessage, "do_rectify", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
