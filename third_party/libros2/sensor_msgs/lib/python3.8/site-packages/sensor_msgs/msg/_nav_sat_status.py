# generated from rosidl_generator_py/resource/_idl.py.em
# with input from sensor_msgs:msg/NavSatStatus.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_NavSatStatus(type):
    """Metaclass of message 'NavSatStatus'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
        'STATUS_NO_FIX': -1,
        'STATUS_FIX': 0,
        'STATUS_SBAS_FIX': 1,
        'STATUS_GBAS_FIX': 2,
        'SERVICE_GPS': 1,
        'SERVICE_GLONASS': 2,
        'SERVICE_COMPASS': 4,
        'SERVICE_GALILEO': 8,
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('sensor_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'sensor_msgs.msg.NavSatStatus')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__nav_sat_status
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__nav_sat_status
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__nav_sat_status
            cls._TYPE_SUPPORT = module.type_support_msg__msg__nav_sat_status
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__nav_sat_status

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
            'STATUS_NO_FIX': cls.__constants['STATUS_NO_FIX'],
            'STATUS_FIX': cls.__constants['STATUS_FIX'],
            'STATUS_SBAS_FIX': cls.__constants['STATUS_SBAS_FIX'],
            'STATUS_GBAS_FIX': cls.__constants['STATUS_GBAS_FIX'],
            'SERVICE_GPS': cls.__constants['SERVICE_GPS'],
            'SERVICE_GLONASS': cls.__constants['SERVICE_GLONASS'],
            'SERVICE_COMPASS': cls.__constants['SERVICE_COMPASS'],
            'SERVICE_GALILEO': cls.__constants['SERVICE_GALILEO'],
        }

    @property
    def STATUS_NO_FIX(self):
        """Message constant 'STATUS_NO_FIX'."""
        return Metaclass_NavSatStatus.__constants['STATUS_NO_FIX']

    @property
    def STATUS_FIX(self):
        """Message constant 'STATUS_FIX'."""
        return Metaclass_NavSatStatus.__constants['STATUS_FIX']

    @property
    def STATUS_SBAS_FIX(self):
        """Message constant 'STATUS_SBAS_FIX'."""
        return Metaclass_NavSatStatus.__constants['STATUS_SBAS_FIX']

    @property
    def STATUS_GBAS_FIX(self):
        """Message constant 'STATUS_GBAS_FIX'."""
        return Metaclass_NavSatStatus.__constants['STATUS_GBAS_FIX']

    @property
    def SERVICE_GPS(self):
        """Message constant 'SERVICE_GPS'."""
        return Metaclass_NavSatStatus.__constants['SERVICE_GPS']

    @property
    def SERVICE_GLONASS(self):
        """Message constant 'SERVICE_GLONASS'."""
        return Metaclass_NavSatStatus.__constants['SERVICE_GLONASS']

    @property
    def SERVICE_COMPASS(self):
        """Message constant 'SERVICE_COMPASS'."""
        return Metaclass_NavSatStatus.__constants['SERVICE_COMPASS']

    @property
    def SERVICE_GALILEO(self):
        """Message constant 'SERVICE_GALILEO'."""
        return Metaclass_NavSatStatus.__constants['SERVICE_GALILEO']


class NavSatStatus(metaclass=Metaclass_NavSatStatus):
    """
    Message class 'NavSatStatus'.

    Constants:
      STATUS_NO_FIX
      STATUS_FIX
      STATUS_SBAS_FIX
      STATUS_GBAS_FIX
      SERVICE_GPS
      SERVICE_GLONASS
      SERVICE_COMPASS
      SERVICE_GALILEO
    """

    __slots__ = [
        '_status',
        '_service',
    ]

    _fields_and_field_types = {
        'status': 'int8',
        'service': 'uint16',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint16'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.status = kwargs.get('status', int())
        self.service = kwargs.get('service', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.status != other.status:
            return False
        if self.service != other.service:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def status(self):
        """Message field 'status'."""
        return self._status

    @status.setter
    def status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'status' field must be of type 'int'"
            assert value >= -128 and value < 128, \
                "The 'status' field must be an integer in [-128, 127]"
        self._status = value

    @builtins.property
    def service(self):
        """Message field 'service'."""
        return self._service

    @service.setter
    def service(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'service' field must be of type 'int'"
            assert value >= 0 and value < 65536, \
                "The 'service' field must be an unsigned integer in [0, 65535]"
        self._service = value
