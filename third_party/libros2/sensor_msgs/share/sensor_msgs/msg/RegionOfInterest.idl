// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/RegionOfInterest.msg
// generated code does not contain a copyright notice


module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This message is used to specify a region of interest within an image." "\n"
      "" "\n"
      "When used to specify the ROI setting of the camera when the image was" "\n"
      "taken, the height and width fields should either match the height and" "\n"
      "width fields for the associated image; or height = width = 0" "\n"
      "indicates that the full resolution image was captured.")
    struct RegionOfInterest {
      @verbatim (language="comment", text=
        "Leftmost pixel of the ROI" "\n"
        "(0 if the ROI includes the left edge of the image)")
      uint32 x_offset;

      @verbatim (language="comment", text=
        "Topmost pixel of the ROI" "\n"
        "(0 if the ROI includes the top edge of the image)")
      uint32 y_offset;

      @verbatim (language="comment", text=
        "Height of ROI")
      uint32 height;

      @verbatim (language="comment", text=
        "Width of ROI")
      uint32 width;

      @verbatim (language="comment", text=
        "True if a distinct rectified ROI should be calculated from the \"raw\"" "\n"
        "ROI in this message. Typically this should be False if the full image" "\n"
        "is captured (ROI not used), and True if a subwindow is captured (ROI" "\n"
        "used).")
      boolean do_rectify;
    };
  };
};
