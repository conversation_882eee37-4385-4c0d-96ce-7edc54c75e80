// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/CompressedImage.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This message contains a compressed image.")
    struct CompressedImage {
      @verbatim (language="comment", text=
        "Header timestamp should be acquisition time of image" "\n"
        "Header frame_id should be optical frame of camera" "\n"
        "origin of frame should be optical center of cameara" "\n"
        "+x should point to the right in the image" "\n"
        "+y should point down in the image" "\n"
        "+z should point into to plane of the image")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Specifies the format of the data" "\n"
        "  Acceptable values:" "\n"
        "    jpeg, png, tiff")
      string format;

      @verbatim (language="comment", text=
        "Compressed image buffer")
      sequence<uint8> data;
    };
  };
};
