// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/LaserEcho.msg
// generated code does not contain a copyright notice


module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This message is a submessage of MultiEchoLaserScan and is not intended" "\n"
      "to be used separately.")
    struct LaserEcho {
      @verbatim (language="comment", text=
        "Multiple values of ranges or intensities." "\n"
        "Each array represents data from the same angle increment.")
      sequence<float> echoes;
    };
  };
};
