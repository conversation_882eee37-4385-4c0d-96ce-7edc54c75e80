// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/FluidPressure.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Single pressure reading.  This message is appropriate for measuring the" "\n"
      "pressure inside of a fluid (air, water, etc).  This also includes" "\n"
      "atmospheric or barometric pressure." "\n"
      "" "\n"
      "This message is not appropriate for force/pressure contact sensors.")
    struct FluidPressure {
      @verbatim (language="comment", text=
        "timestamp of the measurement" "\n"
        "frame_id is the location of the pressure sensor")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Absolute pressure reading in Pascals.")
      double fluid_pressure;

      @verbatim (language="comment", text=
        "0 is interpreted as variance unknown")
      double variance;
    };
  };
};
