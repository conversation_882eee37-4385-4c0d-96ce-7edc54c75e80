// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/BatteryState.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sensor_msgs {
  module msg {
    module BatteryState_Constants {
      @verbatim (language="comment", text=
        "Constants are chosen to match the enums in the linux kernel" "\n"        "defined in include/linux/power_supply.h as of version 3.7" "\n"        "The one difference is for style reasons the constants are" "\n"        "all uppercase not mixed case." "\n"        "Power supply status constants")
      const uint8 POWER_SUPPLY_STATUS_UNKNOWN = 0;
      const uint8 POWER_SUPPLY_STATUS_CHARGING = 1;
      const uint8 POWER_SUPPLY_STATUS_DISCHARGING = 2;
      const uint8 POWER_SUPPLY_STATUS_NOT_CHARGING = 3;
      const uint8 POWER_SUPPLY_STATUS_FULL = 4;
      @verbatim (language="comment", text=
        "Power supply health constants")
      const uint8 POWER_SUPPLY_HEALTH_UNKNOWN = 0;
      const uint8 POWER_SUPPLY_HEALTH_GOOD = 1;
      const uint8 POWER_SUPPLY_HEALTH_OVERHEAT = 2;
      const uint8 POWER_SUPPLY_HEALTH_DEAD = 3;
      const uint8 POWER_SUPPLY_HEALTH_OVERVOLTAGE = 4;
      const uint8 POWER_SUPPLY_HEALTH_UNSPEC_FAILURE = 5;
      const uint8 POWER_SUPPLY_HEALTH_COLD = 6;
      const uint8 POWER_SUPPLY_HEALTH_WATCHDOG_TIMER_EXPIRE = 7;
      const uint8 POWER_SUPPLY_HEALTH_SAFETY_TIMER_EXPIRE = 8;
      @verbatim (language="comment", text=
        "Power supply technology (chemistry) constants")
      const uint8 POWER_SUPPLY_TECHNOLOGY_UNKNOWN = 0;
      const uint8 POWER_SUPPLY_TECHNOLOGY_NIMH = 1;
      const uint8 POWER_SUPPLY_TECHNOLOGY_LION = 2;
      const uint8 POWER_SUPPLY_TECHNOLOGY_LIPO = 3;
      const uint8 POWER_SUPPLY_TECHNOLOGY_LIFE = 4;
      const uint8 POWER_SUPPLY_TECHNOLOGY_NICD = 5;
      const uint8 POWER_SUPPLY_TECHNOLOGY_LIMN = 6;
    };
    struct BatteryState {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Voltage in Volts (Mandatory)")
      float voltage;

      @verbatim (language="comment", text=
        "Temperature in Degrees Celsius (If unmeasured NaN)")
      float temperature;

      @verbatim (language="comment", text=
        "Negative when discharging (A)  (If unmeasured NaN)")
      float current;

      @verbatim (language="comment", text=
        "Current charge in Ah  (If unmeasured NaN)")
      float charge;

      @verbatim (language="comment", text=
        "Capacity in Ah (last full capacity)  (If unmeasured NaN)")
      float capacity;

      @verbatim (language="comment", text=
        "Capacity in Ah (design capacity)  (If unmeasured NaN)")
      float design_capacity;

      @verbatim (language="comment", text=
        "Charge percentage on 0 to 1 range  (If unmeasured NaN)")
      float percentage;

      @verbatim (language="comment", text=
        "The charging status as reported. Values defined above")
      uint8 power_supply_status;

      @verbatim (language="comment", text=
        "The battery health metric. Values defined above")
      uint8 power_supply_health;

      @verbatim (language="comment", text=
        "The battery chemistry. Values defined above")
      uint8 power_supply_technology;

      @verbatim (language="comment", text=
        "True if the battery is present")
      boolean present;

      @verbatim (language="comment", text=
        "An array of individual cell voltages for each cell in the pack" "\n"
        "If individual voltages unknown but number of cells known set each to NaN")
      sequence<float> cell_voltage;

      @verbatim (language="comment", text=
        "An array of individual cell temperatures for each cell in the pack" "\n"
        "If individual temperatures unknown but number of cells known set each to NaN")
      sequence<float> cell_temperature;

      @verbatim (language="comment", text=
        "The location into which the battery is inserted. (slot number or plug)")
      string location;

      @verbatim (language="comment", text=
        "The best approximation of the battery serial number")
      string serial_number;
    };
  };
};
