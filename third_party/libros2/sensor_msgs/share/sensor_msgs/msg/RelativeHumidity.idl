// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/RelativeHumidity.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Single reading from a relative humidity sensor." "\n"
      "Defines the ratio of partial pressure of water vapor to the saturated vapor" "\n"
      "pressure at a temperature.")
    struct RelativeHumidity {
      @verbatim (language="comment", text=
        "timestamp of the measurement" "\n"
        "frame_id is the location of the humidity sensor")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Expression of the relative humidity" "\n"
        "from 0.0 to 1.0." "\n"
        "0.0 is no partial pressure of water vapor" "\n"
        "1.0 represents partial pressure of saturation")
      double relative_humidity;

      @verbatim (language="comment", text=
        "0 is interpreted as variance unknown")
      double variance;
    };
  };
};
