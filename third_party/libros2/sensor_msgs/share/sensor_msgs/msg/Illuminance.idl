// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/Illuminance.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Single photometric illuminance measurement.  Light should be assumed to be" "\n"
      "measured along the sensor's x-axis (the area of detection is the y-z plane)." "\n"
      "The illuminance should have a 0 or positive value and be received with" "\n"
      "the sensor's +X axis pointing toward the light source." "\n"
      "" "\n"
      "Photometric illuminance is the measure of the human eye's sensitivity of the" "\n"
      "intensity of light encountering or passing through a surface." "\n"
      "" "\n"
      "All other Photometric and Radiometric measurements should not use this message." "\n"
      "This message cannot represent:" "\n"
      " - Luminous intensity (candela/light source output)" "\n"
      " - Luminance (nits/light output per area)" "\n"
      " - Irradiance (watt/area), etc.")
    struct Illuminance {
      @verbatim (language="comment", text=
        "timestamp is the time the illuminance was measured" "\n"
        "frame_id is the location and direction of the reading")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Measurement of the Photometric Illuminance in Lux.")
      double illuminance;

      @verbatim (language="comment", text=
        "0 is interpreted as variance unknown")
      double variance;
    };
  };
};
