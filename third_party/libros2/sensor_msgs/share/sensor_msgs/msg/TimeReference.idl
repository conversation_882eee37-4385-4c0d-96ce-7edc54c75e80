// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/TimeReference.msg
// generated code does not contain a copyright notice

#include "builtin_interfaces/msg/Time.idl"
#include "std_msgs/msg/Header.idl"

module sensor_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Measurement from an external time source not actively synchronized with the system clock.")
    struct TimeReference {
      @verbatim (language="comment", text=
        "stamp is system time for which measurement was valid" "\n"
        "frame_id is not used")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "corresponding time from this external source")
      builtin_interfaces::msg::Time time_ref;

      @verbatim (language="comment", text=
        "(optional) name of time source")
      string source;
    };
  };
};
