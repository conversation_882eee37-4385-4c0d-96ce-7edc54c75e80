// generated from rosidl_adapter/resource/msg.idl.em
// with input from sensor_msgs/msg/JoyFeedback.msg
// generated code does not contain a copyright notice


module sensor_msgs {
  module msg {
    module JoyFeedback_Constants {
      const uint8 TYPE_LED = 0;
      const uint8 TYPE_RUMBLE = 1;
      const uint8 TYPE_BUZZER = 2;
    };
    @verbatim (language="comment", text=
      "Declare of the type of feedback")
    struct JoyFeedback {
      uint8 type;

      @verbatim (language="comment", text=
        "This will hold an id number for each type of each feedback." "\n"
        "Example, the first led would be id=0, the second would be id=1")
      uint8 id;

      @verbatim (language="comment", text=
        "Intensity of the feedback, from 0.0 to 1.0, inclusive.  If device is" "\n"
        "actually binary, driver should treat 0<=x<0.5 as off, 0.5<=x<=1 as on.")
      float intensity;
    };
  };
};
