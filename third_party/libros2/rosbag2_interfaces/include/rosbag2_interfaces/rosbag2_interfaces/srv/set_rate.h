// generated from rosidl_generator_c/resource/idl.h.em
// with input from rosbag2_interfaces:srv/SetRate.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__SRV__SET_RATE_H_
#define ROSBAG2_INTERFACES__SRV__SET_RATE_H_

#include "rosbag2_interfaces/srv/detail/set_rate__struct.h"
#include "rosbag2_interfaces/srv/detail/set_rate__functions.h"
#include "rosbag2_interfaces/srv/detail/set_rate__type_support.h"

#endif  // ROSBAG2_INTERFACES__SRV__SET_RATE_H_
