// generated from rosidl_generator_c/resource/idl.h.em
// with input from rosbag2_interfaces:srv/Burst.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__SRV__BURST_H_
#define ROSBAG2_INTERFACES__SRV__BURST_H_

#include "rosbag2_interfaces/srv/detail/burst__struct.h"
#include "rosbag2_interfaces/srv/detail/burst__functions.h"
#include "rosbag2_interfaces/srv/detail/burst__type_support.h"

#endif  // ROSBAG2_INTERFACES__SRV__BURST_H_
