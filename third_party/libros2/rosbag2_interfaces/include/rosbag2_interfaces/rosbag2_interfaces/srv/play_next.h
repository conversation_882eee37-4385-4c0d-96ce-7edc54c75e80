// generated from rosidl_generator_c/resource/idl.h.em
// with input from rosbag2_interfaces:srv/PlayNext.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__SRV__PLAY_NEXT_H_
#define ROSBAG2_INTERFACES__SRV__PLAY_NEXT_H_

#include "rosbag2_interfaces/srv/detail/play_next__struct.h"
#include "rosbag2_interfaces/srv/detail/play_next__functions.h"
#include "rosbag2_interfaces/srv/detail/play_next__type_support.h"

#endif  // ROSBAG2_INTERFACES__SRV__PLAY_NEXT_H_
