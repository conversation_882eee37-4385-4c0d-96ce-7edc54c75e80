// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__SRV__SET_RATE_HPP_
#define ROSBAG2_INTERFACES__SRV__SET_RATE_HPP_

#include "rosbag2_interfaces/srv/detail/set_rate__struct.hpp"
#include "rosbag2_interfaces/srv/detail/set_rate__builder.hpp"
#include "rosbag2_interfaces/srv/detail/set_rate__traits.hpp"
#include "rosbag2_interfaces/srv/detail/set_rate__type_support.hpp"

#endif  // ROSBAG2_INTERFACES__SRV__SET_RATE_HPP_
