// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from rosbag2_interfaces:srv/SetRate.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__SRV__DETAIL__SET_RATE__TYPE_SUPPORT_HPP_
#define ROSBAG2_INTERFACES__SRV__DETAIL__SET_RATE__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "rosbag2_interfaces/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/service_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_rosbag2_interfaces
const rosidl_service_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  rosbag2_interfaces,
  srv,
  SetRate
)();
#ifdef __cplusplus
}
#endif

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_rosbag2_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  rosbag2_interfaces,
  srv,
  SetRate_Request
)();
#ifdef __cplusplus
}
#endif

// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_rosbag2_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  rosbag2_interfaces,
  srv,
  SetRate_Response
)();
#ifdef __cplusplus
}
#endif


#endif  // ROSBAG2_INTERFACES__SRV__DETAIL__SET_RATE__TYPE_SUPPORT_HPP_
