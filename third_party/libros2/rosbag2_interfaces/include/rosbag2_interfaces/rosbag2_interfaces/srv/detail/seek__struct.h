// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from rosbag2_interfaces:srv/Seek.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__SRV__DETAIL__SEEK__STRUCT_H_
#define ROSBAG2_INTERFACES__SRV__DETAIL__SEEK__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'time'
#include "builtin_interfaces/msg/detail/time__struct.h"

/// Struct defined in srv/Seek in the package rosbag2_interfaces.
typedef struct rosbag2_interfaces__srv__Seek_Request
{
  builtin_interfaces__msg__Time time;
} rosbag2_interfaces__srv__Seek_Request;

// Struct for a sequence of rosbag2_interfaces__srv__Seek_Request.
typedef struct rosbag2_interfaces__srv__Seek_Request__Sequence
{
  rosbag2_interfaces__srv__Seek_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} rosbag2_interfaces__srv__Seek_Request__Sequence;


// Constants defined in the message

/// Struct defined in srv/Seek in the package rosbag2_interfaces.
typedef struct rosbag2_interfaces__srv__Seek_Response
{
  /// return true if valid time in bag duration, and successful seek
  bool success;
} rosbag2_interfaces__srv__Seek_Response;

// Struct for a sequence of rosbag2_interfaces__srv__Seek_Response.
typedef struct rosbag2_interfaces__srv__Seek_Response__Sequence
{
  rosbag2_interfaces__srv__Seek_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} rosbag2_interfaces__srv__Seek_Response__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // ROSBAG2_INTERFACES__SRV__DETAIL__SEEK__STRUCT_H_
