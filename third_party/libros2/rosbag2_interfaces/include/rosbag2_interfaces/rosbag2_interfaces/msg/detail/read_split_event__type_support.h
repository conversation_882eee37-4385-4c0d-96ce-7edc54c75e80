// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from rosbag2_interfaces:msg/ReadSplitEvent.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__MSG__DETAIL__READ_SPLIT_EVENT__TYPE_SUPPORT_H_
#define ROSBAG2_INTERFACES__MSG__DETAIL__READ_SPLIT_EVENT__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "rosbag2_interfaces/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_rosbag2_interfaces
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  rosbag2_interfaces,
  msg,
  ReadSplitEvent
)();

#ifdef __cplusplus
}
#endif

#endif  // ROSBAG2_INTERFACES__MSG__DETAIL__READ_SPLIT_EVENT__TYPE_SUPPORT_H_
