// generated from rosidl_generator_c/resource/idl.h.em
// with input from rosbag2_interfaces:msg/WriteSplitEvent.idl
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__MSG__WRITE_SPLIT_EVENT_H_
#define ROSBAG2_INTERFACES__MSG__WRITE_SPLIT_EVENT_H_

#include "rosbag2_interfaces/msg/detail/write_split_event__struct.h"
#include "rosbag2_interfaces/msg/detail/write_split_event__functions.h"
#include "rosbag2_interfaces/msg/detail/write_split_event__type_support.h"

#endif  // ROSBAG2_INTERFACES__MSG__WRITE_SPLIT_EVENT_H_
