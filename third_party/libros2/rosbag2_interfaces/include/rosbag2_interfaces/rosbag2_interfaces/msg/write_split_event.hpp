// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef ROSBAG2_INTERFACES__MSG__WRITE_SPLIT_EVENT_HPP_
#define ROSBAG2_INTERFACES__MSG__WRITE_SPLIT_EVENT_HPP_

#include "rosbag2_interfaces/msg/detail/write_split_event__struct.hpp"
#include "rosbag2_interfaces/msg/detail/write_split_event__builder.hpp"
#include "rosbag2_interfaces/msg/detail/write_split_event__traits.hpp"
#include "rosbag2_interfaces/msg/detail/write_split_event__type_support.hpp"

#endif  // ROSBAG2_INTERFACES__MSG__WRITE_SPLIT_EVENT_HPP_
