// generated from rosidl_adapter/resource/msg.idl.em
// with input from rosbag2_interfaces/msg/WriteSplitEvent.msg
// generated code does not contain a copyright notice


module rosbag2_interfaces {
  module msg {
    @verbatim (language="comment", text=
      "The full path of the file that was finished and closed")
    struct WriteSplitEvent {
      string closed_file;

      @verbatim (language="comment", text=
        "The full path of the new file that was created to continue recording")
      string opened_file;
    };
  };
};
