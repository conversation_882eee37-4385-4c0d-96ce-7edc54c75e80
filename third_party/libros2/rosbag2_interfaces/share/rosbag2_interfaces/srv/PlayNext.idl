// generated from rosidl_adapter/resource/srv.idl.em
// with input from rosbag2_interfaces/srv/PlayNext.srv
// generated code does not contain a copyright notice


module rosbag2_interfaces {
  module srv {
    struct PlayNext_Request {
      uint8 structure_needs_at_least_one_member;
    };
    struct PlayNext_Response {
      @verbatim (language="comment", text=
        "can only play-next while playback is paused")
      boolean success;
    };
  };
};
