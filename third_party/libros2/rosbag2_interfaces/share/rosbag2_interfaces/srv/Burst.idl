// generated from rosidl_adapter/resource/srv.idl.em
// with input from rosbag2_interfaces/srv/Burst.srv
// generated code does not contain a copyright notice


module rosbag2_interfaces {
  module srv {
    struct Burst_Request {
      @verbatim (language="comment", text=
        "Number of messages to burst")
      uint64 num_messages;
    };
    struct Burst_Response {
      @verbatim (language="comment", text=
        "Number of messages actually burst")
      uint64 actually_burst;
    };
  };
};
