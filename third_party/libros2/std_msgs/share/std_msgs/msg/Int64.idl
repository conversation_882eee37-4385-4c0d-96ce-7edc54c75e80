// generated from rosidl_adapter/resource/msg.idl.em
// with input from std_msgs/msg/Int64.msg
// generated code does not contain a copyright notice


module std_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This was originally provided as an example message." "\n"
      "It is deprecated as of <PERSON>y" "\n"
      "It is recommended to create your own semantically meaningful message." "\n"
      "However if you would like to continue using this please use the equivalent in example_msgs.")
    struct Int64 {
      int64 data;
    };
  };
};
