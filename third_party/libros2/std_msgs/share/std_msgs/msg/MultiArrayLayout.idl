// generated from rosidl_adapter/resource/msg.idl.em
// with input from std_msgs/msg/MultiArrayLayout.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/MultiArrayDimension.idl"

module std_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This was originally provided as an example message." "\n"
      "It is deprecated as of Foxy" "\n"
      "It is recommended to create your own semantically meaningful message." "\n"
      "However if you would like to continue using this please use the equivalent in example_msgs.")
    struct MultiArrayLayout {
      @verbatim (language="comment", text=
        "The multiarray declares a generic multi-dimensional array of a" "\n"
        "particular data type.  Dimensions are ordered from outer most" "\n"
        "to inner most." "\n"
        "" "\n"
        "Accessors should ALWAYS be written in terms of dimension stride" "\n"
        "and specified outer-most dimension first." "\n"
        "" "\n"
        "multiarray(i,j,k) = data[data_offset + dim_stride[1]*i + dim_stride[2]*j + k]" "\n"
        "" "\n"
        "A standard, 3-channel 640x480 image with interleaved color channels" "\n"
        "would be specified as:" "\n"
        "" "\n"
        "dim[0].label  = \"height\"" "\n"
        "dim[0].size   = 480" "\n"
        "dim[0].stride = 3*640*480 = 921600  (note dim[0] stride is just size of image)" "\n"
        "dim[1].label  = \"width\"" "\n"
        "dim[1].size   = 640" "\n"
        "dim[1].stride = 3*640 = 1920" "\n"
        "dim[2].label  = \"channel\"" "\n"
        "dim[2].size   = 3" "\n"
        "dim[2].stride = 3" "\n"
        "" "\n"
        "multiarray(i,j,k) refers to the ith row, jth column, and kth channel." "\n"
        "Array of dimension properties")
      sequence<std_msgs::msg::MultiArrayDimension> dim;

      @verbatim (language="comment", text=
        "padding bytes at front of data")
      uint32 data_offset;
    };
  };
};
