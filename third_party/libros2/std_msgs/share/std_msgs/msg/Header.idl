// generated from rosidl_adapter/resource/msg.idl.em
// with input from std_msgs/msg/Header.msg
// generated code does not contain a copyright notice

#include "builtin_interfaces/msg/Time.idl"

module std_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Standard metadata for higher-level stamped data types." "\n"
      "This is generally used to communicate timestamped data" "\n"
      "in a particular coordinate frame.")
    struct Header {
      @verbatim (language="comment", text=
        "Two-integer timestamp that is expressed as seconds and nanoseconds.")
      builtin_interfaces::msg::Time stamp;

      @verbatim (language="comment", text=
        "Transform frame with which this data is associated.")
      string frame_id;
    };
  };
};
