# generated from rosidl_cmake/cmake/rosidl_cmake-extras.cmake.in

set(std_msgs_IDL_FILES "msg/Bool.idl;msg/Byte.idl;msg/ByteMultiArray.idl;msg/Char.idl;msg/ColorRGBA.idl;msg/Empty.idl;msg/Float32.idl;msg/Float32MultiArray.idl;msg/Float64.idl;msg/Float64MultiArray.idl;msg/Header.idl;msg/Int16.idl;msg/Int16MultiArray.idl;msg/Int32.idl;msg/Int32MultiArray.idl;msg/Int64.idl;msg/Int64MultiArray.idl;msg/Int8.idl;msg/Int8MultiArray.idl;msg/MultiArrayDimension.idl;msg/MultiArrayLayout.idl;msg/String.idl;msg/UInt16.idl;msg/UInt16MultiArray.idl;msg/UInt32.idl;msg/UInt32MultiArray.idl;msg/UInt64.idl;msg/UInt64MultiArray.idl;msg/UInt8.idl;msg/UInt8MultiArray.idl")
set(std_msgs_INTERFACE_FILES "msg/Bool.msg;msg/Byte.msg;msg/ByteMultiArray.msg;msg/Char.msg;msg/ColorRGBA.msg;msg/Empty.msg;msg/Float32.msg;msg/Float32MultiArray.msg;msg/Float64.msg;msg/Float64MultiArray.msg;msg/Header.msg;msg/Int16.msg;msg/Int16MultiArray.msg;msg/Int32.msg;msg/Int32MultiArray.msg;msg/Int64.msg;msg/Int64MultiArray.msg;msg/Int8.msg;msg/Int8MultiArray.msg;msg/MultiArrayDimension.msg;msg/MultiArrayLayout.msg;msg/String.msg;msg/UInt16.msg;msg/UInt16MultiArray.msg;msg/UInt32.msg;msg/UInt32MultiArray.msg;msg/UInt64.msg;msg/UInt64MultiArray.msg;msg/UInt8.msg;msg/UInt8MultiArray.msg")
