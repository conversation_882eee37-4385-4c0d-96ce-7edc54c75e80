// generated from rosidl_generator_c/resource/idl.h.em
// with input from std_msgs:msg/Int32MultiArray.idl
// generated code does not contain a copyright notice

#ifndef STD_MSGS__MSG__INT32_MULTI_ARRAY_H_
#define STD_MSGS__MSG__INT32_MULTI_ARRAY_H_

#include "std_msgs/msg/detail/int32_multi_array__struct.h"
#include "std_msgs/msg/detail/int32_multi_array__functions.h"
#include "std_msgs/msg/detail/int32_multi_array__type_support.h"

#endif  // STD_MSGS__MSG__INT32_MULTI_ARRAY_H_
