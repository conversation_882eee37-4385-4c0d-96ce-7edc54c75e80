// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef STD_MSGS__MSG__FLOAT32_HPP_
#define STD_MSGS__MSG__FLOAT32_HPP_

#include "std_msgs/msg/detail/float32__struct.hpp"
#include "std_msgs/msg/detail/float32__builder.hpp"
#include "std_msgs/msg/detail/float32__traits.hpp"
#include "std_msgs/msg/detail/float32__type_support.hpp"

#endif  // STD_MSGS__MSG__FLOAT32_HPP_
