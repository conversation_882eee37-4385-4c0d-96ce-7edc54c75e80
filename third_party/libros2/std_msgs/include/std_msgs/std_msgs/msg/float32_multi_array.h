// generated from rosidl_generator_c/resource/idl.h.em
// with input from std_msgs:msg/Float32MultiArray.idl
// generated code does not contain a copyright notice

#ifndef STD_MSGS__MSG__FLOAT32_MULTI_ARRAY_H_
#define STD_MSGS__MSG__FLOAT32_MULTI_ARRAY_H_

#include "std_msgs/msg/detail/float32_multi_array__struct.h"
#include "std_msgs/msg/detail/float32_multi_array__functions.h"
#include "std_msgs/msg/detail/float32_multi_array__type_support.h"

#endif  // STD_MSGS__MSG__FLOAT32_MULTI_ARRAY_H_
