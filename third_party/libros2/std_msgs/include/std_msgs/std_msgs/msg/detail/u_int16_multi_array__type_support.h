// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from std_msgs:msg/UInt16MultiArray.idl
// generated code does not contain a copyright notice

#ifndef STD_MSGS__MSG__DETAIL__U_INT16_MULTI_ARRAY__TYPE_SUPPORT_H_
#define STD_MSGS__MSG__DETAIL__U_INT16_MULTI_ARRAY__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "std_msgs/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_std_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  std_msgs,
  msg,
  UInt16MultiArray
)();

#ifdef __cplusplus
}
#endif

#endif  // STD_MSGS__MSG__DETAIL__U_INT16_MULTI_ARRAY__TYPE_SUPPORT_H_
