// generated from rosidl_generator_c/resource/idl.h.em
// with input from std_msgs:msg/MultiArrayDimension.idl
// generated code does not contain a copyright notice

#ifndef STD_MSGS__MSG__MULTI_ARRAY_DIMENSION_H_
#define STD_MSGS__MSG__MULTI_ARRAY_DIMENSION_H_

#include "std_msgs/msg/detail/multi_array_dimension__struct.h"
#include "std_msgs/msg/detail/multi_array_dimension__functions.h"
#include "std_msgs/msg/detail/multi_array_dimension__type_support.h"

#endif  // STD_MSGS__MSG__MULTI_ARRAY_DIMENSION_H_
