<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rosidl_runtime_cpp</name>
  <version>3.1.6</version>
  <description>Provides definitions and templated functions for getting and working with rosidl typesupport types in C++.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <buildtool_export_depend>ament_cmake</buildtool_export_depend>

  <depend>rosidl_runtime_c</depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>performance_test_fixture</test_depend>

  <member_of_group>rosidl_runtime_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
