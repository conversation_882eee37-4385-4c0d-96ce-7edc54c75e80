// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TRAITS_HPP_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "rscamera_msg/msg/detail/rs_compressed_image__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__traits.hpp"

namespace rscamera_msg
{

namespace msg
{

inline void to_flow_style_yaml(
  const RsCompressedImage & msg,
  std::ostream & out)
{
  out << "{";
  // member: header
  {
    out << "header: ";
    to_flow_style_yaml(msg.header, out);
    out << ", ";
  }

  // member: type
  {
    out << "type: ";
    rosidl_generator_traits::value_to_yaml(msg.type, out);
    out << ", ";
  }

  // member: data
  {
    if (msg.data.size() == 0) {
      out << "data: []";
    } else {
      out << "data: [";
      size_t pending_items = msg.data.size();
      for (auto item : msg.data) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const RsCompressedImage & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: header
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "header:\n";
    to_block_style_yaml(msg.header, out, indentation + 2);
  }

  // member: type
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "type: ";
    rosidl_generator_traits::value_to_yaml(msg.type, out);
    out << "\n";
  }

  // member: data
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.data.size() == 0) {
      out << "data: []\n";
    } else {
      out << "data:\n";
      for (auto item : msg.data) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const RsCompressedImage & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace rscamera_msg

namespace rosidl_generator_traits
{

[[deprecated("use rscamera_msg::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const rscamera_msg::msg::RsCompressedImage & msg,
  std::ostream & out, size_t indentation = 0)
{
  rscamera_msg::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use rscamera_msg::msg::to_yaml() instead")]]
inline std::string to_yaml(const rscamera_msg::msg::RsCompressedImage & msg)
{
  return rscamera_msg::msg::to_yaml(msg);
}

template<>
inline const char * data_type<rscamera_msg::msg::RsCompressedImage>()
{
  return "rscamera_msg::msg::RsCompressedImage";
}

template<>
inline const char * name<rscamera_msg::msg::RsCompressedImage>()
{
  return "rscamera_msg/msg/RsCompressedImage";
}

template<>
struct has_fixed_size<rscamera_msg::msg::RsCompressedImage>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<rscamera_msg::msg::RsCompressedImage>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<rscamera_msg::msg::RsCompressedImage>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TRAITS_HPP_
