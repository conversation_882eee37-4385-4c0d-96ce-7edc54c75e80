// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TYPE_SUPPORT_HPP_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "rscamera_msg/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_rscamera_msg
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  rscamera_msg,
  msg,
  RsCompressedImage
)();
#ifdef __cplusplus
}
#endif

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TYPE_SUPPORT_HPP_
