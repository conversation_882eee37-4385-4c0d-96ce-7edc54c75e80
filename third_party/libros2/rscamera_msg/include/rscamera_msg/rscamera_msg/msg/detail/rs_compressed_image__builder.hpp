// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__BUILDER_HPP_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "rscamera_msg/msg/detail/rs_compressed_image__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace rscamera_msg
{

namespace msg
{

namespace builder
{

class Init_RsCompressedImage_data
{
public:
  explicit Init_RsCompressedImage_data(::rscamera_msg::msg::RsCompressedImage & msg)
  : msg_(msg)
  {}
  ::rscamera_msg::msg::RsCompressedImage data(::rscamera_msg::msg::RsCompressedImage::_data_type arg)
  {
    msg_.data = std::move(arg);
    return std::move(msg_);
  }

private:
  ::rscamera_msg::msg::RsCompressedImage msg_;
};

class Init_RsCompressedImage_type
{
public:
  explicit Init_RsCompressedImage_type(::rscamera_msg::msg::RsCompressedImage & msg)
  : msg_(msg)
  {}
  Init_RsCompressedImage_data type(::rscamera_msg::msg::RsCompressedImage::_type_type arg)
  {
    msg_.type = std::move(arg);
    return Init_RsCompressedImage_data(msg_);
  }

private:
  ::rscamera_msg::msg::RsCompressedImage msg_;
};

class Init_RsCompressedImage_header
{
public:
  Init_RsCompressedImage_header()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_RsCompressedImage_type header(::rscamera_msg::msg::RsCompressedImage::_header_type arg)
  {
    msg_.header = std::move(arg);
    return Init_RsCompressedImage_type(msg_);
  }

private:
  ::rscamera_msg::msg::RsCompressedImage msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::rscamera_msg::msg::RsCompressedImage>()
{
  return rscamera_msg::msg::builder::Init_RsCompressedImage_header();
}

}  // namespace rscamera_msg

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__BUILDER_HPP_
