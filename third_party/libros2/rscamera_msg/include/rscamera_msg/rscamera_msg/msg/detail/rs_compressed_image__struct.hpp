// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__STRUCT_HPP_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__rscamera_msg__msg__RsCompressedImage __attribute__((deprecated))
#else
# define DEPRECATED__rscamera_msg__msg__RsCompressedImage __declspec(deprecated)
#endif

namespace rscamera_msg
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct RsCompressedImage_
{
  using Type = RsCompressedImage_<ContainerAllocator>;

  explicit RsCompressedImage_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->type = 0;
    }
  }

  explicit RsCompressedImage_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : header(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->type = 0;
    }
  }

  // field types and members
  using _header_type =
    std_msgs::msg::Header_<ContainerAllocator>;
  _header_type header;
  using _type_type =
    uint8_t;
  _type_type type;
  using _data_type =
    std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>>;
  _data_type data;

  // setters for named parameter idiom
  Type & set__header(
    const std_msgs::msg::Header_<ContainerAllocator> & _arg)
  {
    this->header = _arg;
    return *this;
  }
  Type & set__type(
    const uint8_t & _arg)
  {
    this->type = _arg;
    return *this;
  }
  Type & set__data(
    const std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> & _arg)
  {
    this->data = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    rscamera_msg::msg::RsCompressedImage_<ContainerAllocator> *;
  using ConstRawPtr =
    const rscamera_msg::msg::RsCompressedImage_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      rscamera_msg::msg::RsCompressedImage_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      rscamera_msg::msg::RsCompressedImage_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__rscamera_msg__msg__RsCompressedImage
    std::shared_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__rscamera_msg__msg__RsCompressedImage
    std::shared_ptr<rscamera_msg::msg::RsCompressedImage_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const RsCompressedImage_ & other) const
  {
    if (this->header != other.header) {
      return false;
    }
    if (this->type != other.type) {
      return false;
    }
    if (this->data != other.data) {
      return false;
    }
    return true;
  }
  bool operator!=(const RsCompressedImage_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct RsCompressedImage_

// alias to use template instance with default allocator
using RsCompressedImage =
  rscamera_msg::msg::RsCompressedImage_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace rscamera_msg

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__STRUCT_HPP_
