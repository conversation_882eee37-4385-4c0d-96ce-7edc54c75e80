// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice
#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "rscamera_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_rscamera_msg
size_t get_serialized_size_rscamera_msg__msg__RsCompressedImage(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_rscamera_msg
size_t max_serialized_size_rscamera_msg__msg__RsCompressedImage(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_rscamera_msg
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, rscamera_msg, msg, RsCompressedImage)();

#ifdef __cplusplus
}
#endif

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
