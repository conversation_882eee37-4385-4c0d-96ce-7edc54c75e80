// generated from rosidl_generator_c/resource/idl__functions.h.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__FUNCTIONS_H_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__FUNCTIONS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stdlib.h>

#include "rosidl_runtime_c/visibility_control.h"
#include "rscamera_msg/msg/rosidl_generator_c__visibility_control.h"

#include "rscamera_msg/msg/detail/rs_compressed_image__struct.h"

/// Initialize msg/RsCompressedImage message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * rscamera_msg__msg__RsCompressedImage
 * )) before or use
 * rscamera_msg__msg__RsCompressedImage__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
bool
rscamera_msg__msg__RsCompressedImage__init(rscamera_msg__msg__RsCompressedImage * msg);

/// Finalize msg/RsCompressedImage message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
void
rscamera_msg__msg__RsCompressedImage__fini(rscamera_msg__msg__RsCompressedImage * msg);

/// Create msg/RsCompressedImage message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * rscamera_msg__msg__RsCompressedImage__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
rscamera_msg__msg__RsCompressedImage *
rscamera_msg__msg__RsCompressedImage__create();

/// Destroy msg/RsCompressedImage message.
/**
 * It calls
 * rscamera_msg__msg__RsCompressedImage__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
void
rscamera_msg__msg__RsCompressedImage__destroy(rscamera_msg__msg__RsCompressedImage * msg);

/// Check for msg/RsCompressedImage message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
bool
rscamera_msg__msg__RsCompressedImage__are_equal(const rscamera_msg__msg__RsCompressedImage * lhs, const rscamera_msg__msg__RsCompressedImage * rhs);

/// Copy a msg/RsCompressedImage message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
bool
rscamera_msg__msg__RsCompressedImage__copy(
  const rscamera_msg__msg__RsCompressedImage * input,
  rscamera_msg__msg__RsCompressedImage * output);

/// Initialize array of msg/RsCompressedImage messages.
/**
 * It allocates the memory for the number of elements and calls
 * rscamera_msg__msg__RsCompressedImage__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
bool
rscamera_msg__msg__RsCompressedImage__Sequence__init(rscamera_msg__msg__RsCompressedImage__Sequence * array, size_t size);

/// Finalize array of msg/RsCompressedImage messages.
/**
 * It calls
 * rscamera_msg__msg__RsCompressedImage__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
void
rscamera_msg__msg__RsCompressedImage__Sequence__fini(rscamera_msg__msg__RsCompressedImage__Sequence * array);

/// Create array of msg/RsCompressedImage messages.
/**
 * It allocates the memory for the array and calls
 * rscamera_msg__msg__RsCompressedImage__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
rscamera_msg__msg__RsCompressedImage__Sequence *
rscamera_msg__msg__RsCompressedImage__Sequence__create(size_t size);

/// Destroy array of msg/RsCompressedImage messages.
/**
 * It calls
 * rscamera_msg__msg__RsCompressedImage__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
void
rscamera_msg__msg__RsCompressedImage__Sequence__destroy(rscamera_msg__msg__RsCompressedImage__Sequence * array);

/// Check for msg/RsCompressedImage message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
bool
rscamera_msg__msg__RsCompressedImage__Sequence__are_equal(const rscamera_msg__msg__RsCompressedImage__Sequence * lhs, const rscamera_msg__msg__RsCompressedImage__Sequence * rhs);

/// Copy an array of msg/RsCompressedImage messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
bool
rscamera_msg__msg__RsCompressedImage__Sequence__copy(
  const rscamera_msg__msg__RsCompressedImage__Sequence * input,
  rscamera_msg__msg__RsCompressedImage__Sequence * output);

#ifdef __cplusplus
}
#endif

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__FUNCTIONS_H_
