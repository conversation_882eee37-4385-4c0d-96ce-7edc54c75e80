// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TYPE_SUPPORT_H_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "rscamera_msg/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_rscamera_msg
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  rscamera_msg,
  msg,
  RsCompressedImage
)();

#ifdef __cplusplus
}
#endif

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__TYPE_SUPPORT_H_
