// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#ifndef RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__STRUCT_H_
#define RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'header'
#include "std_msgs/msg/detail/header__struct.h"
// Member 'data'
#include "rosidl_runtime_c/primitives_sequence.h"

/// Struct defined in msg/RsCompressedImage in the package rscamera_msg.
typedef struct rscamera_msg__msg__RsCompressedImage
{
  std_msgs__msg__Header header;
  uint8_t type;
  rosidl_runtime_c__uint8__Sequence data;
} rscamera_msg__msg__RsCompressedImage;

// Struct for a sequence of rscamera_msg__msg__RsCompressedImage.
typedef struct rscamera_msg__msg__RsCompressedImage__Sequence
{
  rscamera_msg__msg__RsCompressedImage * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} rscamera_msg__msg__RsCompressedImage__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // RSCAMERA_MSG__MSG__DETAIL__RS_COMPRESSED_IMAGE__STRUCT_H_
