// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice
#include "rscamera_msg/msg/detail/rs_compressed_image__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/detail/header__functions.h"
// Member `data`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

bool
rscamera_msg__msg__RsCompressedImage__init(rscamera_msg__msg__RsCompressedImage * msg)
{
  if (!msg) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__init(&msg->header)) {
    rscamera_msg__msg__RsCompressedImage__fini(msg);
    return false;
  }
  // type
  // data
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->data, 0)) {
    rscamera_msg__msg__RsCompressedImage__fini(msg);
    return false;
  }
  return true;
}

void
rscamera_msg__msg__RsCompressedImage__fini(rscamera_msg__msg__RsCompressedImage * msg)
{
  if (!msg) {
    return;
  }
  // header
  std_msgs__msg__Header__fini(&msg->header);
  // type
  // data
  rosidl_runtime_c__uint8__Sequence__fini(&msg->data);
}

bool
rscamera_msg__msg__RsCompressedImage__are_equal(const rscamera_msg__msg__RsCompressedImage * lhs, const rscamera_msg__msg__RsCompressedImage * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__are_equal(
      &(lhs->header), &(rhs->header)))
  {
    return false;
  }
  // type
  if (lhs->type != rhs->type) {
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->data), &(rhs->data)))
  {
    return false;
  }
  return true;
}

bool
rscamera_msg__msg__RsCompressedImage__copy(
  const rscamera_msg__msg__RsCompressedImage * input,
  rscamera_msg__msg__RsCompressedImage * output)
{
  if (!input || !output) {
    return false;
  }
  // header
  if (!std_msgs__msg__Header__copy(
      &(input->header), &(output->header)))
  {
    return false;
  }
  // type
  output->type = input->type;
  // data
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->data), &(output->data)))
  {
    return false;
  }
  return true;
}

rscamera_msg__msg__RsCompressedImage *
rscamera_msg__msg__RsCompressedImage__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  rscamera_msg__msg__RsCompressedImage * msg = (rscamera_msg__msg__RsCompressedImage *)allocator.allocate(sizeof(rscamera_msg__msg__RsCompressedImage), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(rscamera_msg__msg__RsCompressedImage));
  bool success = rscamera_msg__msg__RsCompressedImage__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
rscamera_msg__msg__RsCompressedImage__destroy(rscamera_msg__msg__RsCompressedImage * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    rscamera_msg__msg__RsCompressedImage__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
rscamera_msg__msg__RsCompressedImage__Sequence__init(rscamera_msg__msg__RsCompressedImage__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  rscamera_msg__msg__RsCompressedImage * data = NULL;

  if (size) {
    data = (rscamera_msg__msg__RsCompressedImage *)allocator.zero_allocate(size, sizeof(rscamera_msg__msg__RsCompressedImage), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = rscamera_msg__msg__RsCompressedImage__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        rscamera_msg__msg__RsCompressedImage__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
rscamera_msg__msg__RsCompressedImage__Sequence__fini(rscamera_msg__msg__RsCompressedImage__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      rscamera_msg__msg__RsCompressedImage__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

rscamera_msg__msg__RsCompressedImage__Sequence *
rscamera_msg__msg__RsCompressedImage__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  rscamera_msg__msg__RsCompressedImage__Sequence * array = (rscamera_msg__msg__RsCompressedImage__Sequence *)allocator.allocate(sizeof(rscamera_msg__msg__RsCompressedImage__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = rscamera_msg__msg__RsCompressedImage__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
rscamera_msg__msg__RsCompressedImage__Sequence__destroy(rscamera_msg__msg__RsCompressedImage__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    rscamera_msg__msg__RsCompressedImage__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
rscamera_msg__msg__RsCompressedImage__Sequence__are_equal(const rscamera_msg__msg__RsCompressedImage__Sequence * lhs, const rscamera_msg__msg__RsCompressedImage__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!rscamera_msg__msg__RsCompressedImage__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
rscamera_msg__msg__RsCompressedImage__Sequence__copy(
  const rscamera_msg__msg__RsCompressedImage__Sequence * input,
  rscamera_msg__msg__RsCompressedImage__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(rscamera_msg__msg__RsCompressedImage);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    rscamera_msg__msg__RsCompressedImage * data =
      (rscamera_msg__msg__RsCompressedImage *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!rscamera_msg__msg__RsCompressedImage__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          rscamera_msg__msg__RsCompressedImage__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!rscamera_msg__msg__RsCompressedImage__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
