// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from rscamera_msg:msg/RsCompressedImage.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "rscamera_msg/msg/detail/rs_compressed_image__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace rscamera_msg
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void RsCompressedImage_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) rscamera_msg::msg::RsCompressedImage(_init);
}

void RsCompressedImage_fini_function(void * message_memory)
{
  auto typed_message = static_cast<rscamera_msg::msg::RsCompressedImage *>(message_memory);
  typed_message->~RsCompressedImage();
}

size_t size_function__RsCompressedImage__data(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<uint8_t> *>(untyped_member);
  return member->size();
}

const void * get_const_function__RsCompressedImage__data(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<uint8_t> *>(untyped_member);
  return &member[index];
}

void * get_function__RsCompressedImage__data(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<uint8_t> *>(untyped_member);
  return &member[index];
}

void fetch_function__RsCompressedImage__data(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const uint8_t *>(
    get_const_function__RsCompressedImage__data(untyped_member, index));
  auto & value = *reinterpret_cast<uint8_t *>(untyped_value);
  value = item;
}

void assign_function__RsCompressedImage__data(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<uint8_t *>(
    get_function__RsCompressedImage__data(untyped_member, index));
  const auto & value = *reinterpret_cast<const uint8_t *>(untyped_value);
  item = value;
}

void resize_function__RsCompressedImage__data(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<uint8_t> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember RsCompressedImage_message_member_array[3] = {
  {
    "header",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<std_msgs::msg::Header>(),  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(rscamera_msg::msg::RsCompressedImage, header),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "type",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(rscamera_msg::msg::RsCompressedImage, type),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "data",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(rscamera_msg::msg::RsCompressedImage, data),  // bytes offset in struct
    nullptr,  // default value
    size_function__RsCompressedImage__data,  // size() function pointer
    get_const_function__RsCompressedImage__data,  // get_const(index) function pointer
    get_function__RsCompressedImage__data,  // get(index) function pointer
    fetch_function__RsCompressedImage__data,  // fetch(index, &value) function pointer
    assign_function__RsCompressedImage__data,  // assign(index, value) function pointer
    resize_function__RsCompressedImage__data  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers RsCompressedImage_message_members = {
  "rscamera_msg::msg",  // message namespace
  "RsCompressedImage",  // message name
  3,  // number of fields
  sizeof(rscamera_msg::msg::RsCompressedImage),
  RsCompressedImage_message_member_array,  // message members
  RsCompressedImage_init_function,  // function to initialize message memory (memory has to be allocated)
  RsCompressedImage_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t RsCompressedImage_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &RsCompressedImage_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace rscamera_msg


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<rscamera_msg::msg::RsCompressedImage>()
{
  return &::rscamera_msg::msg::rosidl_typesupport_introspection_cpp::RsCompressedImage_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, rscamera_msg, msg, RsCompressedImage)() {
  return &::rscamera_msg::msg::rosidl_typesupport_introspection_cpp::RsCompressedImage_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
