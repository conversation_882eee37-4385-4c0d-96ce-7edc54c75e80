<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rosidl_typesupport_cpp</name>
  <version>2.0.2</version>
  <description>Generate the type support for C++ messages.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>

  <buildtool_export_depend>python3</buildtool_export_depend>

  <depend>rcutils</depend>
  <depend>rcpputils</depend>
  <build_depend>rosidl_runtime_c</build_depend>
  <build_depend>rosidl_typesupport_c</build_depend>
  <!--
  <PERSON> does not support group_depend so entries below duplicate the group rosidl_typesupport_cpp_packages.
  This ensures that binary packages have support for all of these rmw impl. enabled.
  -->
  <build_depend>rosidl_typesupport_introspection_cpp</build_depend>
  <!-- end of group rosidl_typesupport_cpp_packages for bloom -->

  <buildtool_export_depend>ament_cmake_core</buildtool_export_depend>
  <build_export_depend>rosidl_typesupport_c</build_export_depend>
  <build_export_depend>rosidl_runtime_c</build_export_depend>
  <build_export_depend>rosidl_runtime_cpp</build_export_depend>

  <exec_depend>ament_index_python</exec_depend>
  <exec_depend>rosidl_cli</exec_depend>
  <exec_depend>rosidl_typesupport_interface</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>performance_test_fixture</test_depend>

  <group_depend>rosidl_typesupport_cpp_packages</group_depend>

  <member_of_group>rosidl_runtime_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
