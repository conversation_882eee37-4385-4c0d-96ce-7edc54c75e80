// Copyright 2016 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef ROSIDL_TYPESUPPORT_CPP__TYPE_SUPPORT_MAP_H_
#define ROSIDL_TYPESUPPORT_CPP__TYPE_SUPPORT_MAP_H_

// Keep this file not to break API. It was no more than
// a copy of rosidl_typesupport_c/type_support_map.h

#warning rosidl_typesupport_cpp/type_support_map.h header is deprecated \
  in favor of rosidl_typesupport_c/type_support_map.h.

#include "rosidl_typesupport_c/type_support_map.h"

#endif  // ROSIDL_TYPESUPPORT_CPP__TYPE_SUPPORT_MAP_H_
