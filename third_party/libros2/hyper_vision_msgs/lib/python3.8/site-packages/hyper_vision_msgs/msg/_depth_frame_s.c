// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hyper_vision_msgs:msg/DepthFrame.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hyper_vision_msgs/msg/detail/depth_frame__struct.h"
#include "hyper_vision_msgs/msg/detail/depth_frame__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "hyper_vision_msgs/msg/detail/depth_frame_point__functions.h"
// end nested array functions include
bool hyper_vision_msgs__msg__depth_frame_point__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hyper_vision_msgs__msg__depth_frame_point__convert_to_py(void * raw_ros_message);
bool hyper_vision_msgs__msg__frame_timeval__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hyper_vision_msgs__msg__frame_timeval__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hyper_vision_msgs__msg__depth_frame__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[46];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hyper_vision_msgs.msg._depth_frame.DepthFrame", full_classname_dest, 45) == 0);
  }
  hyper_vision_msgs__msg__DepthFrame * ros_message = _ros_message;
  {  // points
    PyObject * field = PyObject_GetAttrString(_pymsg, "points");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'points'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = PySequence_Size(field);
    if (-1 == size) {
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    if (!hyper_vision_msgs__msg__DepthFramePoint__Sequence__init(&(ros_message->points), size)) {
      PyErr_SetString(PyExc_RuntimeError, "unable to create hyper_vision_msgs__msg__DepthFramePoint__Sequence ros_message");
      Py_DECREF(seq_field);
      Py_DECREF(field);
      return false;
    }
    hyper_vision_msgs__msg__DepthFramePoint * dest = ros_message->points.data;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!hyper_vision_msgs__msg__depth_frame_point__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }
  {  // points_nums
    PyObject * field = PyObject_GetAttrString(_pymsg, "points_nums");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->points_nums = (uint16_t)PyLong_AsUnsignedLong(field);
    Py_DECREF(field);
  }
  {  // capture_time
    PyObject * field = PyObject_GetAttrString(_pymsg, "capture_time");
    if (!field) {
      return false;
    }
    if (!hyper_vision_msgs__msg__frame_timeval__convert_from_py(field, &ros_message->capture_time)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hyper_vision_msgs__msg__depth_frame__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of DepthFrame */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hyper_vision_msgs.msg._depth_frame");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "DepthFrame");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hyper_vision_msgs__msg__DepthFrame * ros_message = (hyper_vision_msgs__msg__DepthFrame *)raw_ros_message;
  {  // points
    PyObject * field = NULL;
    size_t size = ros_message->points.size;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    hyper_vision_msgs__msg__DepthFramePoint * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->points.data[i]);
      PyObject * pyitem = hyper_vision_msgs__msg__depth_frame_point__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "points", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // points_nums
    PyObject * field = NULL;
    field = PyLong_FromUnsignedLong(ros_message->points_nums);
    {
      int rc = PyObject_SetAttrString(_pymessage, "points_nums", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // capture_time
    PyObject * field = NULL;
    field = hyper_vision_msgs__msg__frame_timeval__convert_to_py(&ros_message->capture_time);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "capture_time", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
