// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from hyper_vision_msgs:msg/MotionFrame.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "hyper_vision_msgs/msg/detail/motion_frame__struct.h"
#include "hyper_vision_msgs/msg/detail/motion_frame__functions.h"

bool hyper_vision_msgs__msg__motion_frame_xyz__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hyper_vision_msgs__msg__motion_frame_xyz__convert_to_py(void * raw_ros_message);
bool hyper_vision_msgs__msg__motion_frame_xyz__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hyper_vision_msgs__msg__motion_frame_xyz__convert_to_py(void * raw_ros_message);
bool hyper_vision_msgs__msg__frame_timeval__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * hyper_vision_msgs__msg__frame_timeval__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool hyper_vision_msgs__msg__motion_frame__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[48];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("hyper_vision_msgs.msg._motion_frame.MotionFrame", full_classname_dest, 47) == 0);
  }
  hyper_vision_msgs__msg__MotionFrame * ros_message = _ros_message;
  {  // accel
    PyObject * field = PyObject_GetAttrString(_pymsg, "accel");
    if (!field) {
      return false;
    }
    if (!hyper_vision_msgs__msg__motion_frame_xyz__convert_from_py(field, &ros_message->accel)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // gyro
    PyObject * field = PyObject_GetAttrString(_pymsg, "gyro");
    if (!field) {
      return false;
    }
    if (!hyper_vision_msgs__msg__motion_frame_xyz__convert_from_py(field, &ros_message->gyro)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }
  {  // temperature
    PyObject * field = PyObject_GetAttrString(_pymsg, "temperature");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->temperature = (float)PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // capture_time
    PyObject * field = PyObject_GetAttrString(_pymsg, "capture_time");
    if (!field) {
      return false;
    }
    if (!hyper_vision_msgs__msg__frame_timeval__convert_from_py(field, &ros_message->capture_time)) {
      Py_DECREF(field);
      return false;
    }
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * hyper_vision_msgs__msg__motion_frame__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of MotionFrame */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("hyper_vision_msgs.msg._motion_frame");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "MotionFrame");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  hyper_vision_msgs__msg__MotionFrame * ros_message = (hyper_vision_msgs__msg__MotionFrame *)raw_ros_message;
  {  // accel
    PyObject * field = NULL;
    field = hyper_vision_msgs__msg__motion_frame_xyz__convert_to_py(&ros_message->accel);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "accel", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gyro
    PyObject * field = NULL;
    field = hyper_vision_msgs__msg__motion_frame_xyz__convert_to_py(&ros_message->gyro);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "gyro", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // temperature
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->temperature);
    {
      int rc = PyObject_SetAttrString(_pymessage, "temperature", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // capture_time
    PyObject * field = NULL;
    field = hyper_vision_msgs__msg__frame_timeval__convert_to_py(&ros_message->capture_time);
    if (!field) {
      return NULL;
    }
    {
      int rc = PyObject_SetAttrString(_pymessage, "capture_time", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
