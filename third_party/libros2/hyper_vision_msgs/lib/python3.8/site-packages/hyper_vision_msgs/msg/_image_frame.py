# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hyper_vision_msgs:msg/ImageFrame.idl
# generated code does not contain a copyright notice


# Import statements for member types

# Member 'data'
import array  # noqa: E402, I100

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_ImageFrame(type):
    """Metaclass of message 'ImageFrame'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
        'FRAME_FORMAT_ANY': 1,
        'FRAME_FORMAT_H265': 2,
        'FRAME_FORMAT_RGB': 3,
        'FRAME_FORMAT_NV12': 4,
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hyper_vision_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hyper_vision_msgs.msg.ImageFrame')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__image_frame
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__image_frame
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__image_frame
            cls._TYPE_SUPPORT = module.type_support_msg__msg__image_frame
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__image_frame

            from hyper_vision_msgs.msg import FrameTimeval
            if FrameTimeval.__class__._TYPE_SUPPORT is None:
                FrameTimeval.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
            'FRAME_FORMAT_ANY': cls.__constants['FRAME_FORMAT_ANY'],
            'FRAME_FORMAT_H265': cls.__constants['FRAME_FORMAT_H265'],
            'FRAME_FORMAT_RGB': cls.__constants['FRAME_FORMAT_RGB'],
            'FRAME_FORMAT_NV12': cls.__constants['FRAME_FORMAT_NV12'],
        }

    @property
    def FRAME_FORMAT_ANY(self):
        """Message constant 'FRAME_FORMAT_ANY'."""
        return Metaclass_ImageFrame.__constants['FRAME_FORMAT_ANY']

    @property
    def FRAME_FORMAT_H265(self):
        """Message constant 'FRAME_FORMAT_H265'."""
        return Metaclass_ImageFrame.__constants['FRAME_FORMAT_H265']

    @property
    def FRAME_FORMAT_RGB(self):
        """Message constant 'FRAME_FORMAT_RGB'."""
        return Metaclass_ImageFrame.__constants['FRAME_FORMAT_RGB']

    @property
    def FRAME_FORMAT_NV12(self):
        """Message constant 'FRAME_FORMAT_NV12'."""
        return Metaclass_ImageFrame.__constants['FRAME_FORMAT_NV12']


class ImageFrame(metaclass=Metaclass_ImageFrame):
    """
    Message class 'ImageFrame'.

    Constants:
      FRAME_FORMAT_ANY
      FRAME_FORMAT_H265
      FRAME_FORMAT_RGB
      FRAME_FORMAT_NV12
    """

    __slots__ = [
        '_data',
        '_data_bytes',
        '_width',
        '_height',
        '_frame_format',
        '_step',
        '_sequence',
        '_capture_time',
    ]

    _fields_and_field_types = {
        'data': 'sequence<uint8>',
        'data_bytes': 'uint32',
        'width': 'uint32',
        'height': 'uint32',
        'frame_format': 'uint8',
        'step': 'uint32',
        'sequence': 'uint32',
        'capture_time': 'hyper_vision_msgs/FrameTimeval',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.UnboundedSequence(rosidl_parser.definition.BasicType('uint8')),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint8'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint32'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hyper_vision_msgs', 'msg'], 'FrameTimeval'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.data = array.array('B', kwargs.get('data', []))
        self.data_bytes = kwargs.get('data_bytes', int())
        self.width = kwargs.get('width', int())
        self.height = kwargs.get('height', int())
        self.frame_format = kwargs.get('frame_format', int())
        self.step = kwargs.get('step', int())
        self.sequence = kwargs.get('sequence', int())
        from hyper_vision_msgs.msg import FrameTimeval
        self.capture_time = kwargs.get('capture_time', FrameTimeval())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.data != other.data:
            return False
        if self.data_bytes != other.data_bytes:
            return False
        if self.width != other.width:
            return False
        if self.height != other.height:
            return False
        if self.frame_format != other.frame_format:
            return False
        if self.step != other.step:
            return False
        if self.sequence != other.sequence:
            return False
        if self.capture_time != other.capture_time:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def data(self):
        """Message field 'data'."""
        return self._data

    @data.setter
    def data(self, value):
        if isinstance(value, array.array):
            assert value.typecode == 'B', \
                "The 'data' array.array() must have the type code of 'B'"
            self._data = value
            return
        if __debug__:
            from collections.abc import Sequence
            from collections.abc import Set
            from collections import UserList
            from collections import UserString
            assert \
                ((isinstance(value, Sequence) or
                  isinstance(value, Set) or
                  isinstance(value, UserList)) and
                 not isinstance(value, str) and
                 not isinstance(value, UserString) and
                 all(isinstance(v, int) for v in value) and
                 all(val >= 0 and val < 256 for val in value)), \
                "The 'data' field must be a set or sequence and each value of type 'int' and each unsigned integer in [0, 255]"
        self._data = array.array('B', value)

    @builtins.property
    def data_bytes(self):
        """Message field 'data_bytes'."""
        return self._data_bytes

    @data_bytes.setter
    def data_bytes(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'data_bytes' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'data_bytes' field must be an unsigned integer in [0, 4294967295]"
        self._data_bytes = value

    @builtins.property
    def width(self):
        """Message field 'width'."""
        return self._width

    @width.setter
    def width(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'width' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'width' field must be an unsigned integer in [0, 4294967295]"
        self._width = value

    @builtins.property
    def height(self):
        """Message field 'height'."""
        return self._height

    @height.setter
    def height(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'height' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'height' field must be an unsigned integer in [0, 4294967295]"
        self._height = value

    @builtins.property
    def frame_format(self):
        """Message field 'frame_format'."""
        return self._frame_format

    @frame_format.setter
    def frame_format(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'frame_format' field must be of type 'int'"
            assert value >= 0 and value < 256, \
                "The 'frame_format' field must be an unsigned integer in [0, 255]"
        self._frame_format = value

    @builtins.property
    def step(self):
        """Message field 'step'."""
        return self._step

    @step.setter
    def step(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'step' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'step' field must be an unsigned integer in [0, 4294967295]"
        self._step = value

    @builtins.property
    def sequence(self):
        """Message field 'sequence'."""
        return self._sequence

    @sequence.setter
    def sequence(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'sequence' field must be of type 'int'"
            assert value >= 0 and value < 4294967296, \
                "The 'sequence' field must be an unsigned integer in [0, 4294967295]"
        self._sequence = value

    @builtins.property
    def capture_time(self):
        """Message field 'capture_time'."""
        return self._capture_time

    @capture_time.setter
    def capture_time(self, value):
        if __debug__:
            from hyper_vision_msgs.msg import FrameTimeval
            assert \
                isinstance(value, FrameTimeval), \
                "The 'capture_time' field must be a sub message of type 'FrameTimeval'"
        self._capture_time = value
