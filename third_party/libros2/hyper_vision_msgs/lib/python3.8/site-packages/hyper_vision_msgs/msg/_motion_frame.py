# generated from rosidl_generator_py/resource/_idl.py.em
# with input from hyper_vision_msgs:msg/MotionFrame.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_MotionFrame(type):
    """Metaclass of message 'MotionFrame'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('hyper_vision_msgs')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'hyper_vision_msgs.msg.MotionFrame')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__motion_frame
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__motion_frame
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__motion_frame
            cls._TYPE_SUPPORT = module.type_support_msg__msg__motion_frame
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__motion_frame

            from hyper_vision_msgs.msg import FrameTimeval
            if FrameTimeval.__class__._TYPE_SUPPORT is None:
                FrameTimeval.__class__.__import_type_support__()

            from hyper_vision_msgs.msg import MotionFrameXyz
            if MotionFrameXyz.__class__._TYPE_SUPPORT is None:
                MotionFrameXyz.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class MotionFrame(metaclass=Metaclass_MotionFrame):
    """Message class 'MotionFrame'."""

    __slots__ = [
        '_accel',
        '_gyro',
        '_temperature',
        '_capture_time',
    ]

    _fields_and_field_types = {
        'accel': 'hyper_vision_msgs/MotionFrameXyz',
        'gyro': 'hyper_vision_msgs/MotionFrameXyz',
        'temperature': 'float',
        'capture_time': 'hyper_vision_msgs/FrameTimeval',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['hyper_vision_msgs', 'msg'], 'MotionFrameXyz'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hyper_vision_msgs', 'msg'], 'MotionFrameXyz'),  # noqa: E501
        rosidl_parser.definition.BasicType('float'),  # noqa: E501
        rosidl_parser.definition.NamespacedType(['hyper_vision_msgs', 'msg'], 'FrameTimeval'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from hyper_vision_msgs.msg import MotionFrameXyz
        self.accel = kwargs.get('accel', MotionFrameXyz())
        from hyper_vision_msgs.msg import MotionFrameXyz
        self.gyro = kwargs.get('gyro', MotionFrameXyz())
        self.temperature = kwargs.get('temperature', float())
        from hyper_vision_msgs.msg import FrameTimeval
        self.capture_time = kwargs.get('capture_time', FrameTimeval())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.accel != other.accel:
            return False
        if self.gyro != other.gyro:
            return False
        if self.temperature != other.temperature:
            return False
        if self.capture_time != other.capture_time:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def accel(self):
        """Message field 'accel'."""
        return self._accel

    @accel.setter
    def accel(self, value):
        if __debug__:
            from hyper_vision_msgs.msg import MotionFrameXyz
            assert \
                isinstance(value, MotionFrameXyz), \
                "The 'accel' field must be a sub message of type 'MotionFrameXyz'"
        self._accel = value

    @builtins.property
    def gyro(self):
        """Message field 'gyro'."""
        return self._gyro

    @gyro.setter
    def gyro(self, value):
        if __debug__:
            from hyper_vision_msgs.msg import MotionFrameXyz
            assert \
                isinstance(value, MotionFrameXyz), \
                "The 'gyro' field must be a sub message of type 'MotionFrameXyz'"
        self._gyro = value

    @builtins.property
    def temperature(self):
        """Message field 'temperature'."""
        return self._temperature

    @temperature.setter
    def temperature(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'temperature' field must be of type 'float'"
            assert not (value < -3.402823466e+38 or value > 3.402823466e+38) or math.isinf(value), \
                "The 'temperature' field must be a float in [-3.402823466e+38, 3.402823466e+38]"
        self._temperature = value

    @builtins.property
    def capture_time(self):
        """Message field 'capture_time'."""
        return self._capture_time

    @capture_time.setter
    def capture_time(self, value):
        if __debug__:
            from hyper_vision_msgs.msg import FrameTimeval
            assert \
                isinstance(value, FrameTimeval), \
                "The 'capture_time' field must be a sub message of type 'FrameTimeval'"
        self._capture_time = value
