// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from hyper_vision_msgs:msg/DepthFrame.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "hyper_vision_msgs/msg/detail/depth_frame__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace hyper_vision_msgs
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void DepthFrame_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) hyper_vision_msgs::msg::DepthFrame(_init);
}

void DepthFrame_fini_function(void * message_memory)
{
  auto typed_message = static_cast<hyper_vision_msgs::msg::DepthFrame *>(message_memory);
  typed_message->~DepthFrame();
}

size_t size_function__DepthFrame__points(const void * untyped_member)
{
  const auto * member = reinterpret_cast<const std::vector<hyper_vision_msgs::msg::DepthFramePoint> *>(untyped_member);
  return member->size();
}

const void * get_const_function__DepthFrame__points(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::vector<hyper_vision_msgs::msg::DepthFramePoint> *>(untyped_member);
  return &member[index];
}

void * get_function__DepthFrame__points(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::vector<hyper_vision_msgs::msg::DepthFramePoint> *>(untyped_member);
  return &member[index];
}

void fetch_function__DepthFrame__points(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const hyper_vision_msgs::msg::DepthFramePoint *>(
    get_const_function__DepthFrame__points(untyped_member, index));
  auto & value = *reinterpret_cast<hyper_vision_msgs::msg::DepthFramePoint *>(untyped_value);
  value = item;
}

void assign_function__DepthFrame__points(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<hyper_vision_msgs::msg::DepthFramePoint *>(
    get_function__DepthFrame__points(untyped_member, index));
  const auto & value = *reinterpret_cast<const hyper_vision_msgs::msg::DepthFramePoint *>(untyped_value);
  item = value;
}

void resize_function__DepthFrame__points(void * untyped_member, size_t size)
{
  auto * member =
    reinterpret_cast<std::vector<hyper_vision_msgs::msg::DepthFramePoint> *>(untyped_member);
  member->resize(size);
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember DepthFrame_message_member_array[3] = {
  {
    "points",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<hyper_vision_msgs::msg::DepthFramePoint>(),  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs::msg::DepthFrame, points),  // bytes offset in struct
    nullptr,  // default value
    size_function__DepthFrame__points,  // size() function pointer
    get_const_function__DepthFrame__points,  // get_const(index) function pointer
    get_function__DepthFrame__points,  // get(index) function pointer
    fetch_function__DepthFrame__points,  // fetch(index, &value) function pointer
    assign_function__DepthFrame__points,  // assign(index, value) function pointer
    resize_function__DepthFrame__points  // resize(index) function pointer
  },
  {
    "points_nums",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs::msg::DepthFrame, points_nums),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "capture_time",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<hyper_vision_msgs::msg::FrameTimeval>(),  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs::msg::DepthFrame, capture_time),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers DepthFrame_message_members = {
  "hyper_vision_msgs::msg",  // message namespace
  "DepthFrame",  // message name
  3,  // number of fields
  sizeof(hyper_vision_msgs::msg::DepthFrame),
  DepthFrame_message_member_array,  // message members
  DepthFrame_init_function,  // function to initialize message memory (memory has to be allocated)
  DepthFrame_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t DepthFrame_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &DepthFrame_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace hyper_vision_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<hyper_vision_msgs::msg::DepthFrame>()
{
  return &::hyper_vision_msgs::msg::rosidl_typesupport_introspection_cpp::DepthFrame_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, hyper_vision_msgs, msg, DepthFrame)() {
  return &::hyper_vision_msgs::msg::rosidl_typesupport_introspection_cpp::DepthFrame_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
