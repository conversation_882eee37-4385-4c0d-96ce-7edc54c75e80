// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hyper_vision_msgs:msg/FrameTimeval.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__STRUCT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/FrameTimeval in the package hyper_vision_msgs.
typedef struct hyper_vision_msgs__msg__FrameTimeval
{
  uint32_t tv_sec;
  uint32_t tv_usec;
} hyper_vision_msgs__msg__FrameTimeval;

// Struct for a sequence of hyper_vision_msgs__msg__FrameTimeval.
typedef struct hyper_vision_msgs__msg__FrameTimeval__Sequence
{
  hyper_vision_msgs__msg__FrameTimeval * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hyper_vision_msgs__msg__FrameTimeval__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__STRUCT_H_
