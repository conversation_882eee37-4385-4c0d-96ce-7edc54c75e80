// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hyper_vision_msgs:msg/DepthFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__TRAITS_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hyper_vision_msgs/msg/detail/depth_frame__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'points'
#include "hyper_vision_msgs/msg/detail/depth_frame_point__traits.hpp"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__traits.hpp"

namespace hyper_vision_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const DepthFrame & msg,
  std::ostream & out)
{
  out << "{";
  // member: points
  {
    if (msg.points.size() == 0) {
      out << "points: []";
    } else {
      out << "points: [";
      size_t pending_items = msg.points.size();
      for (auto item : msg.points) {
        to_flow_style_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: points_nums
  {
    out << "points_nums: ";
    rosidl_generator_traits::value_to_yaml(msg.points_nums, out);
    out << ", ";
  }

  // member: capture_time
  {
    out << "capture_time: ";
    to_flow_style_yaml(msg.capture_time, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const DepthFrame & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: points
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.points.size() == 0) {
      out << "points: []\n";
    } else {
      out << "points:\n";
      for (auto item : msg.points) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "-\n";
        to_block_style_yaml(item, out, indentation + 2);
      }
    }
  }

  // member: points_nums
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "points_nums: ";
    rosidl_generator_traits::value_to_yaml(msg.points_nums, out);
    out << "\n";
  }

  // member: capture_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "capture_time:\n";
    to_block_style_yaml(msg.capture_time, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const DepthFrame & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hyper_vision_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hyper_vision_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hyper_vision_msgs::msg::DepthFrame & msg,
  std::ostream & out, size_t indentation = 0)
{
  hyper_vision_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hyper_vision_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hyper_vision_msgs::msg::DepthFrame & msg)
{
  return hyper_vision_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hyper_vision_msgs::msg::DepthFrame>()
{
  return "hyper_vision_msgs::msg::DepthFrame";
}

template<>
inline const char * name<hyper_vision_msgs::msg::DepthFrame>()
{
  return "hyper_vision_msgs/msg/DepthFrame";
}

template<>
struct has_fixed_size<hyper_vision_msgs::msg::DepthFrame>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<hyper_vision_msgs::msg::DepthFrame>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<hyper_vision_msgs::msg::DepthFrame>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__TRAITS_HPP_
