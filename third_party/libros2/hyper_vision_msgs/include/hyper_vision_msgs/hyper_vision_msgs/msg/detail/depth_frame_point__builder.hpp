// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hyper_vision_msgs:msg/DepthFramePoint.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__BUILDER_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hyper_vision_msgs/msg/detail/depth_frame_point__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hyper_vision_msgs
{

namespace msg
{

namespace builder
{

class Init_DepthFramePoint_timestamp
{
public:
  explicit Init_DepthFramePoint_timestamp(::hyper_vision_msgs::msg::DepthFramePoint & msg)
  : msg_(msg)
  {}
  ::hyper_vision_msgs::msg::DepthFramePoint timestamp(::hyper_vision_msgs::msg::DepthFramePoint::_timestamp_type arg)
  {
    msg_.timestamp = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFramePoint msg_;
};

class Init_DepthFramePoint_ring
{
public:
  explicit Init_DepthFramePoint_ring(::hyper_vision_msgs::msg::DepthFramePoint & msg)
  : msg_(msg)
  {}
  Init_DepthFramePoint_timestamp ring(::hyper_vision_msgs::msg::DepthFramePoint::_ring_type arg)
  {
    msg_.ring = std::move(arg);
    return Init_DepthFramePoint_timestamp(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFramePoint msg_;
};

class Init_DepthFramePoint_intensity
{
public:
  explicit Init_DepthFramePoint_intensity(::hyper_vision_msgs::msg::DepthFramePoint & msg)
  : msg_(msg)
  {}
  Init_DepthFramePoint_ring intensity(::hyper_vision_msgs::msg::DepthFramePoint::_intensity_type arg)
  {
    msg_.intensity = std::move(arg);
    return Init_DepthFramePoint_ring(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFramePoint msg_;
};

class Init_DepthFramePoint_z
{
public:
  explicit Init_DepthFramePoint_z(::hyper_vision_msgs::msg::DepthFramePoint & msg)
  : msg_(msg)
  {}
  Init_DepthFramePoint_intensity z(::hyper_vision_msgs::msg::DepthFramePoint::_z_type arg)
  {
    msg_.z = std::move(arg);
    return Init_DepthFramePoint_intensity(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFramePoint msg_;
};

class Init_DepthFramePoint_y
{
public:
  explicit Init_DepthFramePoint_y(::hyper_vision_msgs::msg::DepthFramePoint & msg)
  : msg_(msg)
  {}
  Init_DepthFramePoint_z y(::hyper_vision_msgs::msg::DepthFramePoint::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_DepthFramePoint_z(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFramePoint msg_;
};

class Init_DepthFramePoint_x
{
public:
  Init_DepthFramePoint_x()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_DepthFramePoint_y x(::hyper_vision_msgs::msg::DepthFramePoint::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_DepthFramePoint_y(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFramePoint msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hyper_vision_msgs::msg::DepthFramePoint>()
{
  return hyper_vision_msgs::msg::builder::Init_DepthFramePoint_x();
}

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__BUILDER_HPP_
