// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from hyper_vision_msgs:msg/ImageFrame.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "hyper_vision_msgs/msg/detail/image_frame__rosidl_typesupport_introspection_c.h"
#include "hyper_vision_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "hyper_vision_msgs/msg/detail/image_frame__functions.h"
#include "hyper_vision_msgs/msg/detail/image_frame__struct.h"


// Include directives for member types
// Member `data`
#include "rosidl_runtime_c/primitives_sequence_functions.h"
// Member `capture_time`
#include "hyper_vision_msgs/msg/frame_timeval.h"
// Member `capture_time`
#include "hyper_vision_msgs/msg/detail/frame_timeval__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  hyper_vision_msgs__msg__ImageFrame__init(message_memory);
}

void hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_fini_function(void * message_memory)
{
  hyper_vision_msgs__msg__ImageFrame__fini(message_memory);
}

size_t hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__size_function__ImageFrame__data(
  const void * untyped_member)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return member->size;
}

const void * hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__get_const_function__ImageFrame__data(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__uint8__Sequence * member =
    (const rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void * hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__get_function__ImageFrame__data(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  return &member->data[index];
}

void hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__fetch_function__ImageFrame__data(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const uint8_t * item =
    ((const uint8_t *)
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__get_const_function__ImageFrame__data(untyped_member, index));
  uint8_t * value =
    (uint8_t *)(untyped_value);
  *value = *item;
}

void hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__assign_function__ImageFrame__data(
  void * untyped_member, size_t index, const void * untyped_value)
{
  uint8_t * item =
    ((uint8_t *)
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__get_function__ImageFrame__data(untyped_member, index));
  const uint8_t * value =
    (const uint8_t *)(untyped_value);
  *item = *value;
}

bool hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__resize_function__ImageFrame__data(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__uint8__Sequence * member =
    (rosidl_runtime_c__uint8__Sequence *)(untyped_member);
  rosidl_runtime_c__uint8__Sequence__fini(member);
  return rosidl_runtime_c__uint8__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_member_array[8] = {
  {
    "data",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, data),  // bytes offset in struct
    NULL,  // default value
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__size_function__ImageFrame__data,  // size() function pointer
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__get_const_function__ImageFrame__data,  // get_const(index) function pointer
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__get_function__ImageFrame__data,  // get(index) function pointer
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__fetch_function__ImageFrame__data,  // fetch(index, &value) function pointer
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__assign_function__ImageFrame__data,  // assign(index, value) function pointer
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__resize_function__ImageFrame__data  // resize(index) function pointer
  },
  {
    "data_bytes",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, data_bytes),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "width",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, width),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "height",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, height),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "frame_format",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, frame_format),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "step",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, step),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "sequence",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, sequence),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "capture_time",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__ImageFrame, capture_time),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_members = {
  "hyper_vision_msgs__msg",  // message namespace
  "ImageFrame",  // message name
  8,  // number of fields
  sizeof(hyper_vision_msgs__msg__ImageFrame),
  hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_member_array,  // message members
  hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_init_function,  // function to initialize message memory (memory has to be allocated)
  hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_type_support_handle = {
  0,
  &hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_hyper_vision_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hyper_vision_msgs, msg, ImageFrame)() {
  hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_member_array[7].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hyper_vision_msgs, msg, FrameTimeval)();
  if (!hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_type_support_handle.typesupport_identifier) {
    hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &hyper_vision_msgs__msg__ImageFrame__rosidl_typesupport_introspection_c__ImageFrame_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
