// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hyper_vision_msgs:msg/ImageFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__BUILDER_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hyper_vision_msgs/msg/detail/image_frame__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hyper_vision_msgs
{

namespace msg
{

namespace builder
{

class Init_ImageFrame_capture_time
{
public:
  explicit Init_ImageFrame_capture_time(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  ::hyper_vision_msgs::msg::ImageFrame capture_time(::hyper_vision_msgs::msg::ImageFrame::_capture_time_type arg)
  {
    msg_.capture_time = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_sequence
{
public:
  explicit Init_ImageFrame_sequence(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  Init_ImageFrame_capture_time sequence(::hyper_vision_msgs::msg::ImageFrame::_sequence_type arg)
  {
    msg_.sequence = std::move(arg);
    return Init_ImageFrame_capture_time(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_step
{
public:
  explicit Init_ImageFrame_step(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  Init_ImageFrame_sequence step(::hyper_vision_msgs::msg::ImageFrame::_step_type arg)
  {
    msg_.step = std::move(arg);
    return Init_ImageFrame_sequence(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_frame_format
{
public:
  explicit Init_ImageFrame_frame_format(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  Init_ImageFrame_step frame_format(::hyper_vision_msgs::msg::ImageFrame::_frame_format_type arg)
  {
    msg_.frame_format = std::move(arg);
    return Init_ImageFrame_step(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_height
{
public:
  explicit Init_ImageFrame_height(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  Init_ImageFrame_frame_format height(::hyper_vision_msgs::msg::ImageFrame::_height_type arg)
  {
    msg_.height = std::move(arg);
    return Init_ImageFrame_frame_format(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_width
{
public:
  explicit Init_ImageFrame_width(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  Init_ImageFrame_height width(::hyper_vision_msgs::msg::ImageFrame::_width_type arg)
  {
    msg_.width = std::move(arg);
    return Init_ImageFrame_height(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_data_bytes
{
public:
  explicit Init_ImageFrame_data_bytes(::hyper_vision_msgs::msg::ImageFrame & msg)
  : msg_(msg)
  {}
  Init_ImageFrame_width data_bytes(::hyper_vision_msgs::msg::ImageFrame::_data_bytes_type arg)
  {
    msg_.data_bytes = std::move(arg);
    return Init_ImageFrame_width(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

class Init_ImageFrame_data
{
public:
  Init_ImageFrame_data()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_ImageFrame_data_bytes data(::hyper_vision_msgs::msg::ImageFrame::_data_type arg)
  {
    msg_.data = std::move(arg);
    return Init_ImageFrame_data_bytes(msg_);
  }

private:
  ::hyper_vision_msgs::msg::ImageFrame msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hyper_vision_msgs::msg::ImageFrame>()
{
  return hyper_vision_msgs::msg::builder::Init_ImageFrame_data();
}

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__BUILDER_HPP_
