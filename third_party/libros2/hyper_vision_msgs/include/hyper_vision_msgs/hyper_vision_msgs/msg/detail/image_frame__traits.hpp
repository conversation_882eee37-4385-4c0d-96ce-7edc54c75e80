// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hyper_vision_msgs:msg/ImageFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__TRAITS_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hyper_vision_msgs/msg/detail/image_frame__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__traits.hpp"

namespace hyper_vision_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const ImageFrame & msg,
  std::ostream & out)
{
  out << "{";
  // member: data
  {
    if (msg.data.size() == 0) {
      out << "data: []";
    } else {
      out << "data: [";
      size_t pending_items = msg.data.size();
      for (auto item : msg.data) {
        rosidl_generator_traits::value_to_yaml(item, out);
        if (--pending_items > 0) {
          out << ", ";
        }
      }
      out << "]";
    }
    out << ", ";
  }

  // member: data_bytes
  {
    out << "data_bytes: ";
    rosidl_generator_traits::value_to_yaml(msg.data_bytes, out);
    out << ", ";
  }

  // member: width
  {
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << ", ";
  }

  // member: height
  {
    out << "height: ";
    rosidl_generator_traits::value_to_yaml(msg.height, out);
    out << ", ";
  }

  // member: frame_format
  {
    out << "frame_format: ";
    rosidl_generator_traits::value_to_yaml(msg.frame_format, out);
    out << ", ";
  }

  // member: step
  {
    out << "step: ";
    rosidl_generator_traits::value_to_yaml(msg.step, out);
    out << ", ";
  }

  // member: sequence
  {
    out << "sequence: ";
    rosidl_generator_traits::value_to_yaml(msg.sequence, out);
    out << ", ";
  }

  // member: capture_time
  {
    out << "capture_time: ";
    to_flow_style_yaml(msg.capture_time, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const ImageFrame & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: data
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    if (msg.data.size() == 0) {
      out << "data: []\n";
    } else {
      out << "data:\n";
      for (auto item : msg.data) {
        if (indentation > 0) {
          out << std::string(indentation, ' ');
        }
        out << "- ";
        rosidl_generator_traits::value_to_yaml(item, out);
        out << "\n";
      }
    }
  }

  // member: data_bytes
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "data_bytes: ";
    rosidl_generator_traits::value_to_yaml(msg.data_bytes, out);
    out << "\n";
  }

  // member: width
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "width: ";
    rosidl_generator_traits::value_to_yaml(msg.width, out);
    out << "\n";
  }

  // member: height
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "height: ";
    rosidl_generator_traits::value_to_yaml(msg.height, out);
    out << "\n";
  }

  // member: frame_format
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "frame_format: ";
    rosidl_generator_traits::value_to_yaml(msg.frame_format, out);
    out << "\n";
  }

  // member: step
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "step: ";
    rosidl_generator_traits::value_to_yaml(msg.step, out);
    out << "\n";
  }

  // member: sequence
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "sequence: ";
    rosidl_generator_traits::value_to_yaml(msg.sequence, out);
    out << "\n";
  }

  // member: capture_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "capture_time:\n";
    to_block_style_yaml(msg.capture_time, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const ImageFrame & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hyper_vision_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hyper_vision_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hyper_vision_msgs::msg::ImageFrame & msg,
  std::ostream & out, size_t indentation = 0)
{
  hyper_vision_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hyper_vision_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hyper_vision_msgs::msg::ImageFrame & msg)
{
  return hyper_vision_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hyper_vision_msgs::msg::ImageFrame>()
{
  return "hyper_vision_msgs::msg::ImageFrame";
}

template<>
inline const char * name<hyper_vision_msgs::msg::ImageFrame>()
{
  return "hyper_vision_msgs/msg/ImageFrame";
}

template<>
struct has_fixed_size<hyper_vision_msgs::msg::ImageFrame>
  : std::integral_constant<bool, false> {};

template<>
struct has_bounded_size<hyper_vision_msgs::msg::ImageFrame>
  : std::integral_constant<bool, false> {};

template<>
struct is_message<hyper_vision_msgs::msg::ImageFrame>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__TRAITS_HPP_
