// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hyper_vision_msgs:msg/MotionFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__TRAITS_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hyper_vision_msgs/msg/detail/motion_frame__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'accel'
// Member 'gyro'
#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__traits.hpp"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__traits.hpp"

namespace hyper_vision_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const MotionFrame & msg,
  std::ostream & out)
{
  out << "{";
  // member: accel
  {
    out << "accel: ";
    to_flow_style_yaml(msg.accel, out);
    out << ", ";
  }

  // member: gyro
  {
    out << "gyro: ";
    to_flow_style_yaml(msg.gyro, out);
    out << ", ";
  }

  // member: temperature
  {
    out << "temperature: ";
    rosidl_generator_traits::value_to_yaml(msg.temperature, out);
    out << ", ";
  }

  // member: capture_time
  {
    out << "capture_time: ";
    to_flow_style_yaml(msg.capture_time, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const MotionFrame & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: accel
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "accel:\n";
    to_block_style_yaml(msg.accel, out, indentation + 2);
  }

  // member: gyro
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gyro:\n";
    to_block_style_yaml(msg.gyro, out, indentation + 2);
  }

  // member: temperature
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "temperature: ";
    rosidl_generator_traits::value_to_yaml(msg.temperature, out);
    out << "\n";
  }

  // member: capture_time
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "capture_time:\n";
    to_block_style_yaml(msg.capture_time, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const MotionFrame & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hyper_vision_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hyper_vision_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hyper_vision_msgs::msg::MotionFrame & msg,
  std::ostream & out, size_t indentation = 0)
{
  hyper_vision_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hyper_vision_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hyper_vision_msgs::msg::MotionFrame & msg)
{
  return hyper_vision_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hyper_vision_msgs::msg::MotionFrame>()
{
  return "hyper_vision_msgs::msg::MotionFrame";
}

template<>
inline const char * name<hyper_vision_msgs::msg::MotionFrame>()
{
  return "hyper_vision_msgs/msg/MotionFrame";
}

template<>
struct has_fixed_size<hyper_vision_msgs::msg::MotionFrame>
  : std::integral_constant<bool, has_fixed_size<hyper_vision_msgs::msg::FrameTimeval>::value && has_fixed_size<hyper_vision_msgs::msg::MotionFrameXyz>::value> {};

template<>
struct has_bounded_size<hyper_vision_msgs::msg::MotionFrame>
  : std::integral_constant<bool, has_bounded_size<hyper_vision_msgs::msg::FrameTimeval>::value && has_bounded_size<hyper_vision_msgs::msg::MotionFrameXyz>::value> {};

template<>
struct is_message<hyper_vision_msgs::msg::MotionFrame>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__TRAITS_HPP_
