// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hyper_vision_msgs:msg/MotionFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__BUILDER_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hyper_vision_msgs/msg/detail/motion_frame__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hyper_vision_msgs
{

namespace msg
{

namespace builder
{

class Init_MotionFrame_capture_time
{
public:
  explicit Init_MotionFrame_capture_time(::hyper_vision_msgs::msg::MotionFrame & msg)
  : msg_(msg)
  {}
  ::hyper_vision_msgs::msg::MotionFrame capture_time(::hyper_vision_msgs::msg::MotionFrame::_capture_time_type arg)
  {
    msg_.capture_time = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrame msg_;
};

class Init_MotionFrame_temperature
{
public:
  explicit Init_MotionFrame_temperature(::hyper_vision_msgs::msg::MotionFrame & msg)
  : msg_(msg)
  {}
  Init_MotionFrame_capture_time temperature(::hyper_vision_msgs::msg::MotionFrame::_temperature_type arg)
  {
    msg_.temperature = std::move(arg);
    return Init_MotionFrame_capture_time(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrame msg_;
};

class Init_MotionFrame_gyro
{
public:
  explicit Init_MotionFrame_gyro(::hyper_vision_msgs::msg::MotionFrame & msg)
  : msg_(msg)
  {}
  Init_MotionFrame_temperature gyro(::hyper_vision_msgs::msg::MotionFrame::_gyro_type arg)
  {
    msg_.gyro = std::move(arg);
    return Init_MotionFrame_temperature(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrame msg_;
};

class Init_MotionFrame_accel
{
public:
  Init_MotionFrame_accel()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_MotionFrame_gyro accel(::hyper_vision_msgs::msg::MotionFrame::_accel_type arg)
  {
    msg_.accel = std::move(arg);
    return Init_MotionFrame_gyro(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrame msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hyper_vision_msgs::msg::MotionFrame>()
{
  return hyper_vision_msgs::msg::builder::Init_MotionFrame_accel();
}

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__BUILDER_HPP_
