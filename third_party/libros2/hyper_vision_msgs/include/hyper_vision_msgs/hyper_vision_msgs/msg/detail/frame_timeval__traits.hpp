// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hyper_vision_msgs:msg/FrameTimeval.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__TRAITS_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace hyper_vision_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const FrameTimeval & msg,
  std::ostream & out)
{
  out << "{";
  // member: tv_sec
  {
    out << "tv_sec: ";
    rosidl_generator_traits::value_to_yaml(msg.tv_sec, out);
    out << ", ";
  }

  // member: tv_usec
  {
    out << "tv_usec: ";
    rosidl_generator_traits::value_to_yaml(msg.tv_usec, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const FrameTimeval & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: tv_sec
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "tv_sec: ";
    rosidl_generator_traits::value_to_yaml(msg.tv_sec, out);
    out << "\n";
  }

  // member: tv_usec
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "tv_usec: ";
    rosidl_generator_traits::value_to_yaml(msg.tv_usec, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const FrameTimeval & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hyper_vision_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hyper_vision_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hyper_vision_msgs::msg::FrameTimeval & msg,
  std::ostream & out, size_t indentation = 0)
{
  hyper_vision_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hyper_vision_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hyper_vision_msgs::msg::FrameTimeval & msg)
{
  return hyper_vision_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hyper_vision_msgs::msg::FrameTimeval>()
{
  return "hyper_vision_msgs::msg::FrameTimeval";
}

template<>
inline const char * name<hyper_vision_msgs::msg::FrameTimeval>()
{
  return "hyper_vision_msgs/msg/FrameTimeval";
}

template<>
struct has_fixed_size<hyper_vision_msgs::msg::FrameTimeval>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<hyper_vision_msgs::msg::FrameTimeval>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<hyper_vision_msgs::msg::FrameTimeval>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__TRAITS_HPP_
