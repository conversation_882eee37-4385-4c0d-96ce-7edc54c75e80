// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from hyper_vision_msgs:msg/MotionFrameXyz.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__rosidl_typesupport_introspection_c.h"
#include "hyper_vision_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__functions.h"
#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__struct.h"


#ifdef __cplusplus
extern "C"
{
#endif

void hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  hyper_vision_msgs__msg__MotionFrameXyz__init(message_memory);
}

void hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_fini_function(void * message_memory)
{
  hyper_vision_msgs__msg__MotionFrameXyz__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_member_array[3] = {
  {
    "x",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__MotionFrameXyz, x),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "y",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__MotionFrameXyz, y),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "z",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__MotionFrameXyz, z),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_members = {
  "hyper_vision_msgs__msg",  // message namespace
  "MotionFrameXyz",  // message name
  3,  // number of fields
  sizeof(hyper_vision_msgs__msg__MotionFrameXyz),
  hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_member_array,  // message members
  hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_init_function,  // function to initialize message memory (memory has to be allocated)
  hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_type_support_handle = {
  0,
  &hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_hyper_vision_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hyper_vision_msgs, msg, MotionFrameXyz)() {
  if (!hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_type_support_handle.typesupport_identifier) {
    hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &hyper_vision_msgs__msg__MotionFrameXyz__rosidl_typesupport_introspection_c__MotionFrameXyz_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
