// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hyper_vision_msgs:msg/DepthFramePoint.idl
// generated code does not contain a copyright notice
#include "hyper_vision_msgs/msg/detail/depth_frame_point__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
hyper_vision_msgs__msg__DepthFramePoint__init(hyper_vision_msgs__msg__DepthFramePoint * msg)
{
  if (!msg) {
    return false;
  }
  // x
  // y
  // z
  // intensity
  // ring
  // timestamp
  return true;
}

void
hyper_vision_msgs__msg__DepthFramePoint__fini(hyper_vision_msgs__msg__DepthFramePoint * msg)
{
  if (!msg) {
    return;
  }
  // x
  // y
  // z
  // intensity
  // ring
  // timestamp
}

bool
hyper_vision_msgs__msg__DepthFramePoint__are_equal(const hyper_vision_msgs__msg__DepthFramePoint * lhs, const hyper_vision_msgs__msg__DepthFramePoint * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // x
  if (lhs->x != rhs->x) {
    return false;
  }
  // y
  if (lhs->y != rhs->y) {
    return false;
  }
  // z
  if (lhs->z != rhs->z) {
    return false;
  }
  // intensity
  if (lhs->intensity != rhs->intensity) {
    return false;
  }
  // ring
  if (lhs->ring != rhs->ring) {
    return false;
  }
  // timestamp
  if (lhs->timestamp != rhs->timestamp) {
    return false;
  }
  return true;
}

bool
hyper_vision_msgs__msg__DepthFramePoint__copy(
  const hyper_vision_msgs__msg__DepthFramePoint * input,
  hyper_vision_msgs__msg__DepthFramePoint * output)
{
  if (!input || !output) {
    return false;
  }
  // x
  output->x = input->x;
  // y
  output->y = input->y;
  // z
  output->z = input->z;
  // intensity
  output->intensity = input->intensity;
  // ring
  output->ring = input->ring;
  // timestamp
  output->timestamp = input->timestamp;
  return true;
}

hyper_vision_msgs__msg__DepthFramePoint *
hyper_vision_msgs__msg__DepthFramePoint__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__DepthFramePoint * msg = (hyper_vision_msgs__msg__DepthFramePoint *)allocator.allocate(sizeof(hyper_vision_msgs__msg__DepthFramePoint), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hyper_vision_msgs__msg__DepthFramePoint));
  bool success = hyper_vision_msgs__msg__DepthFramePoint__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hyper_vision_msgs__msg__DepthFramePoint__destroy(hyper_vision_msgs__msg__DepthFramePoint * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hyper_vision_msgs__msg__DepthFramePoint__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hyper_vision_msgs__msg__DepthFramePoint__Sequence__init(hyper_vision_msgs__msg__DepthFramePoint__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__DepthFramePoint * data = NULL;

  if (size) {
    data = (hyper_vision_msgs__msg__DepthFramePoint *)allocator.zero_allocate(size, sizeof(hyper_vision_msgs__msg__DepthFramePoint), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hyper_vision_msgs__msg__DepthFramePoint__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hyper_vision_msgs__msg__DepthFramePoint__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hyper_vision_msgs__msg__DepthFramePoint__Sequence__fini(hyper_vision_msgs__msg__DepthFramePoint__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hyper_vision_msgs__msg__DepthFramePoint__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hyper_vision_msgs__msg__DepthFramePoint__Sequence *
hyper_vision_msgs__msg__DepthFramePoint__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__DepthFramePoint__Sequence * array = (hyper_vision_msgs__msg__DepthFramePoint__Sequence *)allocator.allocate(sizeof(hyper_vision_msgs__msg__DepthFramePoint__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hyper_vision_msgs__msg__DepthFramePoint__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hyper_vision_msgs__msg__DepthFramePoint__Sequence__destroy(hyper_vision_msgs__msg__DepthFramePoint__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hyper_vision_msgs__msg__DepthFramePoint__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hyper_vision_msgs__msg__DepthFramePoint__Sequence__are_equal(const hyper_vision_msgs__msg__DepthFramePoint__Sequence * lhs, const hyper_vision_msgs__msg__DepthFramePoint__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hyper_vision_msgs__msg__DepthFramePoint__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hyper_vision_msgs__msg__DepthFramePoint__Sequence__copy(
  const hyper_vision_msgs__msg__DepthFramePoint__Sequence * input,
  hyper_vision_msgs__msg__DepthFramePoint__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hyper_vision_msgs__msg__DepthFramePoint);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hyper_vision_msgs__msg__DepthFramePoint * data =
      (hyper_vision_msgs__msg__DepthFramePoint *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hyper_vision_msgs__msg__DepthFramePoint__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hyper_vision_msgs__msg__DepthFramePoint__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hyper_vision_msgs__msg__DepthFramePoint__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
