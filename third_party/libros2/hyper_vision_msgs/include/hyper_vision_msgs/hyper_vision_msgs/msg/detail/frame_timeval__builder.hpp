// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hyper_vision_msgs:msg/FrameTimeval.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__BUILDER_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hyper_vision_msgs
{

namespace msg
{

namespace builder
{

class Init_FrameTimeval_tv_usec
{
public:
  explicit Init_FrameTimeval_tv_usec(::hyper_vision_msgs::msg::FrameTimeval & msg)
  : msg_(msg)
  {}
  ::hyper_vision_msgs::msg::FrameTimeval tv_usec(::hyper_vision_msgs::msg::FrameTimeval::_tv_usec_type arg)
  {
    msg_.tv_usec = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hyper_vision_msgs::msg::FrameTimeval msg_;
};

class Init_FrameTimeval_tv_sec
{
public:
  Init_FrameTimeval_tv_sec()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_FrameTimeval_tv_usec tv_sec(::hyper_vision_msgs::msg::FrameTimeval::_tv_sec_type arg)
  {
    msg_.tv_sec = std::move(arg);
    return Init_FrameTimeval_tv_usec(msg_);
  }

private:
  ::hyper_vision_msgs::msg::FrameTimeval msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hyper_vision_msgs::msg::FrameTimeval>()
{
  return hyper_vision_msgs::msg::builder::Init_FrameTimeval_tv_sec();
}

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__BUILDER_HPP_
