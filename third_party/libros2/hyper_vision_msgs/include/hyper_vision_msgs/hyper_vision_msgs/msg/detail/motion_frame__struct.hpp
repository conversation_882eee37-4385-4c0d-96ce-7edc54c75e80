// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hyper_vision_msgs:msg/MotionFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__STRUCT_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'accel'
// Member 'gyro'
#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__struct.hpp"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__hyper_vision_msgs__msg__MotionFrame __attribute__((deprecated))
#else
# define DEPRECATED__hyper_vision_msgs__msg__MotionFrame __declspec(deprecated)
#endif

namespace hyper_vision_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct MotionFrame_
{
  using Type = MotionFrame_<ContainerAllocator>;

  explicit MotionFrame_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : accel(_init),
    gyro(_init),
    capture_time(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->temperature = 0.0f;
    }
  }

  explicit MotionFrame_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : accel(_alloc, _init),
    gyro(_alloc, _init),
    capture_time(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->temperature = 0.0f;
    }
  }

  // field types and members
  using _accel_type =
    hyper_vision_msgs::msg::MotionFrameXyz_<ContainerAllocator>;
  _accel_type accel;
  using _gyro_type =
    hyper_vision_msgs::msg::MotionFrameXyz_<ContainerAllocator>;
  _gyro_type gyro;
  using _temperature_type =
    float;
  _temperature_type temperature;
  using _capture_time_type =
    hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>;
  _capture_time_type capture_time;

  // setters for named parameter idiom
  Type & set__accel(
    const hyper_vision_msgs::msg::MotionFrameXyz_<ContainerAllocator> & _arg)
  {
    this->accel = _arg;
    return *this;
  }
  Type & set__gyro(
    const hyper_vision_msgs::msg::MotionFrameXyz_<ContainerAllocator> & _arg)
  {
    this->gyro = _arg;
    return *this;
  }
  Type & set__temperature(
    const float & _arg)
  {
    this->temperature = _arg;
    return *this;
  }
  Type & set__capture_time(
    const hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> & _arg)
  {
    this->capture_time = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator> *;
  using ConstRawPtr =
    const hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hyper_vision_msgs__msg__MotionFrame
    std::shared_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hyper_vision_msgs__msg__MotionFrame
    std::shared_ptr<hyper_vision_msgs::msg::MotionFrame_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const MotionFrame_ & other) const
  {
    if (this->accel != other.accel) {
      return false;
    }
    if (this->gyro != other.gyro) {
      return false;
    }
    if (this->temperature != other.temperature) {
      return false;
    }
    if (this->capture_time != other.capture_time) {
      return false;
    }
    return true;
  }
  bool operator!=(const MotionFrame_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct MotionFrame_

// alias to use template instance with default allocator
using MotionFrame =
  hyper_vision_msgs::msg::MotionFrame_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__STRUCT_HPP_
