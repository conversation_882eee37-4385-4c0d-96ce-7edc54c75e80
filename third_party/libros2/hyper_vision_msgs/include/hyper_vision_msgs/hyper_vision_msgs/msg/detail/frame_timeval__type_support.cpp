// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from hyper_vision_msgs:msg/FrameTimeval.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace hyper_vision_msgs
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void FrameTimeval_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) hyper_vision_msgs::msg::FrameTimeval(_init);
}

void FrameTimeval_fini_function(void * message_memory)
{
  auto typed_message = static_cast<hyper_vision_msgs::msg::FrameTimeval *>(message_memory);
  typed_message->~FrameTimeval();
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember FrameTimeval_message_member_array[2] = {
  {
    "tv_sec",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs::msg::FrameTimeval, tv_sec),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  },
  {
    "tv_usec",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    nullptr,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs::msg::FrameTimeval, tv_usec),  // bytes offset in struct
    nullptr,  // default value
    nullptr,  // size() function pointer
    nullptr,  // get_const(index) function pointer
    nullptr,  // get(index) function pointer
    nullptr,  // fetch(index, &value) function pointer
    nullptr,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers FrameTimeval_message_members = {
  "hyper_vision_msgs::msg",  // message namespace
  "FrameTimeval",  // message name
  2,  // number of fields
  sizeof(hyper_vision_msgs::msg::FrameTimeval),
  FrameTimeval_message_member_array,  // message members
  FrameTimeval_init_function,  // function to initialize message memory (memory has to be allocated)
  FrameTimeval_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t FrameTimeval_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &FrameTimeval_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace hyper_vision_msgs


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<hyper_vision_msgs::msg::FrameTimeval>()
{
  return &::hyper_vision_msgs::msg::rosidl_typesupport_introspection_cpp::FrameTimeval_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, hyper_vision_msgs, msg, FrameTimeval)() {
  return &::hyper_vision_msgs::msg::rosidl_typesupport_introspection_cpp::FrameTimeval_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
