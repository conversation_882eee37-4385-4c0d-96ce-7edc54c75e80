// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hyper_vision_msgs:msg/MotionFrameXyz.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__BUILDER_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hyper_vision_msgs
{

namespace msg
{

namespace builder
{

class Init_MotionFrameXyz_z
{
public:
  explicit Init_MotionFrameXyz_z(::hyper_vision_msgs::msg::MotionFrameXyz & msg)
  : msg_(msg)
  {}
  ::hyper_vision_msgs::msg::MotionFrameXyz z(::hyper_vision_msgs::msg::MotionFrameXyz::_z_type arg)
  {
    msg_.z = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrameXyz msg_;
};

class Init_MotionFrameXyz_y
{
public:
  explicit Init_MotionFrameXyz_y(::hyper_vision_msgs::msg::MotionFrameXyz & msg)
  : msg_(msg)
  {}
  Init_MotionFrameXyz_z y(::hyper_vision_msgs::msg::MotionFrameXyz::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_MotionFrameXyz_z(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrameXyz msg_;
};

class Init_MotionFrameXyz_x
{
public:
  Init_MotionFrameXyz_x()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_MotionFrameXyz_y x(::hyper_vision_msgs::msg::MotionFrameXyz::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_MotionFrameXyz_y(msg_);
  }

private:
  ::hyper_vision_msgs::msg::MotionFrameXyz msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hyper_vision_msgs::msg::MotionFrameXyz>()
{
  return hyper_vision_msgs::msg::builder::Init_MotionFrameXyz_x();
}

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__BUILDER_HPP_
