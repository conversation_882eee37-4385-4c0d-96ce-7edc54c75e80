// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from statistics_msgs:msg/StatisticDataType.idl
// generated code does not contain a copyright notice

#ifndef STATISTICS_MSGS__MSG__DETAIL__STATISTIC_DATA_TYPE__BUILDER_HPP_
#define STATISTICS_MSGS__MSG__DETAIL__STATISTIC_DATA_TYPE__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "statistics_msgs/msg/detail/statistic_data_type__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace statistics_msgs
{

namespace msg
{


}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::statistics_msgs::msg::StatisticDataType>()
{
  return ::statistics_msgs::msg::StatisticDataType(rosidl_runtime_cpp::MessageInitialization::ZERO);
}

}  // namespace statistics_msgs

#endif  // STATISTICS_MSGS__MSG__DETAIL__STATISTIC_DATA_TYPE__BUILDER_HPP_
