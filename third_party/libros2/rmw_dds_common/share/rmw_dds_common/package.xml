<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>rmw_dds_common</name>
  <version>1.6.0</version>
  <description>Define a common interface between DDS implementations of ROS middleware.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>

  <exec_depend>rosidl_default_runtime</exec_depend>

  <depend>rcutils</depend>
  <depend>rcpputils</depend>
  <depend>rmw</depend>
  <depend>rosidl_runtime_cpp</depend>

  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>osrf_testing_tools_cpp</test_depend>
  <test_depend>performance_test_fixture</test_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
