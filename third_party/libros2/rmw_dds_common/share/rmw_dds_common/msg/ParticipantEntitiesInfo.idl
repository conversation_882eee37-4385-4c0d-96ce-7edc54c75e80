// generated from rosidl_adapter/resource/msg.idl.em
// with input from rmw_dds_common/msg/ParticipantEntitiesInfo.msg
// generated code does not contain a copyright notice

#include "rmw_dds_common/msg/Gid.idl"
#include "rmw_dds_common/msg/NodeEntitiesInfo.idl"

module rmw_dds_common {
  module msg {
    struct ParticipantEntitiesInfo {
      rmw_dds_common::msg::Gid gid;

      sequence<rmw_dds_common::msg::NodeEntitiesInfo> node_entities_info_seq;
    };
  };
};
