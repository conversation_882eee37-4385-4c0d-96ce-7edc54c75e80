<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>rosbag2_transport</name>
  <version>0.15.13</version>
  <description>Layer encapsulating ROS middleware to allow rosbag2 to be used with or without middleware</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">ROS Tooling Working Group</maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rosbag2_compression</depend>
  <depend>rosbag2_cpp</depend>
  <depend>rosbag2_interfaces</depend>
  <depend>rosbag2_storage</depend>
  <depend>rmw</depend>
  <depend>shared_queues_vendor</depend>
  <depend>yaml_cpp_vendor</depend>
  <depend>keyboard_handler</depend>

  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>ament_index_cpp</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>rmw_implementation_cmake</test_depend>
  <test_depend>rosbag2_compression_zstd</test_depend>
  <test_depend>rosbag2_test_common</test_depend>
  <test_depend>rosbag2_storage_default_plugins</test_depend>
  <test_depend>test_msgs</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
