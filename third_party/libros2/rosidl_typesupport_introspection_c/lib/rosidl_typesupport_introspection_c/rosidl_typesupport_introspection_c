#!/usr/bin/env python3

import argparse
import sys

from rosidl_typesupport_introspection_c import generate_c
from typing import List


def main(argv: List[str] = sys.argv[1:]):
    """
    Generate the C introspection type support for ROS interfaces.

    :param argv: The command-line arguments to be parsed. Defaults to
        sys.argv[1:].
    :type argv: List[str]
    """
    parser = argparse.ArgumentParser(
        description='Generate the C type support to dynamically handle ROS messages.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument(
        '--generator-arguments-file',
        required=True,
        help='The location of the file containing the generator arguments')
    args = parser.parse_args(argv)

    generate_c(args.generator_arguments_file)


if __name__ == '__main__':
    sys.exit(main())
