// Copyright 2018 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef ROSIDL_RUNTIME_C__STRING_BOUND_H_
#define ROSIDL_RUNTIME_C__STRING_BOUND_H_

#include <stddef.h>

/// Upper boundary for #rosidl_runtime_c__String or #rosidl_runtime_c__U16String.
typedef struct rosidl_runtime_c__String__bound
{
  /// The number of characters in the string (excluding the null character).
  size_t bound;
} rosidl_runtime_c__String__bound;

#endif  // ROSIDL_RUNTIME_C__STRING_BOUND_H_
