// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from geometry_msgs:msg/Twist.idl
// generated code does not contain a copyright notice

#ifndef GEOMETRY_MSGS__MSG__DETAIL__TWIST__TYPE_SUPPORT_HPP_
#define GEOMETRY_MSGS__MSG__DETAIL__TWIST__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "geometry_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_geometry_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  geometry_msgs,
  msg,
  Twist
)();
#ifdef __cplusplus
}
#endif

#endif  // GEOMETRY_MSGS__MSG__DETAIL__TWIST__TYPE_SUPPORT_HPP_
