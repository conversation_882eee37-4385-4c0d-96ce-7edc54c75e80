// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/QuaternionStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Quaternion.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents an orientation with reference coordinate frame and timestamp.")
    struct QuaternionStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::Quaternion quaternion;
    };
  };
};
