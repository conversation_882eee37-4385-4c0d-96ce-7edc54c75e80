// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Accel.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Vector3.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This expresses acceleration in free space broken into its linear and angular parts.")
    struct Accel {
      geometry_msgs::msg::Vector3 linear;

      geometry_msgs::msg::Vector3 angular;
    };
  };
};
