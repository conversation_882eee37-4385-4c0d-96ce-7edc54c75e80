// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Vector3Stamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Vector3.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents a Vector3 with reference coordinate frame and timestamp")
    struct Vector3Stamped {
      @verbatim (language="comment", text=
        "Note that this follows vector semantics with it always anchored at the origin," "\n"
        "so the rotational elements of a transform are the only parts applied when transforming.")
      std_msgs::msg::Header header;

      geometry_msgs::msg::Vector3 vector;
    };
  };
};
