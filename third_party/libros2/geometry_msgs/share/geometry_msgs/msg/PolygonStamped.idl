// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/PolygonStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Polygon.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents a Polygon with reference coordinate frame and timestamp")
    struct PolygonStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::Polygon polygon;
    };
  };
};
