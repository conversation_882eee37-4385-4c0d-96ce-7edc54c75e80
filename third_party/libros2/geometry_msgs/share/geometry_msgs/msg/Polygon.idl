// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Polygon.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Point32.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "A specification of a polygon where the first and last points are assumed to be connected")
    struct Polygon {
      sequence<geometry_msgs::msg::Point32> points;
    };
  };
};
