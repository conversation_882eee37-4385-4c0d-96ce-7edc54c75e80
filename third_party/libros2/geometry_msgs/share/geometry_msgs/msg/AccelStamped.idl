// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/AccelStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Accel.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "An accel with reference coordinate frame and timestamp")
    struct AccelStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::Accel accel;
    };
  };
};
