// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/AccelWithCovarianceStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/AccelWithCovariance.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents an estimated accel with reference coordinate frame and timestamp.")
    struct AccelWithCovarianceStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::AccelWithCovariance accel;
    };
  };
};
