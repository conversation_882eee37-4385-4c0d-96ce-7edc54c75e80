// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/VelocityStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Twist.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This expresses the timestamped velocity vector of a frame 'body_frame_id' in the reference frame 'reference_frame_id' expressed from arbitrary observation frame 'header.frame_id'." "\n"
      "- If the 'body_frame_id' and 'header.frame_id' are identical, the velocity is observed and defined in the local coordinates system of the body" "\n"
      "  which is the usual use-case in mobile robotics and is also known as a body twist.")
    struct VelocityStamped {
      std_msgs::msg::Header header;

      string body_frame_id;

      string reference_frame_id;

      geometry_msgs::msg::Twist velocity;
    };
  };
};
