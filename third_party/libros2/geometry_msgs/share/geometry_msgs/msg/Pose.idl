// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Pose.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Point.idl"
#include "geometry_msgs/msg/Quaternion.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "A representation of pose in free space, composed of position and orientation.")
    struct Pose {
      geometry_msgs::msg::Point position;

      geometry_msgs::msg::Quaternion orientation;
    };
  };
};
