// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Transform.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Quaternion.idl"
#include "geometry_msgs/msg/Vector3.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents the transform between two coordinate frames in free space.")
    struct Transform {
      geometry_msgs::msg::Vector3 translation;

      geometry_msgs::msg::Quaternion rotation;
    };
  };
};
