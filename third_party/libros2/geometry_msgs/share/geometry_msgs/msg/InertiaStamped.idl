// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/InertiaStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Inertia.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "An Inertia with a time stamp and reference frame.")
    struct InertiaStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::Inertia inertia;
    };
  };
};
