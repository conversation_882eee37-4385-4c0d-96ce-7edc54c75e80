// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Wrench.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Vector3.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents force in free space, separated into its linear and angular parts.")
    struct Wrench {
      geometry_msgs::msg::Vector3 force;

      geometry_msgs::msg::Vector3 torque;
    };
  };
};
