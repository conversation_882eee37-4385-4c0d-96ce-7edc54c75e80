// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/Quaternion.msg
// generated code does not contain a copyright notice


module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This represents an orientation in free space in quaternion form.")
    struct Quaternion {
      @default (value=0.0)
      double x;

      @default (value=0.0)
      double y;

      @default (value=0.0)
      double z;

      @default (value=1.0)
      double w;
    };
  };
};
