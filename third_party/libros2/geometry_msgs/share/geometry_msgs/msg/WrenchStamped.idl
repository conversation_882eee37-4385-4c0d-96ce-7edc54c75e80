// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/WrenchStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/Wrench.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "A wrench with reference coordinate frame and timestamp")
    struct WrenchStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::Wrench wrench;
    };
  };
};
