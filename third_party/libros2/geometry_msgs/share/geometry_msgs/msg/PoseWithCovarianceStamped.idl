// generated from rosidl_adapter/resource/msg.idl.em
// with input from geometry_msgs/msg/PoseWithCovarianceStamped.msg
// generated code does not contain a copyright notice

#include "geometry_msgs/msg/PoseWithCovariance.idl"
#include "std_msgs/msg/Header.idl"

module geometry_msgs {
  module msg {
    @verbatim (language="comment", text=
      "This expresses an estimated pose with a reference coordinate frame and timestamp")
    struct PoseWithCovarianceStamped {
      std_msgs::msg::Header header;

      geometry_msgs::msg::PoseWithCovariance pose;
    };
  };
};
