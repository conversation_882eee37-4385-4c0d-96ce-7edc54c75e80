#----------------------------------------------------------------
# Generated CMake target import file.
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "rmw::rmw" for configuration ""
set_property(TARGET rmw::rmw APPEND PROPERTY IMPORTED_CONFIGURATIONS NOCONFIG)
set_target_properties(rmw::rmw PROPERTIES
  IMPORTED_LOCATION_NOCONFIG "${_IMPORT_PREFIX}/lib/librmw.so"
  IMPORTED_SONAME_NOCONFIG "librmw.so"
  )

list(APPEND _IMPORT_CHECK_TARGETS rmw::rmw )
list(APPEND _IMPORT_CHECK_FILES_FOR_rmw::rmw "${_IMPORT_PREFIX}/lib/librmw.so" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
