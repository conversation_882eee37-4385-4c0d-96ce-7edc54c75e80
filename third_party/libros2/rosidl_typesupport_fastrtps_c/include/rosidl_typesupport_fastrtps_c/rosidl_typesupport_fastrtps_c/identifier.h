// Copyright 2016 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef ROSIDL_TYPESUPPORT_FASTRTPS_C__IDENTIFIER_H_
#define ROSIDL_TYPESUPPORT_FASTRTPS_C__IDENTIFIER_H_

#include "rosidl_typesupport_fastrtps_c/visibility_control.h"

#if __cplusplus
extern "C"
{
#endif

/// String identifier specific to rosidl_typesupport_fastrtps_c
ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC
extern const char * rosidl_typesupport_fastrtps_c__identifier;

#if __cplusplus
}
#endif

#endif  // ROSIDL_TYPESUPPORT_FASTRTPS_C__IDENTIFIER_H_
