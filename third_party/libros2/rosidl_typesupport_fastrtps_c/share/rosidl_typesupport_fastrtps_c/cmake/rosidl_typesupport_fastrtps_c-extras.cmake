# generated from
# rosidl_typesupport_fastrtps_c/rosidl_typesupport_fastrtps_c-extras.cmake.in

find_package(fastrtps_cmake_module QUIET)
find_package(fastcdr REQUIRED CONFIG)

if(NOT fastcdr_FOUND)
  message(STATUS
    "Could not find eProsima Fast CDR: skipping rosidl_typesupport_fastrtps_c"
  )
else()
  find_package(ament_cmake_core QUIET REQUIRED)

  # This generator generates code dependent on code generated by rosidl_generator_c
  # Find it first so it registers it's extensions first
  find_package(rosidl_generator_c REQUIRED)

  ament_register_extension(
    "rosidl_generate_idl_interfaces"
    "rosidl_typesupport_fastrtps_c"
    "rosidl_typesupport_fastrtps_c_generate_interfaces.cmake")

  set(rosidl_typesupport_fastrtps_c_BIN
    "${rosidl_typesupport_fastrtps_c_DIR}/../../../lib/rosidl_typesupport_fastrtps_c/rosidl_typesupport_fastrtps_c")
  normalize_path(rosidl_typesupport_fastrtps_c_BIN
    "${rosidl_typesupport_fastrtps_c_BIN}")

  set(rosidl_typesupport_fastrtps_c_GENERATOR_FILES
    "${rosidl_typesupport_fastrtps_c_DIR}/../../../lib/python3.8/site-packages/rosidl_typesupport_fastrtps_c/__init__.py")
  normalize_path(rosidl_typesupport_fastrtps_c_GENERATOR_FILES
    "${rosidl_typesupport_fastrtps_c_GENERATOR_FILES}")

  set(rosidl_typesupport_fastrtps_c_TEMPLATE_DIR
    "${rosidl_typesupport_fastrtps_c_DIR}/../resource")
  normalize_path(rosidl_typesupport_fastrtps_c_TEMPLATE_DIR
    "${rosidl_typesupport_fastrtps_c_TEMPLATE_DIR}")
endif()
