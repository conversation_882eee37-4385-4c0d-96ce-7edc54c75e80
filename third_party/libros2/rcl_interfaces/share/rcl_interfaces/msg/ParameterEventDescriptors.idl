// generated from rosidl_adapter/resource/msg.idl.em
// with input from rcl_interfaces/msg/ParameterEventDescriptors.msg
// generated code does not contain a copyright notice

#include "rcl_interfaces/msg/ParameterDescriptor.idl"

module rcl_interfaces {
  module msg {
    @verbatim (language="comment", text=
      "This message contains descriptors of a parameter event." "\n"
      "It was an atomic update." "\n"
      "A specific parameter name can only be in one of the three sets.")
    struct ParameterEventDescriptors {
      sequence<rcl_interfaces::msg::ParameterDescriptor> new_parameters;

      sequence<rcl_interfaces::msg::ParameterDescriptor> changed_parameters;

      sequence<rcl_interfaces::msg::ParameterDescriptor> deleted_parameters;
    };
  };
};
