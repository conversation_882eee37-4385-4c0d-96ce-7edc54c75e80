// generated from rosidl_adapter/resource/msg.idl.em
// with input from rcl_interfaces/msg/FloatingPointRange.msg
// generated code does not contain a copyright notice


module rcl_interfaces {
  module msg {
    @verbatim (language="comment", text=
      "Represents bounds and a step value for a floating point typed parameter.")
    struct FloatingPointRange {
      @verbatim (language="comment", text=
        "Start value for valid values, inclusive.")
      double from_value;

      @verbatim (language="comment", text=
        "End value for valid values, inclusive.")
      double to_value;

      @verbatim (language="comment", text=
        "Size of valid steps between the from and to bound." "\n"
        "" "\n"
        "Step is considered to be a magnitude, therefore negative values are treated" "\n"
        "the same as positive values, and a step value of zero implies a continuous" "\n"
        "range of values." "\n"
        "" "\n"
        "Ideally, the step would be less than or equal to the distance between the" "\n"
        "bounds, as well as an even multiple of the distance between the bounds, but" "\n"
        "neither are required." "\n"
        "" "\n"
        "If the absolute value of the step is larger than or equal to the distance" "\n"
        "between the two bounds, then the bounds will be the only valid values. e.g. if" "\n"
        "the range is defined as {from_value: 1.0, to_value: 2.0, step: 5.0} then the" "\n"
        "valid values will be 1.0 and 2.0." "\n"
        "" "\n"
        "If the step is less than the distance between the bounds, but the distance is" "\n"
        "not a multiple of the step, then the \"to\" bound will always be a valid value," "\n"
        "e.g. if the range is defined as {from_value: 2.0, to_value: 5.0, step: 2.0}" "\n"
        "then the valid values will be 2.0, 4.0, and 5.0.")
      double step;
    };
  };
};
