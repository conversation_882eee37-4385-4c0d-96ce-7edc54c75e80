// generated from rosidl_adapter/resource/msg.idl.em
// with input from rcl_interfaces/msg/Log.msg
// generated code does not contain a copyright notice

#include "builtin_interfaces/msg/Time.idl"

module rcl_interfaces {
  module msg {
    module Log_Constants {
      @verbatim (language="comment", text=
        "Debug is for pedantic information, which is useful when debugging issues.")
      const octet DEBUG = 10;
      @verbatim (language="comment", text=
        "Info is the standard informational level and is used to report expected" "\n"        "information.")
      const octet INFO = 20;
      @verbatim (language="comment", text=
        "Warning is for information that may potentially cause issues or possibly unexpected" "\n"        "behavior.")
      const octet WARN = 30;
      @verbatim (language="comment", text=
        "Error is for information that this node cannot resolve.")
      const octet ERROR = 40;
      @verbatim (language="comment", text=
        "Information about a impending node shutdown.")
      const octet FATAL = 50;
    };
    @verbatim (language="comment", text=
      "Severity level constants" "\n"
      "" "\n"
      "These logging levels follow the Python Standard" "\n"
      "https://docs.python.org/3/library/logging.html#logging-levels" "\n"
      "And are implemented in rcutils as well" "\n"
      "https://github.com/ros2/rcutils/blob/35f29850064e0c33a4063cbc947ebbfeada11dba/include/rcutils/logging.h#L164-L172" "\n"
      "This leaves space for other standard logging levels to be inserted in the middle in the future," "\n"
      "as well as custom user defined levels." "\n"
      "Since there are several other logging enumeration standard for different implementations," "\n"
      "other logging implementations may need to provide level mappings to match their internal implementations.")
    struct Log {
      @verbatim (language="comment", text=
        "Fields" "\n"
        "" "\n"
        "Timestamp when this message was generated by the node.")
      builtin_interfaces::msg::Time stamp;

      @verbatim (language="comment", text=
        "Corresponding log level, see above definitions.")
      uint8 level;

      @verbatim (language="comment", text=
        "The name representing the logger this message came from.")
      string name;

      @verbatim (language="comment", text=
        "The full log message.")
      string msg;

      @verbatim (language="comment", text=
        "The file the message came from.")
      string file;

      @verbatim (language="comment", text=
        "The function the message came from.")
      string function;

      @verbatim (language="comment", text=
        "The line in the file the message came from.")
      uint32 line;
    };
  };
};
