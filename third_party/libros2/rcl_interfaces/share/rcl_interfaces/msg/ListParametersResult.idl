// generated from rosidl_adapter/resource/msg.idl.em
// with input from rcl_interfaces/msg/ListParametersResult.msg
// generated code does not contain a copyright notice


module rcl_interfaces {
  module msg {
    @verbatim (language="comment", text=
      "The resulting parameters under the given prefixes.")
    struct ListParametersResult {
      sequence<string> names;

      @verbatim (language="comment", text=
        "The resulting prefixes under the given prefixes." "\n"
        "TODO(wjwwood): link to prefix definition and rules.")
      sequence<string> prefixes;
    };
  };
};
