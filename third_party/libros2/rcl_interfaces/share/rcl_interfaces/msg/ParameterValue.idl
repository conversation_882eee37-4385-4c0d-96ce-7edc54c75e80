// generated from rosidl_adapter/resource/msg.idl.em
// with input from rcl_interfaces/msg/ParameterValue.msg
// generated code does not contain a copyright notice


module rcl_interfaces {
  module msg {
    @verbatim (language="comment", text=
      "Used to determine which of the next *_value fields are set." "\n"
      "ParameterType.PARAMETER_NOT_SET indicates that the parameter was not set" "\n"
      "(if gotten) or is uninitialized." "\n"
      "Values are enumerated in `ParameterType.msg`.")
    struct ParameterValue {
      @verbatim (language="comment", text=
        "The type of this parameter, which corresponds to the appropriate field below.")
      uint8 type;

      @verbatim (language="comment", text=
        "\"Variant\" style storage of the parameter value. Only the value corresponding" "\n"
        "the type field will have valid information." "\n"
        "Boolean value, can be either true or false.")
      boolean bool_value;

      @verbatim (language="comment", text=
        "Integer value ranging from -9,223,372,036,854,775,808 to" "\n"
        "9,223,372,036,854,775,807.")
      int64 integer_value;

      @verbatim (language="comment", text=
        "A double precision floating point value following IEEE 754.")
      double double_value;

      @verbatim (language="comment", text=
        "A textual value with no practical length limit.")
      string string_value;

      @verbatim (language="comment", text=
        "An array of bytes, used for non-textual information.")
      sequence<octet> byte_array_value;

      @verbatim (language="comment", text=
        "An array of boolean values.")
      sequence<boolean> bool_array_value;

      @verbatim (language="comment", text=
        "An array of 64-bit integer values.")
      sequence<int64> integer_array_value;

      @verbatim (language="comment", text=
        "An array of 64-bit floating point values.")
      sequence<double> double_array_value;

      @verbatim (language="comment", text=
        "An array of string values.")
      sequence<string> string_array_value;
    };
  };
};
