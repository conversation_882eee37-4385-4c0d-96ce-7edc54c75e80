// generated from rosidl_adapter/resource/msg.idl.em
// with input from rcl_interfaces/msg/IntegerRange.msg
// generated code does not contain a copyright notice


module rcl_interfaces {
  module msg {
    @verbatim (language="comment", text=
      "Represents bounds and a step value for an integer typed parameter.")
    struct IntegerRange {
      @verbatim (language="comment", text=
        "Start value for valid values, inclusive.")
      int64 from_value;

      @verbatim (language="comment", text=
        "End value for valid values, inclusive.")
      int64 to_value;

      @verbatim (language="comment", text=
        "Size of valid steps between the from and to bound." "\n"
        "" "\n"
        "A step value of zero implies a continuous range of values. Ideally, the step" "\n"
        "would be less than or equal to the distance between the bounds, as well as an" "\n"
        "even multiple of the distance between the bounds, but neither are required." "\n"
        "" "\n"
        "If the absolute value of the step is larger than or equal to the distance" "\n"
        "between the two bounds, then the bounds will be the only valid values. e.g. if" "\n"
        "the range is defined as {from_value: 1, to_value: 2, step: 5} then the valid" "\n"
        "values will be 1 and 2." "\n"
        "" "\n"
        "If the step is less than the distance between the bounds, but the distance is" "\n"
        "not a multiple of the step, then the \"to\" bound will always be a valid value," "\n"
        "e.g. if the range is defined as {from_value: 2, to_value: 5, step: 2} then" "\n"
        "the valid values will be 2, 4, and 5.")
      uint64 step;
    };
  };
};
