// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef RCL_INTERFACES__MSG__FLOATING_POINT_RANGE_HPP_
#define RCL_INTERFACES__MSG__FLOATING_POINT_RANGE_HPP_

#include "rcl_interfaces/msg/detail/floating_point_range__struct.hpp"
#include "rcl_interfaces/msg/detail/floating_point_range__builder.hpp"
#include "rcl_interfaces/msg/detail/floating_point_range__traits.hpp"
#include "rcl_interfaces/msg/detail/floating_point_range__type_support.hpp"

#endif  // RCL_INTERFACES__MSG__FLOATING_POINT_RANGE_HPP_
