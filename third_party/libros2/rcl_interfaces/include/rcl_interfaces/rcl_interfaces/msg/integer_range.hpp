// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef RCL_INTERFACES__MSG__INTEGER_RANGE_HPP_
#define RCL_INTERFACES__MSG__INTEGER_RANGE_HPP_

#include "rcl_interfaces/msg/detail/integer_range__struct.hpp"
#include "rcl_interfaces/msg/detail/integer_range__builder.hpp"
#include "rcl_interfaces/msg/detail/integer_range__traits.hpp"
#include "rcl_interfaces/msg/detail/integer_range__type_support.hpp"

#endif  // RCL_INTERFACES__MSG__INTEGER_RANGE_HPP_
