// generated from rosidl_generator_c/resource/idl.h.em
// with input from rcl_interfaces:msg/IntegerRange.idl
// generated code does not contain a copyright notice

#ifndef RCL_INTERFACES__MSG__INTEGER_RANGE_H_
#define RCL_INTERFACES__MSG__INTEGER_RANGE_H_

#include "rcl_interfaces/msg/detail/integer_range__struct.h"
#include "rcl_interfaces/msg/detail/integer_range__functions.h"
#include "rcl_interfaces/msg/detail/integer_range__type_support.h"

#endif  // RCL_INTERFACES__MSG__INTEGER_RANGE_H_
