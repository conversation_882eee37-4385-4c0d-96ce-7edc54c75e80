// generated from rosidl_generator_c/resource/idl.h.em
// with input from rcl_interfaces:srv/SetParametersAtomically.idl
// generated code does not contain a copyright notice

#ifndef RCL_INTERFACES__SRV__SET_PARAMETERS_ATOMICALLY_H_
#define RCL_INTERFACES__SRV__SET_PARAMETERS_ATOMICALLY_H_

#include "rcl_interfaces/srv/detail/set_parameters_atomically__struct.h"
#include "rcl_interfaces/srv/detail/set_parameters_atomically__functions.h"
#include "rcl_interfaces/srv/detail/set_parameters_atomically__type_support.h"

#endif  // RCL_INTERFACES__SRV__SET_PARAMETERS_ATOMICALLY_H_
