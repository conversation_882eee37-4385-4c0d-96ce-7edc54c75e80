<?xml version="1.0" encoding="shift_jis"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="libglog_static"
	ProjectGUID="{772C2111-BBBF-49E6-B912-198A7F7A88E5}"
	RootNamespace="libglog_static"
	TargetFrameworkVersion="196613"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\src\windows"
				PreprocessorDefinitions="GOOGLE_GLOG_DLL_DECL="
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				EnableIntrinsicFunctions="true"
				AdditionalIncludeDirectories="..\..\src\windows"
				PreprocessorDefinitions="GOOGLE_GLOG_DLL_DECL="
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			>
			<File
				RelativePath="..\..\src\logging.cc"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\port.cc"
				>
			</File>
			<File
				RelativePath="..\..\src\raw_logging.cc"
				>
			</File>
			<File
				RelativePath="..\..\src\utilities.cc"
				>
			</File>
			<File
				RelativePath="..\..\src\vlog_is_on.cc"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			>
			<File
				RelativePath="..\..\src\base\commandlineflags.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\config.h"
				>
			</File>
			<File
				RelativePath="..\..\src\base\googleinit.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\glog\log_severity.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\glog\logging.h"
				>
			</File>
			<File
				RelativePath="..\..\src\base\mutex.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\port.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\glog\raw_logging.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\glog\stl_logging.h"
				>
			</File>
			<File
				RelativePath="..\..\src\utilities.h"
				>
			</File>
			<File
				RelativePath="..\..\src\windows\glog\vlog_is_on.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
