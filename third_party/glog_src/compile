#! /bin/sh

# Wrapper for compilers which do not understand `-c -o'.

# Copyright 1999, 2000 Free Software Foundation, Inc.
# Written by <PERSON> <<EMAIL>>.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.

# As a special exception to the GNU General Public License, if you
# distribute this file as part of a program that contains a
# configuration script generated by Autoconf, you may include it under
# the same distribution terms that you use for the rest of that program.

# Usage:
# compile PROGRAM [ARGS]...
# `-o FOO.o' is removed from the args passed to the actual compile.

prog=$1
shift

ofile=
cfile=
args=
while test $# -gt 0; do
   case "$1" in
    -o)
       # configure might choose to run compile as `compile cc -o foo foo.c'.
       # So we do something ugly here.
       ofile=$2
       shift
       case "$ofile" in
	*.o | *.obj)
	   ;;
	*)
	   args="$args -o $ofile"
	   ofile=
	   ;;
       esac
       ;;
    *.c)
       cfile=$1
       args="$args $1"
       ;;
    *)
       args="$args $1"
       ;;
   esac
   shift
done

if test -z "$ofile" || test -z "$cfile"; then
   # If no `-o' option was seen then we might have been invoked from a
   # pattern rule where we don't need one.  That is ok -- this is a
   # normal compilation that the losing compiler can handle.  If no
   # `.c' file was seen then we are probably linking.  That is also
   # ok.
   exec "$prog" $args
fi

# Name of file we expect compiler to create.
cofile=`echo $cfile | sed -e 's|^.*/||' -e 's/\.c$/.o/'`

# Create the lock directory.
# Note: use `[/.-]' here to ensure that we don't use the same name
# that we are using for the .o file.  Also, base the name on the expected
# object file name, since that is what matters with a parallel build.
lockdir=`echo $cofile | sed -e 's|[/.-]|_|g'`.d
while true; do
   if mkdir $lockdir > /dev/null 2>&1; then
      break
   fi
   sleep 1
done
# FIXME: race condition here if user kills between mkdir and trap.
trap "rmdir $lockdir; exit 1" 1 2 15

# Run the compile.
"$prog" $args
status=$?

if test -f "$cofile"; then
   mv "$cofile" "$ofile"
fi

rmdir $lockdir
exit $status
