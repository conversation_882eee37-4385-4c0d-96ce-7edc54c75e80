IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=2 logtostderr=0 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
WARNING: Logging before InitGoogleLogging() is written to STDERR
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=0 logtostderr=0 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] foo bar 10 3.4
EDATE TIME__ THREADID logging_unittest.cc:LINE] Plog every 2, iteration 1: __SUCCESS__ [0]
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 3, iteration 1
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 4, iteration 1
WDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 5, iteration 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 1
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log if less than 3 every 2, iteration 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 2
EDATE TIME__ THREADID logging_unittest.cc:LINE] Plog every 2, iteration 3: __ENOENT__ [2]
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 3
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log if less than 3 every 2, iteration 3
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 3, iteration 4
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 4
EDATE TIME__ THREADID logging_unittest.cc:LINE] Plog every 2, iteration 5: __EINTR__ [4]
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 4, iteration 5
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 5
WDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 5, iteration 6
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 6
EDATE TIME__ THREADID logging_unittest.cc:LINE] Plog every 2, iteration 7: __ENXIO__ [6]
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 3, iteration 7
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 7
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 8
EDATE TIME__ THREADID logging_unittest.cc:LINE] Plog every 2, iteration 9: __ENOEXEC__ [8]
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 4, iteration 9
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 9
EDATE TIME__ THREADID logging_unittest.cc:LINE] Log every 3, iteration 10
IDATE TIME__ THREADID logging_unittest.cc:LINE] Log if every 1, iteration 10
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if this
IDATE TIME__ THREADID logging_unittest.cc:LINE] array
IDATE TIME__ THREADID logging_unittest.cc:LINE] const array
EDATE TIME__ THREADID logging_unittest.cc:LINE] foo 1000 0000001000 3e8
no prefix
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: foo bar 10 3.400000
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: array
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: const array
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: ptr 0x12345678
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: ptr __NULLP__
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: foo 1000 0000001000 3e8
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: foo 1000
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: foo 1000
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: RAW_LOG ERROR: The Message was too long!
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: RAW_LOG ERROR: The Message was too long!
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0 on
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 1 on
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 2 on
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=0 logtostderr=0 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=1 stderrthreshold=0 logtostderr=0 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=-1 stderrthreshold=0 logtostderr=0 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=1 logtostderr=0 alsologtostderr=0
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=2 logtostderr=0 alsologtostderr=0
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=3 logtostderr=0 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=3 logtostderr=1 alsologtostderr=0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=0 stderrthreshold=3 logtostderr=0 alsologtostderr=1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=1 stderrthreshold=1 logtostderr=0 alsologtostderr=0
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Test: v=1 stderrthreshold=3 logtostderr=0 alsologtostderr=1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: vlog 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] log info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if -1
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info
WDATE TIME__ THREADID logging_unittest.cc:LINE] log_if warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] log_if info every 1 expr
EDATE TIME__ THREADID logging_unittest.cc:LINE] log_if error every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] vlog_if 0 every 1 expr
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_STRING: reported info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_STRING: reported warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_STRING: reported error
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_STRING:  LOG_STRING: collected info
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_STRING:  LOG_STRING: collected warning
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_STRING:  LOG_STRING: collected error
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: collected info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: collected warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: collected error
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: reported info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: reported warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: reported error
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_TO_SINK:
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: collected info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: collected warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK: collected error
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK_BUT_NOT_TO_LOGFILE: collected info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK_BUT_NOT_TO_LOGFILE: collected warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_SINK_BUT_NOT_TO_LOGFILE: collected error
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_STRING: collected info
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_TO_STRING:  LOG_TO_STRING: collected info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_STRING: collected warning
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_TO_STRING:  LOG_TO_STRING: collected warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_STRING: collected error
IDATE TIME__ THREADID logging_unittest.cc:LINE] Captured by LOG_TO_STRING:  LOG_TO_STRING: collected error
IDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_STRING: reported info
WDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_STRING: reported warning
EDATE TIME__ THREADID logging_unittest.cc:LINE] LOG_TO_STRING: reported error
IDATE TIME__ THREADID logging_unittest.cc:LINE] Message 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Buffering
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Buffered
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Waiting
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Sink got a messages
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Waited
IDATE TIME__ THREADID logging_unittest.cc:LINE] Sink is sending out a message: IDATE TIME__ THREADID logging_unittest.cc:LINE] Message 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] Have 0 left
EDATE TIME__ THREADID logging_unittest.cc:LINE] Message 2
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Buffering
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Buffered
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Waiting
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Sink got a messages
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Waited
IDATE TIME__ THREADID logging_unittest.cc:LINE] Sink is sending out a message: EDATE TIME__ THREADID logging_unittest.cc:LINE] Message 2
IDATE TIME__ THREADID logging_unittest.cc:LINE] Have 0 left
WDATE TIME__ THREADID logging_unittest.cc:LINE] Message 3
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Buffering
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Buffered
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Waiting
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Sink got a messages
IDATE TIME__ THREADID logging_unittest.cc:LINE] RAW: Waited
IDATE TIME__ THREADID logging_unittest.cc:LINE] Sink is sending out a message: WDATE TIME__ THREADID logging_unittest.cc:LINE] Message 3
IDATE TIME__ THREADID logging_unittest.cc:LINE] Have 0 left
IDATE TIME__ THREADID logging_unittest.cc:LINE] Sink capture: IDATE TIME__ THREADID logging_unittest.cc:LINE] Message 1
IDATE TIME__ THREADID logging_unittest.cc:LINE] Sink capture: EDATE TIME__ THREADID logging_unittest.cc:LINE] Message 2
IDATE TIME__ THREADID logging_unittest.cc:LINE] Sink capture: WDATE TIME__ THREADID logging_unittest.cc:LINE] Message 3
