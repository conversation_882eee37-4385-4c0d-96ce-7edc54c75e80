// generated from rosidl_generator_c/resource/idl.h.em
// with input from example_interfaces:msg\Int8MultiArray.idl
// generated code does not contain a copyright notice

#ifndef EXAMPLE_INTERFACES__MSG__INT8_MULTI_ARRAY_H_
#define EXAMPLE_INTERFACES__MSG__INT8_MULTI_ARRAY_H_

#include "example_interfaces/msg/detail/int8_multi_array__struct.h"
#include "example_interfaces/msg/detail/int8_multi_array__functions.h"
#include "example_interfaces/msg/detail/int8_multi_array__type_support.h"

#endif  // EXAMPLE_INTERFACES__MSG__INT8_MULTI_ARRAY_H_
