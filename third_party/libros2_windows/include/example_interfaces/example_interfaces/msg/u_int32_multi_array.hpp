// generated from rosidl_generator_cpp/resource/idl.hpp.em
// generated code does not contain a copyright notice

#ifndef EXAMPLE_INTERFACES__MSG__U_INT32_MULTI_ARRAY_HPP_
#define EXAMPLE_INTERFACES__MSG__U_INT32_MULTI_ARRAY_HPP_

#include "example_interfaces/msg/detail/u_int32_multi_array__struct.hpp"
#include "example_interfaces/msg/detail/u_int32_multi_array__builder.hpp"
#include "example_interfaces/msg/detail/u_int32_multi_array__traits.hpp"
#include "example_interfaces/msg/detail/u_int32_multi_array__type_support.hpp"

#endif  // EXAMPLE_INTERFACES__MSG__U_INT32_MULTI_ARRAY_HPP_
