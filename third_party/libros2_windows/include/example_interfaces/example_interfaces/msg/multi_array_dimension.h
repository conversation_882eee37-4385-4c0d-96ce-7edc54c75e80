// generated from rosidl_generator_c/resource/idl.h.em
// with input from example_interfaces:msg\MultiArrayDimension.idl
// generated code does not contain a copyright notice

#ifndef EXAMPLE_INTERFACES__MSG__MULTI_ARRAY_DIMENSION_H_
#define EXAMPLE_INTERFACES__MSG__MULTI_ARRAY_DIMENSION_H_

#include "example_interfaces/msg/detail/multi_array_dimension__struct.h"
#include "example_interfaces/msg/detail/multi_array_dimension__functions.h"
#include "example_interfaces/msg/detail/multi_array_dimension__type_support.h"

#endif  // EXAMPLE_INTERFACES__MSG__MULTI_ARRAY_DIMENSION_H_
