// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from logging_demo:srv\ConfigLogger.idl
// generated code does not contain a copyright notice

#ifndef LOGGING_DEMO__SRV__DETAIL__CONFIG_LOGGER__TYPE_SUPPORT_HPP_
#define LOGGING_DEMO__SRV__DETAIL__CONFIG_LOGGER__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "logging_demo/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/service_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_logging_demo
const rosidl_service_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  logging_demo,
  srv,
  ConfigLogger
)();
#ifdef __cplusplus
}
#endif

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_logging_demo
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  logging_demo,
  srv,
  ConfigLogger_Request
)();
#ifdef __cplusplus
}
#endif

// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_logging_demo
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  logging_demo,
  srv,
  ConfigLogger_Response
)();
#ifdef __cplusplus
}
#endif


#endif  // LOGGING_DEMO__SRV__DETAIL__CONFIG_LOGGER__TYPE_SUPPORT_HPP_
