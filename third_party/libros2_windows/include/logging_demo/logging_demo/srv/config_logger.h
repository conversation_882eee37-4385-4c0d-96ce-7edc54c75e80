// generated from rosidl_generator_c/resource/idl.h.em
// with input from logging_demo:srv\ConfigLogger.idl
// generated code does not contain a copyright notice

#ifndef LOGGING_DEMO__SRV__CONFIG_LOGGER_H_
#define LOGGING_DEMO__SRV__CONFIG_LOGGER_H_

#include "logging_demo/srv/detail/config_logger__struct.h"
#include "logging_demo/srv/detail/config_logger__functions.h"
#include "logging_demo/srv/detail/config_logger__type_support.h"

#endif  // LOGGING_DEMO__SRV__CONFIG_LOGGER_H_
