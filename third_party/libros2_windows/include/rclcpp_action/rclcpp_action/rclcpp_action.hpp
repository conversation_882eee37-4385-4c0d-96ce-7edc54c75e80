// Copyright 2018 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/** \mainpage rclcpp_action: ROS Action Client Library for C++
 *
 * `rclcpp_action` provides the canonical C++ API for interacting with ROS Actions.
 * It consists of these main components:
 *
 * - Action Client
 *   - rclcpp_action/client.hpp
 *   - rclcpp_action/create_client.hpp
 *   - rclcpp_action/client_goal_handle.hpp
 * - Action Server
 *   - rclcpp_action/server.hpp
 *   - rclcpp_action/create_server.hpp
 *   - rclcpp_action/server_goal_handle.hpp
 */

#ifndef RCLCPP_ACTION__RCLCPP_ACTION_HPP_
#define RCLCPP_ACTION__RCLCPP_ACTION_HPP_

#include <csignal>
#include <memory>

#include "rclcpp_action/client.hpp"
#include "rclcpp_action/client_goal_handle.hpp"
#include "rclcpp_action/create_client.hpp"
#include "rclcpp_action/create_server.hpp"
#include "rclcpp_action/server.hpp"
#include "rclcpp_action/server_goal_handle.hpp"
#include "rclcpp_action/visibility_control.hpp"

#endif  // RCLCPP_ACTION__RCLCPP_ACTION_HPP_
