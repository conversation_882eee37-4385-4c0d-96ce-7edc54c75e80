
#ifndef DETAIL_IGNITION_MATH_VISIBLE_H
#define DETAIL_IGNITION_MATH_VISIBLE_H

#ifdef IGNITION_MATH_STATIC_DEFINE
#  define DETAIL_IGNITION_MATH_VISIBLE
#  define DETAIL_IGNITION_MATH_HIDDEN
#else
#  ifndef DETAIL_IGNITION_MATH_VISIBLE
#    ifdef ignition_math6_EXPORTS
        /* We are building this library */
#      define DETAIL_IGNITION_MATH_VISIBLE __declspec(dllexport)
#    else
        /* We are using this library */
#      define DETAIL_IGNITION_MATH_VISIBLE __declspec(dllimport)
#    endif
#  endif

#  ifndef DETAIL_IGNITION_MATH_HIDDEN
#    define DE<PERSON>IL_IGNITION_MATH_HIDDEN 
#  endif
#endif

#ifndef IGN_DEPRECATED_ALL_VERSIONS
#  define IGN_DEPRECATED_ALL_VERSIONS __declspec(deprecated)
#endif

#ifndef IGN_DEPRECATED_ALL_VERSIONS_EXPORT
#  define IGN_DEPRECATED_ALL_VERSIONS_EXPORT DETAIL_IGNITION_MATH_VISIBLE IGN_DEPRECATED_ALL_VERSIONS
#endif

#ifndef IGN_DEPRECATED_ALL_VERSIONS_NO_EXPORT
#  define IGN_DEPRECATED_ALL_VERSIONS_NO_EXPORT DETAIL_IGNITION_MATH_HIDDEN IGN_DEPRECATED_ALL_VERSIONS
#endif

#if 0 /* DEFINE_NO_DEPRECATED */
#  ifndef IGNITION_MATH_NO_DEPRECATED
#    define IGNITION_MATH_NO_DEPRECATED
#  endif
#endif

#endif /* DETAIL_IGNITION_MATH_VISIBLE_H */
