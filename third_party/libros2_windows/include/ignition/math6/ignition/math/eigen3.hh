/*
 * Copyright (C) 2017 Open Source Robotics Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
*/

//                ****** Do not modify this file. ******
// This file is automatically generated by CMake. Changes should instead be
// made to cmake/ign_auto_headers.hh.in in ignition-cmake

#include <ignition/math/config.hh>
#include <ignition/math/eigen3/Conversions.hh>
#include <ignition/math/eigen3/Util.hh>

