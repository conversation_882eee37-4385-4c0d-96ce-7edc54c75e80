/*
 * Copyright (C) 2017 Open Source Robotics Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
*/

//                ****** Do not modify this file. ******
// This file is automatically generated by CMake. Changes should instead be
// made to cmake/ign_auto_headers.hh.in in ignition-cmake

#include <ignition/math/config.hh>
#include <ignition/math/graph/Edge.hh>
#include <ignition/math/graph/Graph.hh>
#include <ignition/math/graph/GraphAlgorithms.hh>
#include <ignition/math/graph/Vertex.hh>
#include <ignition/math/Angle.hh>
#include <ignition/math/AxisAlignedBox.hh>
#include <ignition/math/Box.hh>
#include <ignition/math/Capsule.hh>
#include <ignition/math/Color.hh>
#include <ignition/math/Cylinder.hh>
#include <ignition/math/DiffDriveOdometry.hh>
#include <ignition/math/Ellipsoid.hh>
#include <ignition/math/Filter.hh>
#include <ignition/math/Frustum.hh>
#include <ignition/math/GaussMarkovProcess.hh>
#include <ignition/math/Helpers.hh>
#include <ignition/math/Inertial.hh>
#include <ignition/math/Kmeans.hh>
#include <ignition/math/Line2.hh>
#include <ignition/math/Line3.hh>
#include <ignition/math/MassMatrix3.hh>
#include <ignition/math/Material.hh>
#include <ignition/math/MaterialType.hh>
#include <ignition/math/Matrix3.hh>
#include <ignition/math/Matrix4.hh>
#include <ignition/math/MovingWindowFilter.hh>
#include <ignition/math/OrientedBox.hh>
#include <ignition/math/PID.hh>
#include <ignition/math/Plane.hh>
#include <ignition/math/Pose3.hh>
#include <ignition/math/Quaternion.hh>
#include <ignition/math/Rand.hh>
#include <ignition/math/RollingMean.hh>
#include <ignition/math/RotationSpline.hh>
#include <ignition/math/SemanticVersion.hh>
#include <ignition/math/SignalStats.hh>
#include <ignition/math/SpeedLimiter.hh>
#include <ignition/math/Sphere.hh>
#include <ignition/math/SphericalCoordinates.hh>
#include <ignition/math/Spline.hh>
#include <ignition/math/Stopwatch.hh>
#include <ignition/math/Temperature.hh>
#include <ignition/math/Triangle.hh>
#include <ignition/math/Triangle3.hh>
#include <ignition/math/Vector2.hh>
#include <ignition/math/Vector3.hh>
#include <ignition/math/Vector3Stats.hh>
#include <ignition/math/Vector4.hh>

