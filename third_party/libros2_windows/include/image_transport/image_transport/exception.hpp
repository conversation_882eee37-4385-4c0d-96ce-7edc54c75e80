// Copyright (c) 2009, Willow Garage, Inc.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//
//    * Neither the name of the Willow Garage nor the names of its
//      contributors may be used to endorse or promote products derived from
//      this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

#ifndef IMAGE_TRANSPORT__EXCEPTION_HPP_
#define IMAGE_TRANSPORT__EXCEPTION_HPP_

#include <stdexcept>
#include <string>

#include "image_transport/visibility_control.hpp"

namespace image_transport
{

/**
 * \brief A base class for all image_transport exceptions inheriting from std::runtime_error.
 */
class Exception : public std::runtime_error
{
public:
  explicit Exception(const std::string & message)
  : std::runtime_error(message) {}
};

/**
 * \brief An exception class thrown when image_transport is unable to load a requested transport.
 */
class TransportLoadException : public Exception
{
public:
  TransportLoadException(const std::string & transport, const std::string & message)
  : Exception("Unable to load plugin for transport '" + transport + "', error string:\n" + message),
    transport_(transport.c_str())
  {
  }

  std::string getTransport() const {return transport_;}

protected:
  const char * transport_;
};

}  // namespace image_transport

#endif  // IMAGE_TRANSPORT__EXCEPTION_HPP_
