// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hyper_vision_msgs:msg\MotionFrameXyz.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__STRUCT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/MotionFrameXyz in the package hyper_vision_msgs.
typedef struct hyper_vision_msgs__msg__MotionFrameXyz
{
  float x;
  float y;
  float z;
} hyper_vision_msgs__msg__MotionFrameXyz;

// Struct for a sequence of hyper_vision_msgs__msg__MotionFrameXyz.
typedef struct hyper_vision_msgs__msg__MotionFrameXyz__Sequence
{
  hyper_vision_msgs__msg__MotionFrameXyz * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hyper_vision_msgs__msg__MotionFrameXyz__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__STRUCT_H_
