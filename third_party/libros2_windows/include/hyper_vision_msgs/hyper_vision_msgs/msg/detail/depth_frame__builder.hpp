// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from hyper_vision_msgs:msg\DepthFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__BUILDER_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "hyper_vision_msgs/msg/detail/depth_frame__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace hyper_vision_msgs
{

namespace msg
{

namespace builder
{

class Init_DepthFrame_capture_time
{
public:
  explicit Init_DepthFrame_capture_time(::hyper_vision_msgs::msg::DepthFrame & msg)
  : msg_(msg)
  {}
  ::hyper_vision_msgs::msg::DepthFrame capture_time(::hyper_vision_msgs::msg::DepthFrame::_capture_time_type arg)
  {
    msg_.capture_time = std::move(arg);
    return std::move(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFrame msg_;
};

class Init_DepthFrame_points_nums
{
public:
  explicit Init_DepthFrame_points_nums(::hyper_vision_msgs::msg::DepthFrame & msg)
  : msg_(msg)
  {}
  Init_DepthFrame_capture_time points_nums(::hyper_vision_msgs::msg::DepthFrame::_points_nums_type arg)
  {
    msg_.points_nums = std::move(arg);
    return Init_DepthFrame_capture_time(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFrame msg_;
};

class Init_DepthFrame_points
{
public:
  Init_DepthFrame_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_DepthFrame_points_nums points(::hyper_vision_msgs::msg::DepthFrame::_points_type arg)
  {
    msg_.points = std::move(arg);
    return Init_DepthFrame_points_nums(msg_);
  }

private:
  ::hyper_vision_msgs::msg::DepthFrame msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::hyper_vision_msgs::msg::DepthFrame>()
{
  return hyper_vision_msgs::msg::builder::Init_DepthFrame_points();
}

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__BUILDER_HPP_
