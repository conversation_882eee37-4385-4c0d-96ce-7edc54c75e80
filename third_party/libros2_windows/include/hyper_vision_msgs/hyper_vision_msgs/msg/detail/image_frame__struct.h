// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hyper_vision_msgs:msg\ImageFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__STRUCT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Constant 'FRAME_FORMAT_ANY'.
enum
{
  hyper_vision_msgs__msg__ImageFrame__FRAME_FORMAT_ANY = 1
};

/// Constant 'FRAME_FORMAT_H265'.
enum
{
  hyper_vision_msgs__msg__ImageFrame__FRAME_FORMAT_H265 = 2
};

/// Constant 'FRAME_FORMAT_RGB'.
enum
{
  hyper_vision_msgs__msg__ImageFrame__FRAME_FORMAT_RGB = 3
};

/// Constant 'FRAME_FORMAT_NV12'.
enum
{
  hyper_vision_msgs__msg__ImageFrame__FRAME_FORMAT_NV12 = 4
};

// Include directives for member types
// Member 'data'
#include "rosidl_runtime_c/primitives_sequence.h"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.h"

/// Struct defined in msg/ImageFrame in the package hyper_vision_msgs.
/**
  * enum 
 */
typedef struct hyper_vision_msgs__msg__ImageFrame
{
  /// message
  rosidl_runtime_c__uint8__Sequence data;
  uint32_t data_bytes;
  uint32_t width;
  uint32_t height;
  uint8_t frame_format;
  uint32_t step;
  uint32_t sequence;
  hyper_vision_msgs__msg__FrameTimeval capture_time;
} hyper_vision_msgs__msg__ImageFrame;

// Struct for a sequence of hyper_vision_msgs__msg__ImageFrame.
typedef struct hyper_vision_msgs__msg__ImageFrame__Sequence
{
  hyper_vision_msgs__msg__ImageFrame * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hyper_vision_msgs__msg__ImageFrame__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__STRUCT_H_
