// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hyper_vision_msgs:msg\DepthFramePoint.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__STRUCT_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__hyper_vision_msgs__msg__DepthFramePoint __attribute__((deprecated))
#else
# define DEPRECATED__hyper_vision_msgs__msg__DepthFramePoint __declspec(deprecated)
#endif

namespace hyper_vision_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct DepthFramePoint_
{
  using Type = DepthFramePoint_<ContainerAllocator>;

  explicit DepthFramePoint_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->x = 0.0f;
      this->y = 0.0f;
      this->z = 0.0f;
      this->intensity = 0;
      this->ring = 0;
      this->timestamp = 0.0;
    }
  }

  explicit DepthFramePoint_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->x = 0.0f;
      this->y = 0.0f;
      this->z = 0.0f;
      this->intensity = 0;
      this->ring = 0;
      this->timestamp = 0.0;
    }
  }

  // field types and members
  using _x_type =
    float;
  _x_type x;
  using _y_type =
    float;
  _y_type y;
  using _z_type =
    float;
  _z_type z;
  using _intensity_type =
    uint8_t;
  _intensity_type intensity;
  using _ring_type =
    uint16_t;
  _ring_type ring;
  using _timestamp_type =
    double;
  _timestamp_type timestamp;

  // setters for named parameter idiom
  Type & set__x(
    const float & _arg)
  {
    this->x = _arg;
    return *this;
  }
  Type & set__y(
    const float & _arg)
  {
    this->y = _arg;
    return *this;
  }
  Type & set__z(
    const float & _arg)
  {
    this->z = _arg;
    return *this;
  }
  Type & set__intensity(
    const uint8_t & _arg)
  {
    this->intensity = _arg;
    return *this;
  }
  Type & set__ring(
    const uint16_t & _arg)
  {
    this->ring = _arg;
    return *this;
  }
  Type & set__timestamp(
    const double & _arg)
  {
    this->timestamp = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator> *;
  using ConstRawPtr =
    const hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hyper_vision_msgs__msg__DepthFramePoint
    std::shared_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hyper_vision_msgs__msg__DepthFramePoint
    std::shared_ptr<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const DepthFramePoint_ & other) const
  {
    if (this->x != other.x) {
      return false;
    }
    if (this->y != other.y) {
      return false;
    }
    if (this->z != other.z) {
      return false;
    }
    if (this->intensity != other.intensity) {
      return false;
    }
    if (this->ring != other.ring) {
      return false;
    }
    if (this->timestamp != other.timestamp) {
      return false;
    }
    return true;
  }
  bool operator!=(const DepthFramePoint_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct DepthFramePoint_

// alias to use template instance with default allocator
using DepthFramePoint =
  hyper_vision_msgs::msg::DepthFramePoint_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__STRUCT_HPP_
