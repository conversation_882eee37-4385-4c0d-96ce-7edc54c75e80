// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hyper_vision_msgs:msg\ImageFrame.idl
// generated code does not contain a copyright notice
#include "hyper_vision_msgs/msg/detail/image_frame__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


// Include directives for member types
// Member `data`
#include "rosidl_runtime_c/primitives_sequence_functions.h"
// Member `capture_time`
#include "hyper_vision_msgs/msg/detail/frame_timeval__functions.h"

bool
hyper_vision_msgs__msg__ImageFrame__init(hyper_vision_msgs__msg__ImageFrame * msg)
{
  if (!msg) {
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__init(&msg->data, 0)) {
    hyper_vision_msgs__msg__ImageFrame__fini(msg);
    return false;
  }
  // data_bytes
  // width
  // height
  // frame_format
  // step
  // sequence
  // capture_time
  if (!hyper_vision_msgs__msg__FrameTimeval__init(&msg->capture_time)) {
    hyper_vision_msgs__msg__ImageFrame__fini(msg);
    return false;
  }
  return true;
}

void
hyper_vision_msgs__msg__ImageFrame__fini(hyper_vision_msgs__msg__ImageFrame * msg)
{
  if (!msg) {
    return;
  }
  // data
  rosidl_runtime_c__uint8__Sequence__fini(&msg->data);
  // data_bytes
  // width
  // height
  // frame_format
  // step
  // sequence
  // capture_time
  hyper_vision_msgs__msg__FrameTimeval__fini(&msg->capture_time);
}

bool
hyper_vision_msgs__msg__ImageFrame__are_equal(const hyper_vision_msgs__msg__ImageFrame * lhs, const hyper_vision_msgs__msg__ImageFrame * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__are_equal(
      &(lhs->data), &(rhs->data)))
  {
    return false;
  }
  // data_bytes
  if (lhs->data_bytes != rhs->data_bytes) {
    return false;
  }
  // width
  if (lhs->width != rhs->width) {
    return false;
  }
  // height
  if (lhs->height != rhs->height) {
    return false;
  }
  // frame_format
  if (lhs->frame_format != rhs->frame_format) {
    return false;
  }
  // step
  if (lhs->step != rhs->step) {
    return false;
  }
  // sequence
  if (lhs->sequence != rhs->sequence) {
    return false;
  }
  // capture_time
  if (!hyper_vision_msgs__msg__FrameTimeval__are_equal(
      &(lhs->capture_time), &(rhs->capture_time)))
  {
    return false;
  }
  return true;
}

bool
hyper_vision_msgs__msg__ImageFrame__copy(
  const hyper_vision_msgs__msg__ImageFrame * input,
  hyper_vision_msgs__msg__ImageFrame * output)
{
  if (!input || !output) {
    return false;
  }
  // data
  if (!rosidl_runtime_c__uint8__Sequence__copy(
      &(input->data), &(output->data)))
  {
    return false;
  }
  // data_bytes
  output->data_bytes = input->data_bytes;
  // width
  output->width = input->width;
  // height
  output->height = input->height;
  // frame_format
  output->frame_format = input->frame_format;
  // step
  output->step = input->step;
  // sequence
  output->sequence = input->sequence;
  // capture_time
  if (!hyper_vision_msgs__msg__FrameTimeval__copy(
      &(input->capture_time), &(output->capture_time)))
  {
    return false;
  }
  return true;
}

hyper_vision_msgs__msg__ImageFrame *
hyper_vision_msgs__msg__ImageFrame__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__ImageFrame * msg = (hyper_vision_msgs__msg__ImageFrame *)allocator.allocate(sizeof(hyper_vision_msgs__msg__ImageFrame), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hyper_vision_msgs__msg__ImageFrame));
  bool success = hyper_vision_msgs__msg__ImageFrame__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hyper_vision_msgs__msg__ImageFrame__destroy(hyper_vision_msgs__msg__ImageFrame * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hyper_vision_msgs__msg__ImageFrame__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hyper_vision_msgs__msg__ImageFrame__Sequence__init(hyper_vision_msgs__msg__ImageFrame__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__ImageFrame * data = NULL;

  if (size) {
    data = (hyper_vision_msgs__msg__ImageFrame *)allocator.zero_allocate(size, sizeof(hyper_vision_msgs__msg__ImageFrame), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hyper_vision_msgs__msg__ImageFrame__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hyper_vision_msgs__msg__ImageFrame__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hyper_vision_msgs__msg__ImageFrame__Sequence__fini(hyper_vision_msgs__msg__ImageFrame__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hyper_vision_msgs__msg__ImageFrame__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hyper_vision_msgs__msg__ImageFrame__Sequence *
hyper_vision_msgs__msg__ImageFrame__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__ImageFrame__Sequence * array = (hyper_vision_msgs__msg__ImageFrame__Sequence *)allocator.allocate(sizeof(hyper_vision_msgs__msg__ImageFrame__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hyper_vision_msgs__msg__ImageFrame__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hyper_vision_msgs__msg__ImageFrame__Sequence__destroy(hyper_vision_msgs__msg__ImageFrame__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hyper_vision_msgs__msg__ImageFrame__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hyper_vision_msgs__msg__ImageFrame__Sequence__are_equal(const hyper_vision_msgs__msg__ImageFrame__Sequence * lhs, const hyper_vision_msgs__msg__ImageFrame__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hyper_vision_msgs__msg__ImageFrame__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hyper_vision_msgs__msg__ImageFrame__Sequence__copy(
  const hyper_vision_msgs__msg__ImageFrame__Sequence * input,
  hyper_vision_msgs__msg__ImageFrame__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hyper_vision_msgs__msg__ImageFrame);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hyper_vision_msgs__msg__ImageFrame * data =
      (hyper_vision_msgs__msg__ImageFrame *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hyper_vision_msgs__msg__ImageFrame__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hyper_vision_msgs__msg__ImageFrame__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hyper_vision_msgs__msg__ImageFrame__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
