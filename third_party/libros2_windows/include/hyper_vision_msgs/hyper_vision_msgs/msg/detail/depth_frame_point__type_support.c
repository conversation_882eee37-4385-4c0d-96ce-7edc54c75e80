// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from hyper_vision_msgs:msg\DepthFramePoint.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "hyper_vision_msgs/msg/detail/depth_frame_point__rosidl_typesupport_introspection_c.h"
#include "hyper_vision_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "hyper_vision_msgs/msg/detail/depth_frame_point__functions.h"
#include "hyper_vision_msgs/msg/detail/depth_frame_point__struct.h"


#ifdef __cplusplus
extern "C"
{
#endif

void hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  hyper_vision_msgs__msg__DepthFramePoint__init(message_memory);
}

void hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_fini_function(void * message_memory)
{
  hyper_vision_msgs__msg__DepthFramePoint__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_member_array[6] = {
  {
    "x",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__DepthFramePoint, x),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "y",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__DepthFramePoint, y),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "z",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_FLOAT,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__DepthFramePoint, z),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "intensity",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__DepthFramePoint, intensity),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "ring",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT16,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__DepthFramePoint, ring),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "timestamp",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(hyper_vision_msgs__msg__DepthFramePoint, timestamp),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_members = {
  "hyper_vision_msgs__msg",  // message namespace
  "DepthFramePoint",  // message name
  6,  // number of fields
  sizeof(hyper_vision_msgs__msg__DepthFramePoint),
  hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_member_array,  // message members
  hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_init_function,  // function to initialize message memory (memory has to be allocated)
  hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_type_support_handle = {
  0,
  &hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_hyper_vision_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, hyper_vision_msgs, msg, DepthFramePoint)() {
  if (!hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_type_support_handle.typesupport_identifier) {
    hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &hyper_vision_msgs__msg__DepthFramePoint__rosidl_typesupport_introspection_c__DepthFramePoint_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
