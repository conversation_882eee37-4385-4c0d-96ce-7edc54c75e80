// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from hyper_vision_msgs:msg\DepthFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__TYPE_SUPPORT_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "hyper_vision_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_hyper_vision_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  hyper_vision_msgs,
  msg,
  DepthFrame
)();
#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__TYPE_SUPPORT_HPP_
