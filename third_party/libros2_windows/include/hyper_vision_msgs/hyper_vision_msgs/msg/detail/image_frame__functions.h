// generated from rosidl_generator_c/resource/idl__functions.h.em
// with input from hyper_vision_msgs:msg\ImageFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__FUNCTIONS_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__FUNCTIONS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stdlib.h>

#include "rosidl_runtime_c/visibility_control.h"
#include "hyper_vision_msgs/msg/rosidl_generator_c__visibility_control.h"

#include "hyper_vision_msgs/msg/detail/image_frame__struct.h"

/// Initialize msg/ImageFrame message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * hyper_vision_msgs__msg__ImageFrame
 * )) before or use
 * hyper_vision_msgs__msg__ImageFrame__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
bool
hyper_vision_msgs__msg__ImageFrame__init(hyper_vision_msgs__msg__ImageFrame * msg);

/// Finalize msg/ImageFrame message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
void
hyper_vision_msgs__msg__ImageFrame__fini(hyper_vision_msgs__msg__ImageFrame * msg);

/// Create msg/ImageFrame message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * hyper_vision_msgs__msg__ImageFrame__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
hyper_vision_msgs__msg__ImageFrame *
hyper_vision_msgs__msg__ImageFrame__create();

/// Destroy msg/ImageFrame message.
/**
 * It calls
 * hyper_vision_msgs__msg__ImageFrame__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
void
hyper_vision_msgs__msg__ImageFrame__destroy(hyper_vision_msgs__msg__ImageFrame * msg);

/// Check for msg/ImageFrame message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
bool
hyper_vision_msgs__msg__ImageFrame__are_equal(const hyper_vision_msgs__msg__ImageFrame * lhs, const hyper_vision_msgs__msg__ImageFrame * rhs);

/// Copy a msg/ImageFrame message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
bool
hyper_vision_msgs__msg__ImageFrame__copy(
  const hyper_vision_msgs__msg__ImageFrame * input,
  hyper_vision_msgs__msg__ImageFrame * output);

/// Initialize array of msg/ImageFrame messages.
/**
 * It allocates the memory for the number of elements and calls
 * hyper_vision_msgs__msg__ImageFrame__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
bool
hyper_vision_msgs__msg__ImageFrame__Sequence__init(hyper_vision_msgs__msg__ImageFrame__Sequence * array, size_t size);

/// Finalize array of msg/ImageFrame messages.
/**
 * It calls
 * hyper_vision_msgs__msg__ImageFrame__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
void
hyper_vision_msgs__msg__ImageFrame__Sequence__fini(hyper_vision_msgs__msg__ImageFrame__Sequence * array);

/// Create array of msg/ImageFrame messages.
/**
 * It allocates the memory for the array and calls
 * hyper_vision_msgs__msg__ImageFrame__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
hyper_vision_msgs__msg__ImageFrame__Sequence *
hyper_vision_msgs__msg__ImageFrame__Sequence__create(size_t size);

/// Destroy array of msg/ImageFrame messages.
/**
 * It calls
 * hyper_vision_msgs__msg__ImageFrame__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
void
hyper_vision_msgs__msg__ImageFrame__Sequence__destroy(hyper_vision_msgs__msg__ImageFrame__Sequence * array);

/// Check for msg/ImageFrame message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
bool
hyper_vision_msgs__msg__ImageFrame__Sequence__are_equal(const hyper_vision_msgs__msg__ImageFrame__Sequence * lhs, const hyper_vision_msgs__msg__ImageFrame__Sequence * rhs);

/// Copy an array of msg/ImageFrame messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
bool
hyper_vision_msgs__msg__ImageFrame__Sequence__copy(
  const hyper_vision_msgs__msg__ImageFrame__Sequence * input,
  hyper_vision_msgs__msg__ImageFrame__Sequence * output);

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__FUNCTIONS_H_
