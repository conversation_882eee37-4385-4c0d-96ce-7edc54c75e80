// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from hyper_vision_msgs:msg\FrameTimeval.idl
// generated code does not contain a copyright notice
#include "hyper_vision_msgs/msg/detail/frame_timeval__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
hyper_vision_msgs__msg__FrameTimeval__init(hyper_vision_msgs__msg__FrameTimeval * msg)
{
  if (!msg) {
    return false;
  }
  // tv_sec
  // tv_usec
  return true;
}

void
hyper_vision_msgs__msg__FrameTimeval__fini(hyper_vision_msgs__msg__FrameTimeval * msg)
{
  if (!msg) {
    return;
  }
  // tv_sec
  // tv_usec
}

bool
hyper_vision_msgs__msg__FrameTimeval__are_equal(const hyper_vision_msgs__msg__FrameTimeval * lhs, const hyper_vision_msgs__msg__FrameTimeval * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // tv_sec
  if (lhs->tv_sec != rhs->tv_sec) {
    return false;
  }
  // tv_usec
  if (lhs->tv_usec != rhs->tv_usec) {
    return false;
  }
  return true;
}

bool
hyper_vision_msgs__msg__FrameTimeval__copy(
  const hyper_vision_msgs__msg__FrameTimeval * input,
  hyper_vision_msgs__msg__FrameTimeval * output)
{
  if (!input || !output) {
    return false;
  }
  // tv_sec
  output->tv_sec = input->tv_sec;
  // tv_usec
  output->tv_usec = input->tv_usec;
  return true;
}

hyper_vision_msgs__msg__FrameTimeval *
hyper_vision_msgs__msg__FrameTimeval__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__FrameTimeval * msg = (hyper_vision_msgs__msg__FrameTimeval *)allocator.allocate(sizeof(hyper_vision_msgs__msg__FrameTimeval), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(hyper_vision_msgs__msg__FrameTimeval));
  bool success = hyper_vision_msgs__msg__FrameTimeval__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
hyper_vision_msgs__msg__FrameTimeval__destroy(hyper_vision_msgs__msg__FrameTimeval * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    hyper_vision_msgs__msg__FrameTimeval__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
hyper_vision_msgs__msg__FrameTimeval__Sequence__init(hyper_vision_msgs__msg__FrameTimeval__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__FrameTimeval * data = NULL;

  if (size) {
    data = (hyper_vision_msgs__msg__FrameTimeval *)allocator.zero_allocate(size, sizeof(hyper_vision_msgs__msg__FrameTimeval), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = hyper_vision_msgs__msg__FrameTimeval__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        hyper_vision_msgs__msg__FrameTimeval__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
hyper_vision_msgs__msg__FrameTimeval__Sequence__fini(hyper_vision_msgs__msg__FrameTimeval__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      hyper_vision_msgs__msg__FrameTimeval__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

hyper_vision_msgs__msg__FrameTimeval__Sequence *
hyper_vision_msgs__msg__FrameTimeval__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  hyper_vision_msgs__msg__FrameTimeval__Sequence * array = (hyper_vision_msgs__msg__FrameTimeval__Sequence *)allocator.allocate(sizeof(hyper_vision_msgs__msg__FrameTimeval__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = hyper_vision_msgs__msg__FrameTimeval__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
hyper_vision_msgs__msg__FrameTimeval__Sequence__destroy(hyper_vision_msgs__msg__FrameTimeval__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    hyper_vision_msgs__msg__FrameTimeval__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
hyper_vision_msgs__msg__FrameTimeval__Sequence__are_equal(const hyper_vision_msgs__msg__FrameTimeval__Sequence * lhs, const hyper_vision_msgs__msg__FrameTimeval__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!hyper_vision_msgs__msg__FrameTimeval__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
hyper_vision_msgs__msg__FrameTimeval__Sequence__copy(
  const hyper_vision_msgs__msg__FrameTimeval__Sequence * input,
  hyper_vision_msgs__msg__FrameTimeval__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(hyper_vision_msgs__msg__FrameTimeval);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    hyper_vision_msgs__msg__FrameTimeval * data =
      (hyper_vision_msgs__msg__FrameTimeval *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!hyper_vision_msgs__msg__FrameTimeval__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          hyper_vision_msgs__msg__FrameTimeval__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!hyper_vision_msgs__msg__FrameTimeval__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
