// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hyper_vision_msgs:msg\ImageFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__STRUCT_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__hyper_vision_msgs__msg__ImageFrame __attribute__((deprecated))
#else
# define DEPRECATED__hyper_vision_msgs__msg__ImageFrame __declspec(deprecated)
#endif

namespace hyper_vision_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct ImageFrame_
{
  using Type = ImageFrame_<ContainerAllocator>;

  explicit ImageFrame_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : capture_time(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->data_bytes = 0ul;
      this->width = 0ul;
      this->height = 0ul;
      this->frame_format = 0;
      this->step = 0ul;
      this->sequence = 0ul;
    }
  }

  explicit ImageFrame_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : capture_time(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->data_bytes = 0ul;
      this->width = 0ul;
      this->height = 0ul;
      this->frame_format = 0;
      this->step = 0ul;
      this->sequence = 0ul;
    }
  }

  // field types and members
  using _data_type =
    std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>>;
  _data_type data;
  using _data_bytes_type =
    uint32_t;
  _data_bytes_type data_bytes;
  using _width_type =
    uint32_t;
  _width_type width;
  using _height_type =
    uint32_t;
  _height_type height;
  using _frame_format_type =
    uint8_t;
  _frame_format_type frame_format;
  using _step_type =
    uint32_t;
  _step_type step;
  using _sequence_type =
    uint32_t;
  _sequence_type sequence;
  using _capture_time_type =
    hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>;
  _capture_time_type capture_time;

  // setters for named parameter idiom
  Type & set__data(
    const std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> & _arg)
  {
    this->data = _arg;
    return *this;
  }
  Type & set__data_bytes(
    const uint32_t & _arg)
  {
    this->data_bytes = _arg;
    return *this;
  }
  Type & set__width(
    const uint32_t & _arg)
  {
    this->width = _arg;
    return *this;
  }
  Type & set__height(
    const uint32_t & _arg)
  {
    this->height = _arg;
    return *this;
  }
  Type & set__frame_format(
    const uint8_t & _arg)
  {
    this->frame_format = _arg;
    return *this;
  }
  Type & set__step(
    const uint32_t & _arg)
  {
    this->step = _arg;
    return *this;
  }
  Type & set__sequence(
    const uint32_t & _arg)
  {
    this->sequence = _arg;
    return *this;
  }
  Type & set__capture_time(
    const hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> & _arg)
  {
    this->capture_time = _arg;
    return *this;
  }

  // constant declarations
  static constexpr uint8_t FRAME_FORMAT_ANY =
    1u;
  static constexpr uint8_t FRAME_FORMAT_H265 =
    2u;
  static constexpr uint8_t FRAME_FORMAT_RGB =
    3u;
  static constexpr uint8_t FRAME_FORMAT_NV12 =
    4u;

  // pointer types
  using RawPtr =
    hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator> *;
  using ConstRawPtr =
    const hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hyper_vision_msgs__msg__ImageFrame
    std::shared_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hyper_vision_msgs__msg__ImageFrame
    std::shared_ptr<hyper_vision_msgs::msg::ImageFrame_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const ImageFrame_ & other) const
  {
    if (this->data != other.data) {
      return false;
    }
    if (this->data_bytes != other.data_bytes) {
      return false;
    }
    if (this->width != other.width) {
      return false;
    }
    if (this->height != other.height) {
      return false;
    }
    if (this->frame_format != other.frame_format) {
      return false;
    }
    if (this->step != other.step) {
      return false;
    }
    if (this->sequence != other.sequence) {
      return false;
    }
    if (this->capture_time != other.capture_time) {
      return false;
    }
    return true;
  }
  bool operator!=(const ImageFrame_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct ImageFrame_

// alias to use template instance with default allocator
using ImageFrame =
  hyper_vision_msgs::msg::ImageFrame_<std::allocator<void>>;

// constant definitions
#if __cplusplus < 201703L
// static constexpr member variable definitions are only needed in C++14 and below, deprecated in C++17
template<typename ContainerAllocator>
constexpr uint8_t ImageFrame_<ContainerAllocator>::FRAME_FORMAT_ANY;
#endif  // __cplusplus < 201703L
#if __cplusplus < 201703L
// static constexpr member variable definitions are only needed in C++14 and below, deprecated in C++17
template<typename ContainerAllocator>
constexpr uint8_t ImageFrame_<ContainerAllocator>::FRAME_FORMAT_H265;
#endif  // __cplusplus < 201703L
#if __cplusplus < 201703L
// static constexpr member variable definitions are only needed in C++14 and below, deprecated in C++17
template<typename ContainerAllocator>
constexpr uint8_t ImageFrame_<ContainerAllocator>::FRAME_FORMAT_RGB;
#endif  // __cplusplus < 201703L
#if __cplusplus < 201703L
// static constexpr member variable definitions are only needed in C++14 and below, deprecated in C++17
template<typename ContainerAllocator>
constexpr uint8_t ImageFrame_<ContainerAllocator>::FRAME_FORMAT_NV12;
#endif  // __cplusplus < 201703L

}  // namespace msg

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__IMAGE_FRAME__STRUCT_HPP_
