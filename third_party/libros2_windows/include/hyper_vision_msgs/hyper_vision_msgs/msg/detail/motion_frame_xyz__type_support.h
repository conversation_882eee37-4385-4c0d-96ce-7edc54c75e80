// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from hyper_vision_msgs:msg\MotionFrameXyz.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__TYPE_SUPPORT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "hyper_vision_msgs/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_hyper_vision_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  hyper_vision_msgs,
  msg,
  MotionFrameXyz
)();

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__TYPE_SUPPORT_H_
