// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from hyper_vision_msgs:msg\FrameTimeval.idl
// generated code does not contain a copyright notice
#ifndef HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "hyper_vision_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_hyper_vision_msgs
size_t get_serialized_size_hyper_vision_msgs__msg__FrameTimeval(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_hyper_vision_msgs
size_t max_serialized_size_hyper_vision_msgs__msg__FrameTimeval(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_hyper_vision_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, hyper_vision_msgs, msg, FrameTimeval)();

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
