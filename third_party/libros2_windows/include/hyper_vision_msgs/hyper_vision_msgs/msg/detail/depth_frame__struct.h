// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hyper_vision_msgs:msg\DepthFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__STRUCT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'points'
#include "hyper_vision_msgs/msg/detail/depth_frame_point__struct.h"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.h"

/// Struct defined in msg/DepthFrame in the package hyper_vision_msgs.
typedef struct hyper_vision_msgs__msg__DepthFrame
{
  hyper_vision_msgs__msg__DepthFramePoint__Sequence points;
  uint16_t points_nums;
  hyper_vision_msgs__msg__FrameTimeval capture_time;
} hyper_vision_msgs__msg__DepthFrame;

// Struct for a sequence of hyper_vision_msgs__msg__DepthFrame.
typedef struct hyper_vision_msgs__msg__DepthFrame__Sequence
{
  hyper_vision_msgs__msg__DepthFrame * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hyper_vision_msgs__msg__DepthFrame__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__STRUCT_H_
