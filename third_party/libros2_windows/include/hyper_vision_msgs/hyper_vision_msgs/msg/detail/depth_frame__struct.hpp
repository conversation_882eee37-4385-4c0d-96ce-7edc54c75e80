// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hyper_vision_msgs:msg\DepthFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__STRUCT_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "hyper_vision_msgs/msg/detail/depth_frame_point__struct.hpp"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__hyper_vision_msgs__msg__DepthFrame __attribute__((deprecated))
#else
# define DEPRECATED__hyper_vision_msgs__msg__DepthFrame __declspec(deprecated)
#endif

namespace hyper_vision_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct DepthFrame_
{
  using Type = DepthFrame_<ContainerAllocator>;

  explicit DepthFrame_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : capture_time(_init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->points_nums = 0;
    }
  }

  explicit DepthFrame_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : capture_time(_alloc, _init)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->points_nums = 0;
    }
  }

  // field types and members
  using _points_type =
    std::vector<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>>;
  _points_type points;
  using _points_nums_type =
    uint16_t;
  _points_nums_type points_nums;
  using _capture_time_type =
    hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>;
  _capture_time_type capture_time;

  // setters for named parameter idiom
  Type & set__points(
    const std::vector<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<hyper_vision_msgs::msg::DepthFramePoint_<ContainerAllocator>>> & _arg)
  {
    this->points = _arg;
    return *this;
  }
  Type & set__points_nums(
    const uint16_t & _arg)
  {
    this->points_nums = _arg;
    return *this;
  }
  Type & set__capture_time(
    const hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> & _arg)
  {
    this->capture_time = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator> *;
  using ConstRawPtr =
    const hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hyper_vision_msgs__msg__DepthFrame
    std::shared_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hyper_vision_msgs__msg__DepthFrame
    std::shared_ptr<hyper_vision_msgs::msg::DepthFrame_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const DepthFrame_ & other) const
  {
    if (this->points != other.points) {
      return false;
    }
    if (this->points_nums != other.points_nums) {
      return false;
    }
    if (this->capture_time != other.capture_time) {
      return false;
    }
    return true;
  }
  bool operator!=(const DepthFrame_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct DepthFrame_

// alias to use template instance with default allocator
using DepthFrame =
  hyper_vision_msgs::msg::DepthFrame_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME__STRUCT_HPP_
