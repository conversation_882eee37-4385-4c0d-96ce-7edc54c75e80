// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hyper_vision_msgs:msg\DepthFramePoint.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__STRUCT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/DepthFramePoint in the package hyper_vision_msgs.
typedef struct hyper_vision_msgs__msg__DepthFramePoint
{
  float x;
  float y;
  float z;
  uint8_t intensity;
  uint16_t ring;
  double timestamp;
} hyper_vision_msgs__msg__DepthFramePoint;

// Struct for a sequence of hyper_vision_msgs__msg__DepthFramePoint.
typedef struct hyper_vision_msgs__msg__DepthFramePoint__Sequence
{
  hyper_vision_msgs__msg__DepthFramePoint * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hyper_vision_msgs__msg__DepthFramePoint__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__DEPTH_FRAME_POINT__STRUCT_H_
