// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from hyper_vision_msgs:msg\MotionFrameXyz.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__TRAITS_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace hyper_vision_msgs
{

namespace msg
{

inline void to_flow_style_yaml(
  const MotionFrameXyz & msg,
  std::ostream & out)
{
  out << "{";
  // member: x
  {
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << ", ";
  }

  // member: y
  {
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << ", ";
  }

  // member: z
  {
    out << "z: ";
    rosidl_generator_traits::value_to_yaml(msg.z, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const MotionFrameXyz & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << "\n";
  }

  // member: y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << "\n";
  }

  // member: z
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "z: ";
    rosidl_generator_traits::value_to_yaml(msg.z, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const MotionFrameXyz & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace hyper_vision_msgs

namespace rosidl_generator_traits
{

[[deprecated("use hyper_vision_msgs::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const hyper_vision_msgs::msg::MotionFrameXyz & msg,
  std::ostream & out, size_t indentation = 0)
{
  hyper_vision_msgs::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use hyper_vision_msgs::msg::to_yaml() instead")]]
inline std::string to_yaml(const hyper_vision_msgs::msg::MotionFrameXyz & msg)
{
  return hyper_vision_msgs::msg::to_yaml(msg);
}

template<>
inline const char * data_type<hyper_vision_msgs::msg::MotionFrameXyz>()
{
  return "hyper_vision_msgs::msg::MotionFrameXyz";
}

template<>
inline const char * name<hyper_vision_msgs::msg::MotionFrameXyz>()
{
  return "hyper_vision_msgs/msg/MotionFrameXyz";
}

template<>
struct has_fixed_size<hyper_vision_msgs::msg::MotionFrameXyz>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<hyper_vision_msgs::msg::MotionFrameXyz>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<hyper_vision_msgs::msg::MotionFrameXyz>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME_XYZ__TRAITS_HPP_
