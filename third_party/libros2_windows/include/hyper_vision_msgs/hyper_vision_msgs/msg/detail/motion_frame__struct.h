// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from hyper_vision_msgs:msg\MotionFrame.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__STRUCT_H_
#define HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'accel'
// Member 'gyro'
#include "hyper_vision_msgs/msg/detail/motion_frame_xyz__struct.h"
// Member 'capture_time'
#include "hyper_vision_msgs/msg/detail/frame_timeval__struct.h"

/// Struct defined in msg/MotionFrame in the package hyper_vision_msgs.
typedef struct hyper_vision_msgs__msg__MotionFrame
{
  hyper_vision_msgs__msg__MotionFrameXyz accel;
  hyper_vision_msgs__msg__MotionFrameXyz gyro;
  float temperature;
  hyper_vision_msgs__msg__FrameTimeval capture_time;
} hyper_vision_msgs__msg__MotionFrame;

// Struct for a sequence of hyper_vision_msgs__msg__MotionFrame.
typedef struct hyper_vision_msgs__msg__MotionFrame__Sequence
{
  hyper_vision_msgs__msg__MotionFrame * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} hyper_vision_msgs__msg__MotionFrame__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__MOTION_FRAME__STRUCT_H_
