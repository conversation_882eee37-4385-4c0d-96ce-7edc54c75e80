// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from hyper_vision_msgs:msg\FrameTimeval.idl
// generated code does not contain a copyright notice

#ifndef HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__STRUCT_HPP_
#define HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__hyper_vision_msgs__msg__FrameTimeval __attribute__((deprecated))
#else
# define DEPRECATED__hyper_vision_msgs__msg__FrameTimeval __declspec(deprecated)
#endif

namespace hyper_vision_msgs
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct FrameTimeval_
{
  using Type = FrameTimeval_<ContainerAllocator>;

  explicit FrameTimeval_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->tv_sec = 0ul;
      this->tv_usec = 0ul;
    }
  }

  explicit FrameTimeval_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->tv_sec = 0ul;
      this->tv_usec = 0ul;
    }
  }

  // field types and members
  using _tv_sec_type =
    uint32_t;
  _tv_sec_type tv_sec;
  using _tv_usec_type =
    uint32_t;
  _tv_usec_type tv_usec;

  // setters for named parameter idiom
  Type & set__tv_sec(
    const uint32_t & _arg)
  {
    this->tv_sec = _arg;
    return *this;
  }
  Type & set__tv_usec(
    const uint32_t & _arg)
  {
    this->tv_usec = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> *;
  using ConstRawPtr =
    const hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__hyper_vision_msgs__msg__FrameTimeval
    std::shared_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__hyper_vision_msgs__msg__FrameTimeval
    std::shared_ptr<hyper_vision_msgs::msg::FrameTimeval_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const FrameTimeval_ & other) const
  {
    if (this->tv_sec != other.tv_sec) {
      return false;
    }
    if (this->tv_usec != other.tv_usec) {
      return false;
    }
    return true;
  }
  bool operator!=(const FrameTimeval_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct FrameTimeval_

// alias to use template instance with default allocator
using FrameTimeval =
  hyper_vision_msgs::msg::FrameTimeval_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace hyper_vision_msgs

#endif  // HYPER_VISION_MSGS__MSG__DETAIL__FRAME_TIMEVAL__STRUCT_HPP_
