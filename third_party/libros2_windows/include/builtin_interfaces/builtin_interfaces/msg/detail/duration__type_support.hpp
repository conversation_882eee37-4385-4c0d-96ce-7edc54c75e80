// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from builtin_interfaces:msg\Duration.idl
// generated code does not contain a copyright notice

#ifndef BUILTIN_INTERFACES__MSG__DETAIL__DURATION__TYPE_SUPPORT_HPP_
#define BUILTIN_INTERFACES__MSG__DETAIL__DURATION__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "builtin_interfaces/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_builtin_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  builtin_interfaces,
  msg,
  Duration
)();
#ifdef __cplusplus
}
#endif

#endif  // BUILTIN_INTERFACES__MSG__DETAIL__DURATION__TYPE_SUPPORT_HPP_
