// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from composition_interfaces:srv\LoadNode.idl
// generated code does not contain a copyright notice

#ifndef COMPOSITION_INTERFACES__SRV__DETAIL__LOAD_NODE__TYPE_SUPPORT_HPP_
#define COMPOSITION_INTERFACES__SRV__DETAIL__LOAD_NODE__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "composition_interfaces/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/service_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_composition_interfaces
const rosidl_service_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  composition_interfaces,
  srv,
  LoadNode
)();
#ifdef __cplusplus
}
#endif

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_composition_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  composition_interfaces,
  srv,
  LoadNode_Request
)();
#ifdef __cplusplus
}
#endif

// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_composition_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  composition_interfaces,
  srv,
  LoadNode_Response
)();
#ifdef __cplusplus
}
#endif


#endif  // COMPOSITION_INTERFACES__SRV__DETAIL__LOAD_NODE__TYPE_SUPPORT_HPP_
