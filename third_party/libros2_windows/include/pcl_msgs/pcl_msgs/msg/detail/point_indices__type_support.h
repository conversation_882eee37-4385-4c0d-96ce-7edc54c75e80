// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from pcl_msgs:msg\PointIndices.idl
// generated code does not contain a copyright notice

#ifndef PCL_MSGS__MSG__DETAIL__POINT_INDICES__TYPE_SUPPORT_H_
#define PCL_MSGS__MSG__DETAIL__POINT_INDICES__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "pcl_msgs/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_pcl_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  pcl_msgs,
  msg,
  PointIndices
)();

#ifdef __cplusplus
}
#endif

#endif  // PCL_MSGS__MSG__DETAIL__POINT_INDICES__TYPE_SUPPORT_H_
