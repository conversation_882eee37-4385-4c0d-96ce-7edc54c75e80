// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from pcl_msgs:msg\PointIndices.idl
// generated code does not contain a copyright notice

#ifndef PCL_MSGS__MSG__DETAIL__POINT_INDICES__TYPE_SUPPORT_HPP_
#define PCL_MSGS__MSG__DETAIL__POINT_INDICES__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "pcl_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_pcl_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  pcl_msgs,
  msg,
  PointIndices
)();
#ifdef __cplusplus
}
#endif

#endif  // PCL_MSGS__MSG__DETAIL__POINT_INDICES__TYPE_SUPPORT_HPP_
