// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from lifecycle_msgs:msg\TransitionDescription.idl
// generated code does not contain a copyright notice

#ifndef LIFECYCLE_MSGS__MSG__DETAIL__TRANSITION_DESCRIPTION__TYPE_SUPPORT_HPP_
#define LIFECYCLE_MSGS__MSG__DETAIL__TRANSITION_DESCRIPTION__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "lifecycle_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_lifecycle_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  lifecycle_msgs,
  msg,
  TransitionDescription
)();
#ifdef __cplusplus
}
#endif

#endif  // LIFECYCLE_MSGS__MSG__DETAIL__TRANSITION_DESCRIPTION__TYPE_SUPPORT_HPP_
