// generated from rosidl_generator_c/resource/idl.h.em
// with input from lifecycle_msgs:srv\GetAvailableStates.idl
// generated code does not contain a copyright notice

#ifndef LIFECYCLE_MSGS__SRV__GET_AVAILABLE_STATES_H_
#define LIFECYCLE_MSGS__SRV__GET_AVAILABLE_STATES_H_

#include "lifecycle_msgs/srv/detail/get_available_states__struct.h"
#include "lifecycle_msgs/srv/detail/get_available_states__functions.h"
#include "lifecycle_msgs/srv/detail/get_available_states__type_support.h"

#endif  // LIFECYCLE_MSGS__SRV__GET_AVAILABLE_STATES_H_
