// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from diagnostic_msgs:msg\KeyValue.idl
// generated code does not contain a copyright notice

#ifndef DIAGNOSTIC_MSGS__MSG__DETAIL__KEY_VALUE__TYPE_SUPPORT_HPP_
#define DIAGNOSTIC_MSGS__MSG__DETAIL__KEY_VALUE__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "diagnostic_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_diagnostic_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  diagnostic_msgs,
  msg,
  KeyValue
)();
#ifdef __cplusplus
}
#endif

#endif  // DIAGNOSTIC_MSGS__MSG__DETAIL__KEY_VALUE__TYPE_SUPPORT_HPP_
