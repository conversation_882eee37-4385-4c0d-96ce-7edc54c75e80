// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from map_msgs:msg\OccupancyGridUpdate.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "map_msgs/msg/detail/occupancy_grid_update__rosidl_typesupport_introspection_c.h"
#include "map_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "map_msgs/msg/detail/occupancy_grid_update__functions.h"
#include "map_msgs/msg/detail/occupancy_grid_update__struct.h"


// Include directives for member types
// Member `header`
#include "std_msgs/msg/header.h"
// Member `header`
#include "std_msgs/msg/detail/header__rosidl_typesupport_introspection_c.h"
// Member `data`
#include "rosidl_runtime_c/primitives_sequence_functions.h"

#ifdef __cplusplus
extern "C"
{
#endif

void map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  map_msgs__msg__OccupancyGridUpdate__init(message_memory);
}

void map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_fini_function(void * message_memory)
{
  map_msgs__msg__OccupancyGridUpdate__fini(message_memory);
}

size_t map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__size_function__OccupancyGridUpdate__data(
  const void * untyped_member)
{
  const rosidl_runtime_c__int8__Sequence * member =
    (const rosidl_runtime_c__int8__Sequence *)(untyped_member);
  return member->size;
}

const void * map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__get_const_function__OccupancyGridUpdate__data(
  const void * untyped_member, size_t index)
{
  const rosidl_runtime_c__int8__Sequence * member =
    (const rosidl_runtime_c__int8__Sequence *)(untyped_member);
  return &member->data[index];
}

void * map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__get_function__OccupancyGridUpdate__data(
  void * untyped_member, size_t index)
{
  rosidl_runtime_c__int8__Sequence * member =
    (rosidl_runtime_c__int8__Sequence *)(untyped_member);
  return &member->data[index];
}

void map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__fetch_function__OccupancyGridUpdate__data(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const int8_t * item =
    ((const int8_t *)
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__get_const_function__OccupancyGridUpdate__data(untyped_member, index));
  int8_t * value =
    (int8_t *)(untyped_value);
  *value = *item;
}

void map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__assign_function__OccupancyGridUpdate__data(
  void * untyped_member, size_t index, const void * untyped_value)
{
  int8_t * item =
    ((int8_t *)
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__get_function__OccupancyGridUpdate__data(untyped_member, index));
  const int8_t * value =
    (const int8_t *)(untyped_value);
  *item = *value;
}

bool map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__resize_function__OccupancyGridUpdate__data(
  void * untyped_member, size_t size)
{
  rosidl_runtime_c__int8__Sequence * member =
    (rosidl_runtime_c__int8__Sequence *)(untyped_member);
  rosidl_runtime_c__int8__Sequence__fini(member);
  return rosidl_runtime_c__int8__Sequence__init(member, size);
}

static rosidl_typesupport_introspection_c__MessageMember map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_member_array[6] = {
  {
    "header",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(map_msgs__msg__OccupancyGridUpdate, header),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "x",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(map_msgs__msg__OccupancyGridUpdate, x),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "y",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(map_msgs__msg__OccupancyGridUpdate, y),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "width",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(map_msgs__msg__OccupancyGridUpdate, width),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "height",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_UINT32,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(map_msgs__msg__OccupancyGridUpdate, height),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "data",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    true,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(map_msgs__msg__OccupancyGridUpdate, data),  // bytes offset in struct
    NULL,  // default value
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__size_function__OccupancyGridUpdate__data,  // size() function pointer
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__get_const_function__OccupancyGridUpdate__data,  // get_const(index) function pointer
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__get_function__OccupancyGridUpdate__data,  // get(index) function pointer
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__fetch_function__OccupancyGridUpdate__data,  // fetch(index, &value) function pointer
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__assign_function__OccupancyGridUpdate__data,  // assign(index, value) function pointer
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__resize_function__OccupancyGridUpdate__data  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_members = {
  "map_msgs__msg",  // message namespace
  "OccupancyGridUpdate",  // message name
  6,  // number of fields
  sizeof(map_msgs__msg__OccupancyGridUpdate),
  map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_member_array,  // message members
  map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_init_function,  // function to initialize message memory (memory has to be allocated)
  map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_type_support_handle = {
  0,
  &map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_map_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, map_msgs, msg, OccupancyGridUpdate)() {
  map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, std_msgs, msg, Header)();
  if (!map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_type_support_handle.typesupport_identifier) {
    map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &map_msgs__msg__OccupancyGridUpdate__rosidl_typesupport_introspection_c__OccupancyGridUpdate_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
