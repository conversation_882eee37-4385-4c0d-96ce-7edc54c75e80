// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from map_msgs:msg\PointCloud2Update.idl
// generated code does not contain a copyright notice

#ifndef MAP_MSGS__MSG__DETAIL__POINT_CLOUD2_UPDATE__TYPE_SUPPORT_HPP_
#define MAP_MSGS__MSG__DETAIL__POINT_CLOUD2_UPDATE__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "map_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_map_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  map_msgs,
  msg,
  PointCloud2Update
)();
#ifdef __cplusplus
}
#endif

#endif  // MAP_MSGS__MSG__DETAIL__POINT_CLOUD2_UPDATE__TYPE_SUPPORT_HPP_
