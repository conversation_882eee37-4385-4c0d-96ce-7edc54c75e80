// generated from rosidl_generator_c/resource/idl.h.em
// with input from map_msgs:msg\PointCloud2Update.idl
// generated code does not contain a copyright notice

#ifndef MAP_MSGS__MSG__POINT_CLOUD2_UPDATE_H_
#define MAP_MSGS__MSG__POINT_CLOUD2_UPDATE_H_

#include "map_msgs/msg/detail/point_cloud2_update__struct.h"
#include "map_msgs/msg/detail/point_cloud2_update__functions.h"
#include "map_msgs/msg/detail/point_cloud2_update__type_support.h"

#endif  // MAP_MSGS__MSG__POINT_CLOUD2_UPDATE_H_
