// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from map_msgs:srv\GetMapROI.idl
// generated code does not contain a copyright notice

#ifndef MAP_MSGS__SRV__DETAIL__GET_MAP_ROI__TYPE_SUPPORT_HPP_
#define MAP_MSGS__SRV__DETAIL__GET_MAP_ROI__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "map_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/service_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_map_msgs
const rosidl_service_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  map_msgs,
  srv,
  GetMapROI
)();
#ifdef __cplusplus
}
#endif

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_map_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  map_msgs,
  srv,
  GetMapROI_Request
)();
#ifdef __cplusplus
}
#endif

// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_map_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  map_msgs,
  srv,
  GetMapROI_Response
)();
#ifdef __cplusplus
}
#endif


#endif  // MAP_MSGS__SRV__DETAIL__GET_MAP_ROI__TYPE_SUPPORT_HPP_
