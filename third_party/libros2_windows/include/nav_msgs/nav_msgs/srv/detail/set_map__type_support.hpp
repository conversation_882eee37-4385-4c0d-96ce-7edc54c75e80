// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from nav_msgs:srv\SetMap.idl
// generated code does not contain a copyright notice

#ifndef NAV_MSGS__SRV__DETAIL__SET_MAP__TYPE_SUPPORT_HPP_
#define NAV_MSGS__SRV__DETAIL__SET_MAP__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "nav_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/service_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_nav_msgs
const rosidl_service_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  nav_msgs,
  srv,
  SetMap
)();
#ifdef __cplusplus
}
#endif

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_nav_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  nav_msgs,
  srv,
  SetMap_Request
)();
#ifdef __cplusplus
}
#endif

// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_nav_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  nav_msgs,
  srv,
  SetMap_Response
)();
#ifdef __cplusplus
}
#endif


#endif  // NAV_MSGS__SRV__DETAIL__SET_MAP__TYPE_SUPPORT_HPP_
