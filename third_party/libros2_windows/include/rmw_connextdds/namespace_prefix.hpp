// Copyright 2019 ADLINK Technology Limited.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef RMW_CONNEXTDDS__NAMESPACE_PREFIX_HPP_
#define RMW_CONNEXTDDS__NAMESPACE_PREFIX_HPP_

extern const char * const ROS_TOPIC_PREFIX;
extern const char * const ROS_SERVICE_REQUESTER_PREFIX;
extern const char * const ROS_SERVICE_RESPONSE_PREFIX;

#endif  // RMW_CONNEXTDDS__NAMESPACE_PREFIX_HPP_
