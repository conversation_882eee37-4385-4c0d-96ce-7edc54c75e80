# Symlinked to:
# iceoryx_hoofs/include/iceoryx_hoofs/internal/cxx/.clang-tidy
# iceoryx_hoofs/source/cxx/.clang-tidy
# iceoryx_hoofs/platform/.clang-tidy

InheritParentConfig:  true

CheckOptions:
  - { key: readability-identifier-naming.ClassCase,               value: lower_case }
  - { key: readability-identifier-naming.EnumCase,                value: lower_case }
  - { key: readability-identifier-naming.StructCase,              value: lower_case }
  - { key: readability-identifier-naming.UnionCase,               value: lower_case }
  - { key: readability-identifier-naming.MethodCase,              value: lower_case }
  - { key: readability-identifier-naming.FunctionCase,            value: lower_case }
  - { key: readability-identifier-naming.NamespaceCase,           value: lower_case }
  - { key: readability-identifier-naming.PrivateMemberPrefix,     value: m_ }
  - { key: readability-identifier-naming.ProtectedMemberPrefix,   value: m_ }
  - { key: readability-identifier-naming.MemberCase,              value: camelBack }
  - { key: readability-identifier-naming.ConstexprVariableCase,   value: UPPER_CASE }
  - { key: readability-identifier-naming.EnumConstantCase,        value: UPPER_CASE }
  - { key: readability-identifier-naming.GlobalConstantCase,      value: UPPER_CASE }
  - { key: readability-identifier-naming.TemplateParameterCase,   value: CamelCase }
