// Copyright (c) 2019 by <PERSON>. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0
#ifndef IOX_POSH_ICEORYX_POSH_DEPLOYMENT_HPP
#define IOX_POSH_ICEORYX_POSH_DEPLOYMENT_HPP

#include <cstdint>

namespace iox
{
namespace popo
{
class SubscriberPortSingleProducer;
class SubscriberPortMultiProducer;
} // namespace popo
namespace build
{
using OneToManyPolicy = popo::SubscriberPortSingleProducer;
using ManyToManyPolicy = popo::SubscriberPortMultiProducer;
/// @note Do not change the constants here, the file is autogenerated!
///       Edit the iceoryx_posh_deployment.cmake to change the default values,
///       or configure via cmake, e.g. with "-DIOX_MAX_PUBLISHERS=42".
///       If you include iceoryx by source into your cmake project, just add e.g.
///       set(IOX_MAX_PUBLISHERS 42) before add_subdirectory(iceoryx_posh).
// clang-format off
using CommunicationPolicy = ManyToManyPolicy;
constexpr uint32_t IOX_MAX_PUBLISHERS = static_cast<uint32_t>(512);
constexpr uint32_t IOX_MAX_SUBSCRIBERS = static_cast<uint32_t>(1024);
constexpr uint32_t IOX_MAX_INTERFACE_NUMBER = static_cast<uint32_t>(4);
constexpr uint32_t IOX_MAX_SUBSCRIBERS_PER_PUBLISHER = static_cast<uint32_t>(256);
constexpr uint32_t IOX_MAX_CHUNKS_ALLOCATED_PER_PUBLISHER_SIMULTANEOUSLY =
    static_cast<uint32_t>(8);
constexpr uint64_t IOX_MAX_PUBLISHER_HISTORY = static_cast<uint32_t>(16);
constexpr uint32_t IOX_MAX_CHUNKS_HELD_PER_SUBSCRIBER_SIMULTANEOUSLY =
    static_cast<uint32_t>(256);
 constexpr uint32_t IOX_MAX_NUMBER_OF_NOTIFIERS = static_cast<uint32_t>(256);
// clang-format on
} // namespace build
} // namespace iox

#endif // IOX_POSH_ICEORYX_POSH_DEPLOYMENT_HPP
