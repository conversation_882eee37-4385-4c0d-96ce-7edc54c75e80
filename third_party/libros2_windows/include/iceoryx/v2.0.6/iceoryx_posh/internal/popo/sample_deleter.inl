// Copyright (c) 2021 by Apex.AI Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

#ifndef IOX_POSH_POPO_SAMPLE_DELETER_INL
#define IOX_POSH_POPO_SAMPLE_DELETER_INL

#include "iceoryx_posh/internal/popo/sample_deleter.hpp"

namespace iox
{
namespace popo
{
template <typename Port>
SampleDeleter<Port>::SampleDeleter(Port& port) noexcept
    : m_port(&port)
{
}

template <typename Port>
template <typename T>
void SampleDeleter<Port>::operator()(T* const userPayload) noexcept
{
    auto chunkHeader = iox::mepoo::ChunkHeader::fromUserPayload(userPayload);
    m_port->releaseChunk(chunkHeader);
}

template <typename Port>
template <typename T>
void SampleDeleter<Port>::operator()(const T* const userPayload) const noexcept
{
    const auto chunkHeader = iox::mepoo::ChunkHeader::fromUserPayload(userPayload);
    m_port->releaseChunk(chunkHeader);
}

} // namespace popo
} // namespace iox

#endif // IOX_POSH_POPO_SAMPLE_DELETER_INL
