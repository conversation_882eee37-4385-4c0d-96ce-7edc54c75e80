// Copyright (c) 2019 by <PERSON>. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0
#ifndef IOX_POSH_MEPOO_MEPOO_HPP
#define IOX_POSH_MEPOO_MEPOO_HPP

namespace iox
{
/// @brief Mepoo Component Description
namespace mepoo
{
struct ChunkHeader;
struct ChunkManagement;
struct MePooConfig;
struct SegmentConfig;

class MemPool;
template <typename, typename>
class MePooSegment;
template <typename>
class SegmentManager;
class MemoryManager;
class SharedChunk;

} // namespace mepoo
} // namespace iox

#endif // IOX_POSH_MEPOO_MEPOO_HPP
