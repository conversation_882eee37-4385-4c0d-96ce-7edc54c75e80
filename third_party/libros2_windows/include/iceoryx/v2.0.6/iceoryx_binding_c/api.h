// Copyright (c) 2021 - 2022 by Apex.AI Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

#ifndef IOX_BINDING_C_API_H
#define IOX_BINDING_C_API_H

// Provides the complete iceoryx C API in one header.

#include "chunk.h"
#include "client.h"
#include "config.h"
#include "enums.h"
#include "listener.h"
#include "log.h"
#include "node.h"
#include "notification_info.h"
#include "publisher.h"
#include "request_header.h"
#include "response_header.h"
#include "runtime.h"
#include "server.h"
#include "service_description.h"
#include "service_discovery.h"
#include "subscriber.h"
#include "types.h"
#include "user_trigger.h"
#include "wait_set.h"

#endif
