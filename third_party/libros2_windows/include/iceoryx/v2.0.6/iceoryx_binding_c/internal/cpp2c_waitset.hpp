// Copyright (c) 2020 by <PERSON>, Apex.AI Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

#ifndef IOX_BINDING_C_CPP2C_WAITSET_HPP
#define IOX_BINDING_C_CPP2C_WAITSET_HPP

#include "iceoryx_posh/popo/wait_set.hpp"

class cpp2c_WaitSet : public iox::popo::WaitSet<>
{
  public:
    using iox::popo::WaitSet<>::WaitSet;
};

#endif
