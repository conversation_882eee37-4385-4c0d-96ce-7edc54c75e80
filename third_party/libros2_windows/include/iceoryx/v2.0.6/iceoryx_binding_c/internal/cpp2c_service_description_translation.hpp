// Copyright (c) 2021 by Apex.AI Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0

#ifndef IOX_BINDING_C_CPP2C_SERVICE_DESCRIPTION_TRANSLATION_HPP
#define IOX_BINDING_C_CPP2C_SERVICE_DESCRIPTION_TRANSLATION_HPP

#include "iceoryx_binding_c/service_description.h"
#include "iceoryx_posh/capro/service_description.hpp"

iox_service_description_t
TranslateServiceDescription(const iox::capro::ServiceDescription& serviceDescription) noexcept;

#endif
