// Copyright 2019 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/**
 * @file DeadlineMissedStatus.h
 */

#ifndef _DEADLINE_MISSED_STATUS_H_
#define _DEADLINE_MISSED_STATUS_H_

#include <fastdds/dds/core/status/DeadlineMissedStatus.hpp>

namespace eprosima {
namespace fastrtps {

using DeadlineMissedStatus = fastdds::dds::DeadlineMissedStatus;

typedef DeadlineMissedStatus OfferedDeadlineMissedStatus;
typedef DeadlineMissedStatus RequestedDeadlineMissedStatus;

} //end of namespace
} //end of namespace eprosima

#endif /* _DEADLINE_MISSED_STATUS_H_ */
