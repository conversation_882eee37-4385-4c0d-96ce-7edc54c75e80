// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file TypeObjectHashId.h
 * This header file contains the declaration of the described types in the IDL file.
 *
 * This file was generated by the tool gen and modified manually.
 */

#ifndef _TYPEOBJECTHASHID_H_
#define _TYPEOBJECTHASHID_H_

#include <fastrtps/types/TypesBase.h>
#include <stdint.h>
#include <array>
#include <string>
#include <vector>

namespace eprosima {
namespace fastcdr {
class Cdr;
} // namespace fastcdr
} // namespace eprosima

// The types in this file shall be serialized with XCDR encoding version 2
namespace eprosima {
namespace fastrtps {

namespace types {

// First 14 bytes of MD5 of the serialized TypeObject using XCDR
// version 2 with Little Endian encoding
typedef octet EquivalenceHash[14];

/*!
 * @brief This class represents the union TypeObjectHashId defined by the user in the IDL file.
 * @ingroup TYPES_MODULE
 */
class TypeObjectHashId
{
public:

    /*!
     * @brief Default constructor.
     */
    TypeObjectHashId();

    /*!
     * @brief Default destructor.
     */
    ~TypeObjectHashId();

    /*!
     * @brief Copy constructor.
     * @param x Reference to the object TypeObjectHashId that will be copied.
     */
    TypeObjectHashId(
            const TypeObjectHashId& x);

    /*!
     * @brief Move constructor.
     * @param x Reference to the object TypeObjectHashId that will be copied.
     */
    TypeObjectHashId(
            TypeObjectHashId&& x);

    /*!
     * @brief Copy assignment.
     * @param x Reference to the object TypeObjectHashId that will be copied.
     */
    TypeObjectHashId& operator =(
            const TypeObjectHashId& x);

    /*!
     * @brief Move assignment.
     * @param x Reference to the object TypeObjectHashId that will be copied.
     */
    TypeObjectHashId& operator =(
            TypeObjectHashId&& x);

    /*!
     * @brief This function sets the discriminator value.
     * @param __d New value for the discriminator.
     * @exception eprosima::fastcdr::BadParamException This exception is thrown if the new value doesn't correspond to the selected union member.
     */
    void _d(
            uint8_t __d);

    /*!
     * @brief This function returns the value of the discriminator.
     * @return Value of the discriminator
     */
    uint8_t _d() const;

    /*!
     * @brief This function returns a reference to the discriminator.
     * @return Reference to the discriminator.
     */
    uint8_t& _d();

    /*!
     * @brief This function copies the value in member hash
     * @param _hash New value to be copied in member hash
     */
    void hash(
            const EquivalenceHash& _hash);

    /*!
     * @brief This function moves the value in member hash
     * @param _hash New value to be moved in member hash
     */
    void hash(
            EquivalenceHash&& _hash);

    /*!
     * @brief This function returns a constant reference to member hash
     * @return Constant reference to member hash
     * @exception eprosima::fastcdr::BadParamException This exception is thrown if the requested union member is not the current selection.
     */
    const EquivalenceHash& hash() const;

    /*!
     * @brief This function returns a reference to member hash
     * @return Reference to member hash
     * @exception eprosima::fastcdr::BadParamException This exception is thrown if the requested union member is not the current selection.
     */
    EquivalenceHash& hash();

    /*!
     * @brief This function returns the serialized size of a data depending on the buffer alignment.
     * @param data Data which is calculated its serialized size.
     * @param current_alignment Buffer alignment.
     * @return Serialized size.
     */
    static size_t getCdrSerializedSize(
            const TypeObjectHashId& data,
            size_t current_alignment = 0);


    /*!
     * @brief This function serializes an object using CDR serialization.
     * @param cdr CDR serialization object.
     */
    void serialize(
            eprosima::fastcdr::Cdr& cdr) const;

    /*!
     * @brief This function deserializes an object using CDR serialization.
     * @param cdr CDR serialization object.
     */
    void deserialize(
            eprosima::fastcdr::Cdr& cdr);



    /*!
     * @brief This function returns the maximum serialized size of the Key of an object
     * depending on the buffer alignment.
     * @param current_alignment Buffer alignment.
     * @return Maximum serialized size.
     */
    static size_t getKeyMaxCdrSerializedSize(
            size_t current_alignment = 0);

    /*!
     * @brief This function tells you if the Key has been defined for this type
     */
    static bool isKeyDefined();

    /*!
     * @brief This function serializes the key members of an object using CDR serialization.
     * @param cdr CDR serialization object.
     */
    void serializeKey(
            eprosima::fastcdr::Cdr& cdr) const;

private:

    uint8_t m__d;

    EquivalenceHash m_hash;
};

} // namespace types
} // namespace fastrtps
} // namespace eprosima

#endif // _TYPEOBJECTHASHID_H_
