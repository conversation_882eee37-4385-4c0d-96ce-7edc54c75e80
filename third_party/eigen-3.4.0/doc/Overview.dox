namespace Eigen {

/** \mainpage notitle

This is the API documentation for Eigen3. You can <a href="eigen-doc.tgz">download</a> it as a tgz archive for offline reading.

For a first contact with Eigen, the best place is to have a look at the \link GettingStarted getting started \endlink page that show you how to write and compile your first program with Eigen.

Then, the \b quick \b reference \b pages give you a quite complete description of the API in a very condensed format that is specially useful to recall the syntax of a particular feature, or to have a quick look at the API. They currently cover the two following feature sets, and more will come in the future:
  - \link QuickRefPage [QuickRef] Dense matrix and array manipulations \endlink
  - \link SparseQuickRefPage [QuickRef] Sparse linear algebra \endlink

You're a MatLab user? There is also a <a href="AsciiQuickReference.txt">short ASCII reference</a> with Matlab translations.
  
The \b main \b documentation is organized into \em chapters covering different domains of features.
They are themselves composed of \em user \em manual pages describing the different features in a comprehensive way, and \em reference pages that gives you access to the API documentation through the related Eigen's \em modules and \em classes.

Under the \subpage UserManual_CustomizingEigen section, you will find discussions and examples on extending %Eigen's features and supporting custom scalar types.

Under the \subpage UserManual_Generalities section, you will find documentation on more general topics such as preprocessor directives, controlling assertions, multi-threading, MKL support, some Eigen's internal insights, and much more...

Finally, do not miss the search engine, useful to quickly get to the documentation of a given class or function.

Want more? Checkout the <a href="unsupported/index.html">\em unsupported \em modules </a> documentation.

*/

}
