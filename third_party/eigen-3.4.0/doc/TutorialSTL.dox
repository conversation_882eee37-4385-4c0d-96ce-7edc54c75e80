namespace Eigen {

/** \eigenManualPage TutorialSTL STL iterators and algorithms

Since the version 3.4, %Eigen's dense matrices and arrays provide STL compatible iterators.
As demonstrated below, this makes them naturally compatible with range-for-loops and STL's algorithms.

\eigenAutoToc

\section TutorialSTLVectors Iterating over 1D arrays and vectors 

Any dense 1D expressions exposes the pair of `begin()/end()` methods to iterate over them.

This directly enables c++11 range for loops:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_range_for_loop_1d_cxx11.cpp
</td>
<td>
\verbinclude Tutorial_range_for_loop_1d_cxx11.out
</td></tr></table>

One dimensional expressions can also easily be passed to STL algorithms:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_std_sort.cpp
</td>
<td>
\verbinclude Tutorial_std_sort.out
</td></tr></table>

Similar to `std::vector`, 1D expressions also exposes the pair of `cbegin()/cend()` methods to conveniently get const iterators on non-const object.

\section TutorialSTLMatrices Iterating over coefficients of 2D arrays and matrices

STL iterators are intrinsically designed to iterate over 1D structures.
This is why `begin()/end()` methods are disabled for 2D expressions.
Iterating over all coefficients of a 2D expressions is still easily accomplished by creating a 1D linear view through `reshaped()`:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_range_for_loop_2d_cxx11.cpp
</td>
<td>
\verbinclude Tutorial_range_for_loop_2d_cxx11.out
</td></tr></table>

\section TutorialSTLRowsColumns Iterating over rows or columns of 2D arrays and matrices

It is also possible to get iterators over rows or columns of 2D expressions.
Those are available through the `rowwise()` and `colwise()` proxies.
Here is an example sorting each row of a matrix:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_std_sort_rows_cxx11.cpp
</td>
<td>
\verbinclude Tutorial_std_sort_rows_cxx11.out
</td></tr></table>

*/

}
