namespace Eigen {

/** \page TopicLazyEvaluation Lazy Evaluation and Aliasing

Executive summary: %Eigen has intelligent compile-time mechanisms to enable lazy evaluation and removing temporaries where appropriate.
It will handle aliasing automatically in most cases, for example with matrix products. The automatic behavior can be overridden
manually by using the MatrixBase::eval() and MatrixBase::noalias() methods.

When you write a line of code involving a complex expression such as

\code mat1 = mat2 + mat3 * (mat4 + mat5);
\endcode

%Eigen determines automatically, for each sub-expression, whether to evaluate it into a temporary variable. Indeed, in certain cases it is better to evaluate a sub-expression into a temporary variable, while in other cases it is better to avoid that.

A traditional math library without expression templates always evaluates all sub-expressions into temporaries. So with this code,

\code vec1 = vec2 + vec3;
\endcode

a traditional library would evaluate \c vec2 + vec3 into a temporary \c vec4 and then copy \c vec4  into \c vec1. This is of course inefficient: the arrays are traversed twice, so there are a lot of useless load/store operations.

Expression-templates-based libraries can avoid evaluating sub-expressions into temporaries, which in many cases results in large speed improvements.
This is called <i>lazy evaluation</i> as an expression is getting evaluated as late as possible.
In %Eigen <b>all expressions are lazy-evaluated</b>.
More precisely, an expression starts to be evaluated once it is assigned to a matrix.
Until then nothing happens beyond constructing the abstract expression tree.
In contrast to most other expression-templates-based libraries, however, <b>%Eigen might choose to evaluate some sub-expressions into temporaries</b>.
There are two reasons for that: first, pure lazy evaluation is not always a good choice for performance; second, pure lazy evaluation can be very dangerous, for example with matrix products: doing <tt>mat = mat*mat</tt> gives a wrong result if the matrix product is directly evaluated within the destination matrix, because of the way matrix product works.

For these reasons, %Eigen has intelligent compile-time mechanisms to determine automatically which sub-expression should be evaluated into a temporary variable.

So in the basic example,

\code mat1 = mat2 + mat3;
\endcode

%Eigen chooses not to introduce any temporary. Thus the arrays are traversed only once, producing optimized code.
If you really want to force immediate evaluation, use \link MatrixBase::eval() eval()\endlink:

\code mat1 = (mat2 + mat3).eval();
\endcode

Here is now a more involved example:

\code mat1 = -mat2 + mat3 + 5 * mat4;
\endcode

Here again %Eigen won't introduce any temporary, thus producing a single <b>fused</b> evaluation loop, which is clearly the correct choice.

\section TopicLazyEvaluationWhichExpr Which sub-expressions are evaluated into temporaries?

The default evaluation strategy is to fuse the operations in a single loop, and %Eigen will choose it except in a few circumstances.

<b>The first circumstance</b> in which %Eigen chooses to evaluate a sub-expression is when it sees an assignment <tt>a = b;</tt> and the expression \c b has the evaluate-before-assigning \link flags flag\endlink.
The most important example of such an expression is the \link Product matrix product expression\endlink. For example, when you do

\code mat = mat * mat;
\endcode

%Eigen will evaluate <tt>mat * mat</tt> into a temporary matrix, and then copies it into the original \c mat.
This guarantees a correct result as we saw above that lazy evaluation gives wrong results with matrix products.
It also doesn't cost much, as the cost of the matrix product itself is much higher.
Note that this temporary is introduced at evaluation time only, that is, within operator= in this example.
The expression <tt>mat * mat</tt> still return a abstract product type.

What if you know that the result does no alias the operand of the product and want to force lazy evaluation? Then use \link MatrixBase::noalias() .noalias()\endlink instead. Here is an example:

\code mat1.noalias() = mat2 * mat2;
\endcode

Here, since we know that mat2 is not the same matrix as mat1, we know that lazy evaluation is not dangerous, so we may force lazy evaluation. Concretely, the effect of noalias() here is to bypass the evaluate-before-assigning \link flags flag\endlink.

<b>The second circumstance</b> in which %Eigen chooses to evaluate a sub-expression, is when it sees a nested expression such as <tt>a + b</tt> where \c b is already an expression having the evaluate-before-nesting \link flags flag\endlink.
Again, the most important example of such an expression is the \link Product matrix product expression\endlink.
For example, when you do

\code mat1 = mat2 * mat3 + mat4 * mat5;
\endcode

the products <tt>mat2 * mat3</tt> and <tt>mat4 * mat5</tt> gets evaluated separately into temporary matrices before being summed up in <tt>mat1</tt>.
Indeed, to be efficient matrix products need to be evaluated within a destination matrix at hand, and not as simple "dot products".
For small matrices, however, you might want to enforce a "dot-product" based lazy evaluation with lazyProduct().
Again, it is important to understand that those temporaries are created at evaluation time only, that is in operator =.
See TopicPitfalls_auto_keyword for common pitfalls regarding this remark.

<b>The third circumstance</b> in which %Eigen chooses to evaluate a sub-expression, is when its cost model shows that the total cost of an operation is reduced if a sub-expression gets evaluated into a temporary.
Indeed, in certain cases, an intermediate result is sufficiently costly to compute and is reused sufficiently many times, that is worth "caching". Here is an example:

\code mat1 = mat2 * (mat3 + mat4);
\endcode

Here, provided the matrices have at least 2 rows and 2 columns, each coefficient of the expression <tt>mat3 + mat4</tt> is going to be used several times in the matrix product. Instead of computing the sum every time, it is much better to compute it once and store it in a temporary variable. %Eigen understands this and evaluates <tt>mat3 + mat4</tt> into a temporary variable before evaluating the product.

*/

}
