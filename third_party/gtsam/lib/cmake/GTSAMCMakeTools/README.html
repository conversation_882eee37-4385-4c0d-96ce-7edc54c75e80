<h1>GTSAMCMakeTools</h1>
<p>This is the collection of GTSAM CMake tools that may be useful in external projects.  The way to use this collection is by first making a find_package call:</p>
<pre><code>find_package(GTSAMCMakeTools)
</code></pre>
<p>which will add a directory containing the GTSAM CMake tools to the CMAKE_MODULE_PATH variable.  After that, you may include the files you would like to use.  These files and the functions they define are explained below.</p>
<h2>GtsamBuildTypes</h2>
<pre><code>include(GtsamBuildTypes)
</code></pre>
<p>Including this file immediately sets up the following build types and a drop-down list in cmake-gui:</p>
<ul>
<li><code>Debug</code></li>
<li><code>Release</code></li>
<li><code>RelWithDebInfo</code></li>
<li><code>Profiling</code>:  All optimizations enabled and minimal debug symbols</li>
<li><code>Timing</code>:  Defines the symbol GTSAM_ENABLE_TIMING for using GTSAM timing instrumentation</li>
</ul>
<p>It also configures several minor details, as follows:</p>
<ul>
<li>The compile flag <code>-ftemplate-depth=1024</code> is set for newer versions of Clang to handle complex templates.</li>
<li>On Windows, executable and dll output paths are set to <code>${CMAKE_BINARY_DIR}/bin</code> and import library output to <code>${CMAKE_BINARY_DIR}/bin</code>.</li>
</ul>
<p>It defines the following functions:</p>
<ul>
<li><code>gtsam_assign_source_folders( [files] )</code> Organizes files in the IDE into folders to reflect the actual directory structure of those files.  Folders will be determined relative to the current source folder when this function is called.</li>
<li><code>gtsam_assign_all_source_folders()</code> Calls <code>gtsam_assign_source_folders</code> on all cpp, c, and h files recursively in the current source folder.</li>
</ul>
<h2>GtsamTesting</h2>
<pre><code>include(GtsamTesting)
</code></pre>
<p>Defines two useful functions for creating CTest unit tests.  Also immediately creates a <code>check</code> target that builds and runs all unit tests.</p>
<ul>
<li>
<p><code>gtsamAddTestsGlob(groupName globPatterns excludedFiles linkLibraries)</code> Add a group of unit tests.  A list of unit test .cpp files or glob patterns specifies the tests to create.  Tests are assigned into a group name so they can easily by run independently with a make target.  Running 'make check' builds and runs all tests.</p>
<p>Usage example:</p>
<pre><code>gtsamAddTestsGlob(basic "test*.cpp" "testBroken.cpp" "gtsam;GeographicLib")
</code></pre>
<p>Arguments:</p>
<pre><code>groupName:     A name that will allow this group of tests to be run independently, e.g.
               'basic' causes a 'check.basic' target to be created to run this test
               group.
globPatterns:  The list of files or glob patterns from which to create unit tests, with
               one test created for each cpp file.  e.g. "test*.cpp", or
               "testA*.cpp;testB*.cpp;testOneThing.cpp".
excludedFiles: A list of files or globs to exclude, e.g. "testC*.cpp;testBroken.cpp".
               Pass an empty string "" if nothing needs to be excluded.
linkLibraries: The list of libraries to link to in addition to CppUnitLite.
</code></pre>
</li>
<li>
<p><code>gtsamAddExamplesGlob(globPatterns excludedFiles linkLibraries)</code> Add scripts that will serve as examples of how to use the library.  A list of files or glob patterns is specified, and one executable will be created for each matching .cpp file.  These executables will not be installed.  They are build with 'make all' if GTSAM_BUILD_EXAMPLES_ALWAYS is enabled.  They may also be built with 'make examples'.</p>
<p>Usage example:</p>
<pre><code>gtsamAddExamplesGlob("*.cpp" "BrokenExample.cpp" "gtsam;GeographicLib")
</code></pre>
<p>Arguments:</p>
<pre><code>globPatterns:  The list of files or glob patterns from which to create unit tests, with
               one test created for each cpp file.  e.g. "*.cpp", or
               "A*.cpp;B*.cpp;MyExample.cpp".
excludedFiles: A list of files or globs to exclude, e.g. "C*.cpp;BrokenExample.cpp".  Pass
               an empty string "" if nothing needs to be excluded.
linkLibraries: The list of libraries to link to.
</code></pre>
</li>
</ul>
<h2>GtsamMatlabWrap</h2>
<pre><code>include(GtsamMatlabWrap)
</code></pre>
<p>Defines functions for generating MATLAB wrappers.  Also immediately creates several CMake options for configuring the wrapper.</p>
<ul>
<li>
<p><code>wrap_and_install_library(interfaceHeader linkLibraries extraIncludeDirs extraMexFlags)</code> Generates wrap code and compiles the wrapper.</p>
<p>Usage example:</p>
<pre><code>`wrap_and_install_library("lba.h" "" "" "")`
</code></pre>
<p>Arguments:</p>
<pre><code>interfaceHeader:  The relative or absolute path to the wrapper interface
                  definition file.
linkLibraries:    Any *additional* libraries to link.  Your project library
                  (e.g. `lba`), libraries it depends on, and any necessary
                  MATLAB libraries will be linked automatically.  So normally,
                  leave this empty.
extraIncludeDirs: Any *additional* include paths required by dependent
                  libraries that have not already been added by
                  include_directories.  Again, normally, leave this empty.
extraMexFlags:    Any *additional* flags to pass to the compiler when building
                  the wrap code.  Normally, leave this empty.
</code></pre>
</li>
</ul>
<h2>GtsamMakeConfigFile</h2>
<pre><code>include(GtsamMakeConfigFile)
</code></pre>
<p>Defines a function for generating a config file so your project may be found with the CMake <code>find_package</code> function.  TODO: Write documentation.</p>