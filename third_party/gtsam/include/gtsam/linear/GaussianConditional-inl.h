/* ----------------------------------------------------------------------------

 * GTSAM Copyright 2010, Georgia Tech Research Corporation,
 * Atlanta, Georgia 30332-0415
 * All Rights Reserved
 * Authors: <AUTHORS>

 * See LICENSE for the license information

 * -------------------------------------------------------------------------- */

/**
 * @file    GaussianConditional-inl.h
 * @brief   Conditional Gaussian Base class
 * <AUTHOR>
 */

// \callgraph

#pragma once

namespace gtsam {

  /* ************************************************************************* */
  template<typename TERMS>
  GaussianConditional::GaussianConditional(const TERMS& terms,
    size_t nrFrontals, const Vector& d, const SharedDiagonal& sigmas) :
  BaseFactor(terms, d, sigmas), BaseConditional(nrFrontals) {}

  /* ************************************************************************* */
  template<typename KEYS>
  GaussianConditional::GaussianConditional(
    const KEYS& keys, size_t nrFrontals, const VerticalBlockMatrix& augmentedMatrix, const SharedDiagonal& sigmas) :
  BaseFactor(keys, augmentedMatrix, sigmas), BaseConditional(nrFrontals) {}

} // gtsam
