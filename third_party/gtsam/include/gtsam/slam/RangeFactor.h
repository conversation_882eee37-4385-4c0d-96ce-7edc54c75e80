/* ----------------------------------------------------------------------------

 * GTSAM Copyright 2010, Georgia Tech Research Corporation,
 * Atlanta, Georgia 30332-0415
 * All Rights Reserved
 * Authors: <AUTHORS>

 * See LICENSE for the license information

 * -------------------------------------------------------------------------- */

#pragma once

#ifdef _MSC_VER
#pragma message("<PERSON>Factor is now an ExpressionFactor in SAM directory")
#else
#warning "RangeFactor is now an ExpressionFactor in SAM directory"
#endif


#include <gtsam/sam/RangeFactor.h>

