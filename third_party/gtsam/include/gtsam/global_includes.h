/* ----------------------------------------------------------------------------

 * GTSAM Copyright 2010, Georgia Tech Research Corporation,
 * Atlanta, Georgia 30332-0415
 * All Rights Reserved
 * Authors: <AUTHORS>

 * See LICENSE for the license information

 * -------------------------------------------------------------------------- */

/**
 * @file     global_includes.h
 * @brief    Included from all GTSAM files
 * <AUTHOR>
 * @ingroup base
 */

#pragma once

#include <gtsam/config.h>      // Configuration from CMake
#include <gtsam/base/types.h>  // Basic types, constants, and compatibility functions
// types.h includes dllexport.h, which contains macros for dllspec tags for Windows DLLs
