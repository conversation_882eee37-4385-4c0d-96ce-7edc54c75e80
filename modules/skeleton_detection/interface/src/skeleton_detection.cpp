/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/skeleton_detection/skeleton_detection.h"

namespace robosense::skeleton
{
  int SkeletonDetection::Init(const YAML::Node& cfg_node)
  {
    RINFO << name() << "Initialization successful...";
    return 0;
  }

  int SkeletonDetection::Start()
  {
    RINFO << name() << "Started...";
    return 0;
  }

  void SkeletonDetection::Stop()
  {
    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.Reset();
    RINFO << name() << "Stopped...";
  }

  void SkeletonDetection::AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null left image message";
      return ;
    }

    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.has_left_image = true;

    if (data_state_.IsReady())
      TryProcess();
  }

  void SkeletonDetection::AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null right image message";
      return ;
    }

    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.has_right_image = true;

    if (data_state_.IsReady())
      TryProcess();
  }

  void SkeletonDetection::AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null depth message";
      return ;
    }

    std::lock_guard<std::mutex> lock(data_state_mutex_);
    data_state_.has_depth = true;

    if (data_state_.IsReady())
      TryProcess();
  }

  void SkeletonDetection::SetCallback(const std::function<void(const SkeletonOutputMsg::Ptr& msg_ptr)>& callback)
  {
    std::lock_guard<std::mutex> lock(output_msg_mtx_);
    output_msg_callback_ = callback;
  }

  void SkeletonDetection::TryProcess()
  {
    // 调用此函数时已经持有 data_state_mutex_ 锁

    if (not data_state_.IsReady())
      return;

    SkeletonOutputMsg::Ptr out_msg_ptr(new SkeletonOutputMsg);

    auto start = std::chrono::high_resolution_clock::now();

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;

    // 重置状态
    data_state_.Reset();

    // 调用回调函数（释放锁后调用以避免死锁）
    std::function<void(const SkeletonOutputMsg::Ptr&)> callback;
    {
      std::lock_guard<std::mutex> callback_lock(output_msg_mtx_);
      callback = output_msg_callback_;
    }

    if (callback)
      callback(out_msg_ptr);

    RINFO << name() << "Successfully published skeleton message, time consumption: " << duration.count() << " ms";
  }

} // namespace robosense::skeleton_detection