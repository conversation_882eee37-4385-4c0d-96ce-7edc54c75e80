//
// Created by sti on 2025/6/13.
//

#include "infrared_motion/ros2_production.h"
#include <Eigen/src/Core/Matrix.h>
#include <Eigen/src/Geometry/Quaternion.h>
#include <functional>

namespace robosense {
namespace motion_capture {

void Ros2Production::init() {
  AINFO << name() << ": start init...";
  const auto& cfg_node = rally::ConfigureManager::getInstance().getCfgNode();

  auto tracker_calib = cfg_node["sensor"]["tracker"];
  auto left_neck = tracker_calib["neck"];
  /*AINFO << "000";*/
  float left_x = left_neck["x"].as<float>();
  float left_y = left_neck["y"].as<float>();
  float left_z = left_neck["z"].as<float>();
  float left_qx = left_neck["qx"].as<float>();
  float left_qy = left_neck["qy"].as<float>();
  float left_qz = left_neck["qz"].as<float>();
  float left_qw = left_neck["qw"].as<float>();
  /*AINFO << "111";*/
  Eigen::Quaternionf left_quat(left_qw, left_qx, left_qy, left_qz);
  left_world2neck_.block<3,3>(0,0) = left_quat.matrix();
  left_world2neck_.coeffRef(0,3) = left_x;
  left_world2neck_.coeffRef(1,3) = left_y;
  left_world2neck_.coeffRef(2,3) = left_z;

  auto right_neck = tracker_calib["neck"];
  /*AINFO << "222";*/
  float right_x = right_neck["x"].as<float>();
  float right_y = right_neck["y"].as<float>();
  float right_z = right_neck["z"].as<float>();
  float right_qx = right_neck["qx"].as<float>();
  float right_qy = right_neck["qy"].as<float>();
  float right_qz = right_neck["qz"].as<float>();
  float right_qw = right_neck["qw"].as<float>();
  Eigen::Quaternionf right_quat(right_qw, right_qx, right_qy, right_qz);
  right_world2neck_.block<3,3>(0,0) = right_quat.matrix();
  right_world2neck_.coeffRef(0,3) = right_x;
  right_world2neck_.coeffRef(1,3) = right_y;
  right_world2neck_.coeffRef(2,3) = right_z;

  // world2target_ = {left_x,left_y,left_z,left_qx,left_qy,left_qz,left_qw};
  // auto left_spread_pose = tracker_calib["left_spread_pose"];
  // float left_spread_x = left_spread_pose["x"].as<float>();
  // float left_spread_y = left_spread_pose["y"].as<float>();
  // float left_spread_z = left_spread_pose["z"].as<float>();
  // auto right_spread_pose = tracker_calib["right_spread_pose"];
  // float right_spread_x = right_spread_pose["x"].as<float>();
  // float right_spread_y = right_spread_pose["y"].as<float>();
  // float right_spread_z = right_spread_pose["z"].as<float>();
  offset_z_ = cfg_node["offset_z"].as<float>();
  AINFO << "333";
  tracker_body2base_link_.coeffRef(2,3) = offset_z_;

  // person_center_point_ = {(left_spread_x+right_spread_x) / 2, (left_spread_y + right_spread_y) / 2, (left_spread_z + right_spread_z)/2};


  const auto& ros_cfg_node = cfg_node["ros"];
  auto subscribe_node = ros_cfg_node["subscribe"];
  auto publish_node = ros_cfg_node["publish"];

  left_tracker_sub_ = node_ptr_->create_subscription<geometry_msgs::msg::PoseStamped>(subscribe_node["left_tracker_pose"].as<std::string>(), 10, std::bind(&Ros2Production::leftTrackerPoseCallback, this, std::placeholders::_1));
  right_tracker_sub_ = node_ptr_->create_subscription<geometry_msgs::msg::PoseStamped>(subscribe_node["right_tracker_pose"].as<std::string>(), 10, std::bind(&Ros2Production::rightTrackerPoseCallback, this, std::placeholders::_1));

  std::string glove_type = cfg_node["glove_type"].as<std::string>();
  AINFO << glove_type << std::endl;
  if ("noiton" == glove_type) {
    noiton_hand_pose_sub_ = node_ptr_->create_subscription<geometry_msgs::msg::PoseArray>(
        subscribe_node["noiton_glove_topic"].as<std::string>(),
        rclcpp::SensorDataQoS(),
        std::bind(&Ros2Production::noitonHandPoseCallback, this, std::placeholders::_1));
  } else if ("manus" == glove_type) {
    manus_hand_pose_sub_ = node_ptr_->create_subscription<sensor_msgs::msg::JointState>(
      subscribe_node["manus_glove_topic"].as<std::string>(),
      rclcpp::SensorDataQoS(),
      std::bind(&Ros2Production::manusHandPoseCallback, this, std::placeholders::_1));
  } else {
    AERROR << "glove_type not found";
  }


  /*pose_2d_pub_ = node_ptr_->create_publisher<geometry_msgs::msg::PoseArray>(*/
  /*        publish_node["pose_2d_topic"].as<std::string>(), 10);*/

  pose_3d_pub_ = node_ptr_->create_publisher<geometry_msgs::msg::PoseArray>(
          publish_node["pose_3d_topic"].as<std::string>(), 10);

  /*hand_euler_pub_ = node_ptr_->create_publisher<sensor_msgs::msg::JointState>(*/
  /*        publish_node["hand_euler_topic"].as<std::string>(), 10);*/
  /**/
  /*left_finger_distance_pub_ = node_ptr_->create_publisher<sensor_msgs::msg::Temperature>(*/
  /*        publish_node["left_finger_distance_topic"].as<std::string>(), 10);*/
  /**/
  /*right_finger_distance_pub_ = node_ptr_->create_publisher<sensor_msgs::msg::Temperature>(*/
  /*        publish_node["right_finger_distance_topic"].as<std::string>(), 10);*/

  // 语音交互
  m_HciPublisher_ = 
          node_ptr_->create_publisher<std_msgs::msg::String>("/robot_control/hci_status", 10);
  // 进度条信息
  m_ProgressPublisher_ = 
          node_ptr_->create_publisher<std_msgs::msg::String>("/mqtt/calib_progress", 10);

  pipeline_ptr_ = std::make_shared<PipelineInterface>();
  pipeline_ptr_->init();

  /*if (cfg_node["check_mode"].as<bool>()) {*/
  /*  pipeline_ptr_->regOpticalCallback(*/
  /*    std::bind(&Ros2Production::publishCheckHci, this, std::placeholders::_1));*/
  /*} else if (cfg_node["calib_mode"].as<bool>()) {*/
  /*  pipeline_ptr_->regOpticalCallback(*/
  /*    std::bind(&Ros2Production::publishCalibHci, this, std::placeholders::_1));*/
  /*}*/

  /*if (!cfg_node["calib_mode"].as<bool>()) {*/
    // 只有在非标定模式下才发布结果
  pipeline_ptr_->regOpticalCallback(
          std::bind(&Ros2Production::publishPose, this, std::placeholders::_1));
  AINFO << name() << ": finish init.";
}

void Ros2Production::noitonHandPoseCallback(const geometry_msgs::msg::PoseArray::SharedPtr data) {
  JointPose::Ptr joint_pose_ptr = std::make_shared<JointPose>();
  joint_pose_ptr->timestamp = data->header.stamp.sec * 1e9 + data->header.stamp.nanosec;
  joint_pose_ptr->data->resize(data->poses.size());
  for (size_t i = 0; i < data->poses.size(); ++i) {
    joint_pose_ptr->data->at(i) = Eigen::Quaternionf(data->poses[i].orientation.w,
                                                     data->poses[i].orientation.x,
                                                     data->poses[i].orientation.y,
                                                     data->poses[i].orientation.z);
  }
  pipeline_ptr_->addSensorData("hand_pose", joint_pose_ptr);
}

void Ros2Production::manusHandPoseCallback(const sensor_msgs::msg::JointState::SharedPtr data) {
  JointPose2::Ptr joint_pose_ptr = std::make_shared<JointPose2>();
  joint_pose_ptr->timestamp = data->header.stamp.sec * 1e9 + data->header.stamp.nanosec;
  joint_pose_ptr->data->resize(data->position.size());
  for (size_t i = 0; i < data->position.size(); ++i) {
    joint_pose_ptr->data->at(i) = data->position[i];
  }
  pipeline_ptr_->addSensorData("hand_pose", joint_pose_ptr);
}

void Ros2Production::leftTrackerPoseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr data){
  JointPose2::Ptr joint_pose_ptr = std::make_shared<JointPose2>();
  joint_pose_ptr->timestamp = data->header.stamp.sec * 1e9 + data->header.stamp.nanosec;
  joint_pose_ptr->data->resize(7);
  joint_pose_ptr->data->at(0)= data->pose.position.x;
  joint_pose_ptr->data->at(1)= data->pose.position.y;
  joint_pose_ptr->data->at(2)= data->pose.position.z;
  joint_pose_ptr->data->at(3) = data->pose.orientation.x;
  joint_pose_ptr->data->at(4) = data->pose.orientation.y;
  joint_pose_ptr->data->at(5) = data->pose.orientation.z;
  joint_pose_ptr->data->at(6) = data->pose.orientation.w;
  pipeline_ptr_->addTrackerData("left_infrared_tracker", joint_pose_ptr);
}

void Ros2Production::rightTrackerPoseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr data){
  JointPose2::Ptr joint_pose_ptr = std::make_shared<JointPose2>();
  joint_pose_ptr->timestamp = data->header.stamp.sec * 1e9 + data->header.stamp.nanosec;
  joint_pose_ptr->data->resize(7);
  joint_pose_ptr->data->at(0)= data->pose.position.x;
  joint_pose_ptr->data->at(1)= data->pose.position.y;
  joint_pose_ptr->data->at(2)= data->pose.position.z;
  joint_pose_ptr->data->at(3) = data->pose.orientation.x;
  joint_pose_ptr->data->at(4) = data->pose.orientation.y;
  joint_pose_ptr->data->at(5) = data->pose.orientation.z;
  joint_pose_ptr->data->at(6) = data->pose.orientation.w;
  pipeline_ptr_->addTrackerData("right_infrared_tracker", joint_pose_ptr);
}

PointCloud::Ptr Ros2Production::convertRsPointCloudToPointCloud(const robosense_msgs::msg::RsPointCloud::SharedPtr data) {
  // robosense_msgs::msg::RsPointCloud::SharedPtr->sensor_msgs::msg::PointCloud2
  auto cloud_msg = std::make_shared<sensor_msgs::msg::PointCloud2>();
  cloud_msg->header.stamp = data->header.stamp;
  cloud_msg->height = data->height;
  cloud_msg->width = data->width;
  cloud_msg->is_bigendian = data->is_bigendian;
  cloud_msg->point_step = data->point_step;
  cloud_msg->row_step = data->row_step;
  cloud_msg->is_dense = data->is_dense;
  cloud_msg->fields.resize(data->fields.size());
  for (size_t i = 0; i < data->fields.size(); i++) {
    cloud_msg->fields[i].name = std::string(reinterpret_cast<const char*>(data->fields[i].name.data()),
                                            strnlen(reinterpret_cast<const char*>(data->fields[i].name.data()),
                                                    data->fields[i].name.size()));
    cloud_msg->fields[i].offset = data->fields[i].offset;
    cloud_msg->fields[i].datatype = data->fields[i].datatype;
    cloud_msg->fields[i].count = data->fields[i].count;
  }
  size_t data_size = data->row_step * data->height;
  cloud_msg->data.resize(data_size);
  memcpy(cloud_msg->data.data(), data->data.data(), data_size);

  // sensor_msgs::msg::PointCloud2->pcl::PointCloud<pcl::PointXYZ>
  PointCloud::Ptr point_cloud_ptr = std::make_shared<PointCloud>();
  point_cloud_ptr->timestamp = data->header.stamp.sec * 1e9 + data->header.stamp.nanosec;
  pcl::fromROSMsg(*cloud_msg, *point_cloud_ptr->data);
  return point_cloud_ptr;
}

void Ros2Production::start() {
  pipeline_ptr_->start();
//  rclcpp::spin(node_ptr_);
}


void Ros2Production::publishPose(const Msg::Ptr &msg_ptr) {
  auto pose_array_msg = std::make_unique<geometry_msgs::msg::PoseArray>();
  pose_array_msg->header.frame_id = frame_id_;
  pose_array_msg->header.stamp = rclcpp::Time(msg_ptr->timestamp);

  // 0: world pos 2 target neck pose
  geometry_msgs::msg::Pose center_effector_pose;
  center_effector_pose.position.x = 0;
  center_effector_pose.position.y = 0;
  center_effector_pose.position.z = offset_z_;
  center_effector_pose.orientation.x = 0;
  center_effector_pose.orientation.y = 0;
  center_effector_pose.orientation.z = 0;
  center_effector_pose.orientation.w = 1;
  pose_array_msg->poses.push_back(center_effector_pose);

  // 将tracker末端执行器位姿信息编码到消息中
  auto& right_tracker_pose = msg_ptr->output_msg_ptr->end_pose.first;
  auto& left_tracker_pose = msg_ptr->output_msg_ptr->end_pose.second;
  auto& right_hand_pose = msg_ptr->internal_result_ptr->glove_passthrough_pose.first;
  auto& left_hand_pose = msg_ptr->internal_result_ptr->glove_passthrough_pose.second;
  // 右手位置和旋转
  geometry_msgs::msg::Pose right_tracker_effector_pose;
  Eigen::Matrix4f right_pose_matrix = Eigen::Matrix4f::Identity();
  right_pose_matrix.block<3,3>(0,0) = right_tracker_pose.second.matrix();
  //right_pose_matrix.block<3,1>(3,0) = right_tracker_pose.first.matrix();
  right_pose_matrix.coeffRef(0,3) = right_tracker_pose.first(0);
  right_pose_matrix.coeffRef(1,3) = right_tracker_pose.first(1);
  right_pose_matrix.coeffRef(2,3) = right_tracker_pose.first(2);
  /*AINFO<<"right_tracker_pose.first: "<<right_tracker_pose.first;*/
  /*AINFO<<"right_pose_matrix: "<<right_pose_matrix;*/
  Eigen::Matrix4f right_cal_pose = right_world2neck_ * right_pose_matrix;
  /*AINFO<<"right_world2neck_: "<<right_world2neck_;*/
  right_cal_pose = tracker_body2base_link_ * right_cal_pose;
  /*AINFO<<"right_cal_pose: "<<right_cal_pose;*/
  Eigen::Quaternionf right_cal_quat(right_cal_pose.block<3,3>(0,0));
  right_tracker_effector_pose.position.x = right_cal_pose(0,3);
  right_tracker_effector_pose.position.y = right_cal_pose(1,3);
  right_tracker_effector_pose.position.z = right_cal_pose(2,3);
  // 直接使用四元数
  // right_tracker_effector_pose.orientation.x = right_cal_quat.x();
  // right_tracker_effector_pose.orientation.y = right_cal_quat.y();
  // right_tracker_effector_pose.orientation.z = right_cal_quat.z();
  // right_tracker_effector_pose.orientation.w = right_cal_quat.w()
  right_tracker_effector_pose.orientation.x = right_hand_pose.second.x();
  right_tracker_effector_pose.orientation.y = right_hand_pose.second.y();
  right_tracker_effector_pose.orientation.z = right_hand_pose.second.z();
  right_tracker_effector_pose.orientation.w = right_hand_pose.second.w();

  geometry_msgs::msg::Pose left_tracker_effector_pose;
  Eigen::Matrix4f left_pose_matrix = Eigen::Matrix4f::Identity();
  left_pose_matrix.block<3,3>(0,0) = left_tracker_pose.second.matrix();
  //left_pose_matrix.block<3,1>(3,0) = left_tracker_pose.first.matrix();
  left_pose_matrix.coeffRef(0,3) = left_tracker_pose.first(0);
  left_pose_matrix.coeffRef(1,3) = left_tracker_pose.first(1);
  left_pose_matrix.coeffRef(2,3) = left_tracker_pose.first(2);
  /*AINFO<<"left_pose_matrix: "<<left_pose_matrix;*/
  Eigen::Matrix4f left_cal_pose = left_world2neck_ * left_pose_matrix;
  left_cal_pose = tracker_body2base_link_ * left_cal_pose;
  /*AINFO<<"left_cal_pose: "<<left_cal_pose;*/
  Eigen::Quaternionf left_cal_quat(left_cal_pose.block<3,3>(0,0));
  left_tracker_effector_pose.position.x = left_cal_pose(0,3);
  left_tracker_effector_pose.position.y = left_cal_pose(1,3);
  left_tracker_effector_pose.position.z = left_cal_pose(2,3);
  // 直接使用四元数
  // left_tracker_effector_pose.orientation.x = left_cal_quat.x();
  // left_tracker_effector_pose.orientation.y = left_cal_quat.y();
  // left_tracker_effector_pose.orientation.z = left_cal_quat.z();
  // left_tracker_effector_pose.orientation.w = left_cal_quat.w();
  left_tracker_effector_pose.orientation.x = left_hand_pose.second.x();
  left_tracker_effector_pose.orientation.y = left_hand_pose.second.y();
  left_tracker_effector_pose.orientation.z = left_hand_pose.second.z();
  left_tracker_effector_pose.orientation.w = left_hand_pose.second.w();


  geometry_msgs::msg::Pose right_effector_hand_pose;
  right_effector_hand_pose.position.x = right_cal_pose(0,3);
  right_effector_hand_pose.position.y = right_cal_pose(1,3);
  right_effector_hand_pose.position.z = right_cal_pose(2,3);;
  // 直接使用四元数
  right_effector_hand_pose.orientation.x = right_hand_pose.second.x();
  right_effector_hand_pose.orientation.y = right_hand_pose.second.y();
  right_effector_hand_pose.orientation.z = right_hand_pose.second.z();
  right_effector_hand_pose.orientation.w = right_hand_pose.second.w();

  geometry_msgs::msg::Pose left_effector_hand_pose;
  left_effector_hand_pose.position.x = left_cal_pose(0,3);
  left_effector_hand_pose.position.y = left_cal_pose(1,3);
  left_effector_hand_pose.position.z = left_cal_pose(2,3);
  // 直接使用四元数
  left_effector_hand_pose.orientation.x = left_hand_pose.second.x();
  left_effector_hand_pose.orientation.y = left_hand_pose.second.y();
  left_effector_hand_pose.orientation.z = left_hand_pose.second.z();
  left_effector_hand_pose.orientation.w = left_hand_pose.second.w();

  // 添加末端执行器位姿到PoseArray消息
  pose_array_msg->poses.push_back(right_tracker_effector_pose);
  pose_array_msg->poses.push_back(left_tracker_effector_pose);
  pose_array_msg->poses.push_back(right_effector_hand_pose);
  pose_array_msg->poses.push_back(left_effector_hand_pose);
  // 发布消息
  pose_3d_pub_->publish(std::move(pose_array_msg));
  AINFO << name() << ": publish pose.";
}

void Ros2Production::publishHandEuler(const Msg::Ptr &msg_ptr) {
  sensor_msgs::msg::JointState handeuler_msg;
  handeuler_msg.header.frame_id = frame_id_;
  handeuler_msg.header.stamp = rclcpp::Time(msg_ptr->timestamp);
  for (const auto& v : msg_ptr->output_msg_ptr->finger_angles) {
    handeuler_msg.position.push_back(v);
  }
  hand_euler_pub_->publish(handeuler_msg);
  AINFO << name() << ": publish hand euler.";
}

void Ros2Production::publishFingerDistance(const Msg::Ptr &msg_ptr) {
  auto left_msg = std::make_unique<sensor_msgs::msg::Temperature>();
  auto right_msg = std::make_unique<sensor_msgs::msg::Temperature>();
  left_msg->header.frame_id = frame_id_;
  left_msg->header.stamp = rclcpp::Time(msg_ptr->timestamp);
  left_msg->temperature = msg_ptr->output_msg_ptr->left_finger_distance;
  right_msg->header.frame_id = frame_id_;
  right_msg->header.stamp = rclcpp::Time(msg_ptr->timestamp);
  right_msg->temperature = msg_ptr->output_msg_ptr->right_finger_distance;
  left_finger_distance_pub_->publish(std::move(left_msg));
  right_finger_distance_pub_->publish(std::move(right_msg));
  AINFO << name() << ": publish finger distance.";
}

void Ros2Production::publishCheckHci(const Msg::Ptr &msg_ptr) {
  // 发送进度信息
  float cur_progress = msg_ptr->internal_result_ptr->check_progress;
  if (cur_progress > 100.f) {
    cur_progress = 100.f;
  }
  std_msgs::msg::String progress_msg;
  progress_msg.data = "ac_check:"+std::to_string(cur_progress);
  m_ProgressPublisher_->publish(progress_msg);
  RINFO << "===========================当前检查进度: " << cur_progress << "% =========================";

  if ((!pub_check_end_) && (msg_ptr->internal_result_ptr->check_finish)) {
    pub_check_end_ = true;
    std_msgs::msg::String hci_msg;
    hci_msg.data = "check_finish";
    m_HciPublisher_->publish(hci_msg);
    RINFO << name() << ": publish check finish.";
    return;
  }

  if (!pub_check_start_) {
    pub_check_start_ = true;
    std_msgs::msg::String hci_msg;
    hci_msg.data = "check_start";
    m_HciPublisher_->publish(hci_msg);
    RINFO << name() << ": publish check start.";
    return;
  }
}

void Ros2Production::publishCalibHci(const Msg::Ptr &msg_ptr) {
  // 发送进度信息
  float cur_progress = msg_ptr->internal_result_ptr->calib_progress;
  if (cur_progress > 100.f) {
    cur_progress = 100.f;
  }
  std_msgs::msg::String progress_msg;
  progress_msg.data = "bone_calib:"+std::to_string(cur_progress);
  m_ProgressPublisher_->publish(progress_msg);
  RINFO << "===========================当前标定进度: " << cur_progress << "% =========================";

  if ((!pub_calib_end_) && (msg_ptr->internal_result_ptr->calib_finish_map.at(rally::CameraEnum::left_ac_camera) &&
    msg_ptr->internal_result_ptr->calib_finish_map.at(rally::CameraEnum::right_ac_camera))) {
    pub_calib_end_ = true;
    std_msgs::msg::String hci_msg;
    hci_msg.data = "calib_finish";
    m_HciPublisher_->publish(hci_msg);
    RINFO << name() << ": publish bone calib finish.";
    return;
  }

  if (!pub_calib_start_) {
    pub_calib_start_ = true;
    std_msgs::msg::String hci_msg;
    hci_msg.data = "bone_calib_start";
    m_HciPublisher_->publish(hci_msg);
    RINFO << name() << ": publish bone calib start.";
    return;
  }

  if (!msg_ptr->internal_result_ptr->calib_info.empty()) {
    msg_ptr->internal_result_ptr->calib_info = ""; // 防止重复发送
    std_msgs::msg::String hci_msg;
    hci_msg.data = "bone_calib_warning";
    m_HciPublisher_->publish(hci_msg);
    RWARN << name() << ": publish bone calib warning.";
    return;
  }
}

}
}
