#cmake_minimum_required(VERSION 3.15)
#
##========================
## Project
##========================
#
#set(CUR_LIB ros_production)
#project(${CUR_LIB})
#
#set(CMAKE_BUILD_TYPE Release)
#add_definitions(-std=c++14)
#add_compile_options(-W)
#add_compile_options(-std=c++14)
#add_definitions(-DDEBUG_LEVEL=0)


#add_subdirectory(robosense_msgs)
add_subdirectory(rviz_display)
add_subdirectory(production)
add_subdirectory(infrared_motion)
