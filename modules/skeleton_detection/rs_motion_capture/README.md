# 使用说明
rs_motion_capture是一个用于动作捕捉的ros2 package, 支持光学动捕和光惯组合动捕
## 1. 编译
```bash
cd path/to/your/ros2/workspace/
colcon build --packages-select rs_motion_capture
```
## 2. 标定模式
```bash
ros2 launch rs_motion_capture motion_capture_node_launch.py collector:=zed calib_mode:=true
```

## 3. 动捕模式
```bash
ros2 launch rs_motion_capture motion_capture_node_launch.py collector:=zed calib_mode:=false
```

## 4. 双AC标定检查模式
```bash
ros2 launch rs_motion_capture motion_capture_node_launch.py collector:=zed calib_mode:=false check_mode:=true
```
