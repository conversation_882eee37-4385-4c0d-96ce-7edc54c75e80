set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)
set(CMAKE_LIBRARY_ARCHITECTURE aarch64-linux-gnu)

set(CFLAGS "")
set(CXXFLAGS "")

set(CMAKE_SIZEOF_VOID_P 64)

set(CMAKE_C_FLAGS ${CFLAGS} CACHE STRING "" FORCE)
set(CMAKE_CXX_FLAGS ${CXXFLAGS}  CACHE STRING "" FORCE)

set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc-7)
set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++-7)

# set(CMAKE_C_COMPILER_FORCED TRUE)
# set(CMAKE_CXX_COMPILER_FORCED TRUE)
# set(CMAKE_CXX_COMPILER_TARGET ${arch})
set(CMAKE_SYSROOT .)
set(CMAKE_SYSTEM_LIBRARY_PATH ${CMAKE_SYSTEM_LIBRARY_PATH} /usr/lib/aarch64-linux-gnu)

set(CUDA_TOOLKIT_ROOT /usr/local/cuda)
set(CMAKE_CUDA_COMPILER_FORCED TRUE)