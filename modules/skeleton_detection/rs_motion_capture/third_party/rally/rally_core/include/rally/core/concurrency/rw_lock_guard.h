/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RALLY_CORE_CONCURRENCY_RW_LOCK_GUARD_H
#define RALLY_CORE_CONCURRENCY_RW_LOCK_GUARD_H

#include <unistd.h>
#include <atomic>
#include <condition_variable>
#include <cstdint>
#include <cstdlib>
#include <iostream>
#include <mutex>
#include <thread>
#include "rally/utils/details/macros.h"

namespace rally {

template<typename RWLock>
class ReadLockGuard {
public:
    explicit ReadLockGuard(RWLock &lock) : rw_lock_(lock) { rw_lock_.readLock(); }

    ~ReadLockGuard() { rw_lock_.readUnlock(); }

private:
    RALLY_DISALLOW_COPY_AND_ASSIGN(ReadLockGuard)

    RWLock &rw_lock_;
};

template<typename RWLock>
class WriteLockGuard {
public:
    explicit WriteLockGuard(RWLock &lock) : rw_lock_(lock) {
        rw_lock_.writeLock();
    }

    ~WriteLockGuard() { rw_lock_.writeUnlock(); }

private:
    RALLY_DISALLOW_COPY_AND_ASSIGN(WriteLockGuard)

    RWLock &rw_lock_;
};

}  // namespace rally

#endif  // RALLY_CORE_CONCURRENCY_RW_LOCK_GUARD_H
