/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RALLY_CORE_CONTAINERS_DETAILS_TRAITS_H
#define RALLY_CORE_CONTAINERS_DETAILS_TRAITS_H

namespace rally {

/// @brief Determines if you want to left- or right-multiply your matrices with vectors.
/// @details This flag affects the generated transformation matrices. If you want to write M2*M1*v in your code,
/// then choose PRECEDE_VECTOR, if you want v*M1*M2, choose FOLLOW_VECTOR. Matrices generated by Transform, Scale,
/// Rotation and similar functions will match your order of multiplication. (I.e. bottom row is translation if you
/// choose FOLLOW_VECTOR).
enum class eMatrixOrder {
    PRECEDE_VECTOR,
    FOLLOW_VECTOR,
};

/// @brief Determines the memory layout of matrices.
/// @details For ROW_MAJOR layout, the matrix's first row comes first in memory, followed immediately by
/// the second row's elements. For COLUMN_MAJOR matrices, the memory region begins with the first column.
/// This does not affect arithmetic or matrix generator function in any way. Your arithmetic will work
/// the same way if you change this.
enum class eMatrixLayout {
    ROW_MAJOR,
    COLUMN_MAJOR,
};


template <class T, uint32_t Dim>
class ShortArray;

template <class T, uint32_t Rows, uint32_t Columns, eMatrixOrder Order, eMatrixLayout Layout>
class Matrix;

namespace traits {

/// @brief Matrix properties
template <class MatrixT>
class MatrixTraitsHelper {};

template <class T_, uint32_t Rows_, uint32_t Columns_, eMatrixOrder Order_, eMatrixLayout Layout_>
class MatrixTraitsHelper<Matrix<T_, Rows_, Columns_, Order_, Layout_> > {
public:
    using Type = T_;
    static constexpr uint32_t Rows = Rows_;
    static constexpr uint32_t Columns = Columns_;
    static constexpr eMatrixOrder Order = Order_;
    static constexpr eMatrixLayout Layout = Layout_;
};

template <class MatrixT>
class MatrixTraits : public MatrixTraitsHelper<typename std::decay<MatrixT>::type> {};

/// @brief Common utility
template<class T, class U>
using MatMulElemT = decltype(T() * U() + T() * U());

/// @brief Template metaprogramming utilities
template <template <class> class Cond, class... T>
struct All;

template <template <class> class Cond, class Head, class... Rest>
struct All<Cond, Head, Rest...> {
    static constexpr bool value = Cond<Head>::value && All<Cond, Rest...>::value;
};

template <template <class> class Cond>
struct All<Cond> {
    static constexpr bool value = true;
};

/// @brief Decide if type is Scalar, Vector or Matrix.
template <class Arg>
struct isVector {
    static constexpr bool value = false;
};
template <class T, uint32_t Dim>
struct isVector<ShortArray<T, Dim> > {
    static constexpr bool value = true;
};

template <class T>
struct isMatrix {
    static constexpr bool value = false;
};
template <class T, uint32_t Rows, uint32_t Columns, eMatrixOrder Order, eMatrixLayout Layout>
struct isMatrix<Matrix<T, Rows, Columns, Order, Layout> > {
    static constexpr bool value = true;
};

template <class T>
struct isScalar {
    static constexpr bool value = !isMatrix<T>::value && !isVector<T>::value;
};

template<eMatrixOrder Order>
class OppositeOrder {
public:
    static constexpr eMatrixOrder value = (Order == eMatrixOrder::FOLLOW_VECTOR ? eMatrixOrder::PRECEDE_VECTOR
                                                                                : eMatrixOrder::FOLLOW_VECTOR);
};

template<eMatrixLayout Layout>
class OppositeLayout {
public:
    static constexpr eMatrixLayout value = (Layout == eMatrixLayout::ROW_MAJOR ? eMatrixLayout::COLUMN_MAJOR
                                                                               : eMatrixLayout::ROW_MAJOR);
};

}  // namespace traits

}  // namespace rally

#endif  // RALLY_CORE_CONTAINERS_DETAILS_TRAITS_H
