/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RALLY_UTILS_LOGGER_SINKS_BASE_SINK_H
#define RALLY_UTILS_LOGGER_SINKS_BASE_SINK_H

#include <mutex>
#include "rally/utils/details/macros.h"
#include "rally/utils/logger/common.h"
#include "rally/utils/logger/formatter.h"

namespace rally {
namespace log {

class BaseSink {
public:
    using Ptr = std::shared_ptr<BaseSink>;
    BaseSink() = default;
    virtual ~BaseSink() = default;

    virtual void log(const LogMsg &msg);
    virtual void flush();

protected:
    RALLY_DISALLOW_COPY_AND_ASSIGN(BaseSink)
    // sink formatter
    Formatter formatter_;
    std::mutex mutex_;

    virtual void sink_(const LogMsg &msg) = 0;
    virtual void flush_() = 0;
};

}  // namespace log
}  // namespace rally

#endif  // RALLY_UTILS_LOGGER_SINKS_BASE_SINK_H
