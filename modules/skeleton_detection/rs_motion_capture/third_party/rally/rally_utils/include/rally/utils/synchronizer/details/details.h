/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef RALLY_UTILS_SYNCHRONIZER_DETAILS_DETAILS_H
#define RALLY_UTILS_SYNCHRONIZER_DETAILS_DETAILS_H

namespace rally {

namespace details {

// define getTimestamp function check
template<typename T>
struct has_getTimestamp {
    template<typename Class>
    static constexpr bool test(decltype(&Class::getTimestamp) *) {
        return true;
    }

    template<typename>
    static constexpr bool test(...) {
        return false;
    }

    static constexpr bool value = test<T>(nullptr);
};

// define getFrameID function check
template<typename T>
struct has_getFrameID {
    template<typename Class>
    static constexpr bool test(decltype(&Class::getFrameID) *) {
        return true;
    }

    template<typename>
    static constexpr bool test(...) {
        return false;
    }

    static constexpr bool value = test<T>(nullptr);
};

}  // namespace details

}  // namespace rally

#endif  // RALLY_UTILS_SYNCHRONIZER_DETAILS_DETAILS_H
