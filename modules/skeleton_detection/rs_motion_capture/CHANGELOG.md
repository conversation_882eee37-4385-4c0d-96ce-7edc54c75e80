- 2025-06-18 by zed

  commit id: 6ebb1588746b6995098d6f7cf1bbd2f67d4680d9

  修复检测到人未检测到二维码时，多个人的选取逻辑


- 2025-06-18 by zed

  commit id: 192e5e18a8fcaf5f8161816e4cb4920752f23d68

  修复检测到人未检测到二维码时，选取中心点的逻辑为仿射变换上一帧


- 2025-06-18 by zed

  commit id: c3f510ca46c73c60a9ee72a5f4bac212ba82d9d2

  修复未检测到人时，rviz崩溃的问题


- 2025-06-18 by zed

  commit id: 3420e9bdbb44679371dd392407b27160226b26f6

  日志保存路径可配置，如果配置路径不存在，则保存在/tmp目录下


- 2025-06-17 by zed

  commit id: abb45806a43a50183b924fc233b9af4d5a84d9ce
  
  增加传感器数据转换的performance日志，增加系统时延日志


- 2025-06-17 by zed
  
  commit id: 2ac60ed9078155ebb6437b1e42b0c05408cbfef1
  
  修复ctrl+c杀节点时偶现崩溃的问题