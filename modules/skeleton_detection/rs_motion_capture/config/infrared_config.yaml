operator: "eric.li"
check_mode: false
calib_mode: false
debug_mode: true
glove_type: "noiton"  # noiton or manus_glove

sensor:
  tracker: 
    include: sensor/infrared_tracker_calibration.yaml

offset_z: 1.2

ros:
  subscribe:
    left_tracker_pose: /tracker/left/pose
    right_tracker_pose: /tracker/right/pose
    noiton_glove_topic: /hand_poses_noiton
    manus_glove_topic: /hand_poses
  publish:
    pose_3d_topic: /pose_detection/pose_3d
    hand_euler_topic: /pose_detection/hand_euler

pipeline:
  strategy: HybridInfraredInertialMotionCapture
  HybridInfraredInertialMotionCapture:
    infrared:
      strategy: InfraredMotionCapture
      InfraredMotionCapture:
        dynamic_calib: true
    inertial:
      strategy: NoitonGloveInertialMotionCapture
      NoitonGloveInertialMotionCapture:
        xxx: xxx
      ManusGloveInertialMotionCapture:
        xxx: xxx

