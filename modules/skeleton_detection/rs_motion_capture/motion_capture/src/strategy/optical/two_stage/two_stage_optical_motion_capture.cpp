//
// Created by sti on 2025/6/6.
//

#include "motion_capture/strategy/optical/two_stage/two_stage_optical_motion_capture.h"

namespace robosense {
namespace motion_capture {

void TwoStageOpticalMotionCapture::init(const YAML::Node& cfg_node) {
  AINFO << name() << ": start init...";
  YAML::Node od_cfg_node = cfg_node["object_detection"];
  YAML::Node pd_cfg_node = cfg_node["pose_detection"];
  YAML::Node fusion_cfg_node = cfg_node["fusion"];
  YAML::Node check_cfg_node = cfg_node["check"];
  // create stream
  if (cudaStreamCreateWithFlags(&stream_, cudaStreamNonBlocking) != cudaSuccess) {
    RTHROW("Failed to create CUDA stream");
  }
  // bindings
  bindings_ptr_ = std::make_shared<Bindings>();
  // init
  object_detection_ptr_ = std::make_shared<ObjectDetection>(bindings_ptr_, stream_);
  object_detection_ptr_->init(od_cfg_node);
  pose_detection_ptr_ = std::make_shared<PoseDetection>(bindings_ptr_, stream_);
  pose_detection_ptr_->init(pd_cfg_node);

  calib_mode_ = rally::ConfigureManager::getInstance().getCfgNode()["calib_mode"].as<bool>();
  check_mode_ = rally::ConfigureManager::getInstance().getCfgNode()["check_mode"].as<bool>();
  if (check_mode_) {
    check_calibration_ptr_ = std::make_shared<CheckCalibration>();
    check_calibration_ptr_->init(check_cfg_node);
  } else if (calib_mode_) {
    bone_calibration_ptr_ = std::make_shared<BoneCalibration>();
    bone_calibration_ptr_->init();
  } else {
    pose_fusion_optimization_ptr_ = std::make_shared<PoseFusionOptimization>();
    pose_fusion_optimization_ptr_->init(fusion_cfg_node);
  }

  time_recorder_ptr_ = std::make_shared<TimeRecorder>("TwoStageOpticalMotionCapture");

  AINFO << name() << ": finish init.";
}

void TwoStageOpticalMotionCapture::process(const Msg::Ptr& msg_ptr) {
  time_recorder_ptr_->tic();
  AINFO << name() << ": start process...";
  // Process object detection
  object_detection_ptr_->process(msg_ptr);

  // Process pose detection
  pose_detection_ptr_->process(msg_ptr);

  // Perform fusion optimization
  if (check_mode_) {
    check_calibration_ptr_->process(msg_ptr);
  } else if (calib_mode_) {
    bone_calibration_ptr_->process(msg_ptr);
  } else {
    pose_fusion_optimization_ptr_->process(msg_ptr);
  }
  AINFO << name() << ": finish process.";
  time_recorder_ptr_->toc();
}




void TwoStageOpticalMotionCapture::process_one_frame(const std::shared_ptr<Point_Res> &point_res) {
  time_recorder_ptr_->tic();
  AINFO << name() << ": start process...";
  // Process object detection
  object_detection_ptr_->process(msg_ptr);

  // Process pose detection
  pose_detection_ptr_->process(msg_ptr);

  // Perform fusion optimization
  if (check_mode_) {
    check_calibration_ptr_->process(msg_ptr);
  } else if (calib_mode_) {
    bone_calibration_ptr_->process(msg_ptr);
  } else {
    pose_fusion_optimization_ptr_->process(msg_ptr);
  }
  AINFO << name() << ": finish process.";
  time_recorder_ptr_->toc();
  generate2dres();
  for (size_t i = 0;i<7;i++) {
    point_res->camera_left_pts.push_back(msg_ptr->internal_result_ptr->fusion_optimized_camera_key_points_map[rally::CameraEnum::left_ac_camera][i]);
    point_res->camera_right_pts.push_back( msg_ptr->internal_result_ptr->fusion_optimized_camera_key_points_map[rally::CameraEnum::right_ac_camera][i]);
    point_res->world_pts.push_back( msg_ptr->internal_result_ptr->fusion_optimized_world_arm_key_points[i]);
  }
}

void TwoStageOpticalMotionCapture::AddDataImageLeft(const std::shared_ptr<Image> &img_ptr) {
  msg_ptr = std::make_shared<Msg>();
  msg_ptr->use_glove=false;

  auto now = std::chrono::system_clock::now();
  auto ns = std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
  uint64_t timestamp = static_cast<uint64_t>(ns);
  msg_ptr->timestamp=timestamp;
  msg_ptr->input_msg_ptr->image_map[rally::CameraEnum::left_ac_camera] = img_ptr;
}

void TwoStageOpticalMotionCapture::AddDataImageRight(const std::shared_ptr<Image> &img_ptr) {
  msg_ptr->input_msg_ptr->image_map[rally::CameraEnum::right_ac_camera] = img_ptr;
}

void TwoStageOpticalMotionCapture::AddData(const std::shared_ptr<PointCloud> &cloud_ptr) {
  msg_ptr->input_msg_ptr->lidar_map[rally::LidarEnum::left_ac_lidar] = cloud_ptr;
  msg_ptr->input_msg_ptr->lidar_map[rally::LidarEnum::right_ac_lidar] = cloud_ptr;
}

void TwoStageOpticalMotionCapture::generate2dres() {
  Eigen::Matrix4f left_camera2world = SensorManager::getInstance().getCamera2World(rally::CameraEnum::left_ac_camera);
  Eigen::Matrix3f left_camera2world_r = left_camera2world.block<3,3>(0,0);
  Eigen::Vector3f left_camera2world_t = left_camera2world.block<3,1>(0,3);
  Eigen::Matrix4f left_world2camera = Eigen::Matrix4f::Identity();
  left_world2camera.block<3,3>(0,0) = left_camera2world_r.transpose();
  left_world2camera.block<3,1>(0,3) = -left_camera2world_r.transpose() * left_camera2world_t;

  Eigen::Matrix4f right_camera2world = SensorManager::getInstance().getCamera2World(rally::CameraEnum::right_ac_camera);
  Eigen::Matrix3f right_camera2world_r = right_camera2world.block<3,3>(0,0);
  Eigen::Vector3f right_camera2world_t = right_camera2world.block<3,1>(0,3);
  Eigen::Matrix4f right_world2camera = Eigen::Matrix4f::Identity();
  right_world2camera.block<3,3>(0,0) = right_camera2world_r.transpose();
  right_world2camera.block<3,1>(0,3) = -right_camera2world_r.transpose() * right_camera2world_t;
  cv::Mat left_intrinsic = SensorManager::getInstance().getIntrinsic(rally::CameraEnum::left_ac_camera);
  std::vector<Point3f> camera_left_pts;
  for (const auto& pt : msg_ptr->internal_result_ptr->fusion_optimized_world_arm_key_points) {
      Eigen::Vector4f world_pt(pt.x, pt.y, pt.z, 1.0);
      Eigen::Vector4f camera_pt = left_world2camera * world_pt;
      cv::Mat uv1(1, 3, CV_32F);
      uv1.at<float>(0, 0) = camera_pt.x()/camera_pt.z();
      uv1.at<float>(0, 1) = camera_pt.y()/camera_pt.z();
      uv1.at<float>(0, 2) = 1.0;
      cv::Mat cam_coords = (left_intrinsic * uv1.t()).t();
      camera_left_pts.emplace_back(cam_coords.at<float>(0, 0), cam_coords.at<float>(0, 1), 0.0);
  }
  msg_ptr->internal_result_ptr->fusion_optimized_camera_key_points_map[rally::CameraEnum::left_ac_camera] = camera_left_pts;
  cv::Mat right_intrinsic = SensorManager::getInstance().getIntrinsic(rally::CameraEnum::right_ac_camera);
  std::vector<Point3f> camera_right_pts;
  for (const auto& pt : msg_ptr->internal_result_ptr->fusion_optimized_world_arm_key_points) {
      Eigen::Vector4f world_pt(pt.x, pt.y, pt.z, 1.0);
      Eigen::Vector4f camera_pt = right_world2camera * world_pt;
      cv::Mat uv1(1, 3, CV_32F);
      uv1.at<float>(0, 0) = camera_pt.x()/camera_pt.z();
      uv1.at<float>(0, 1) = camera_pt.y()/camera_pt.z();
      uv1.at<float>(0, 2) = 1.0;
      cv::Mat cam_coords = (right_intrinsic * uv1.t()).t();
      camera_right_pts.emplace_back(cam_coords.at<float>(0, 0), cam_coords.at<float>(0, 1), 0.0);
  }
  msg_ptr->internal_result_ptr->fusion_optimized_camera_key_points_map[rally::CameraEnum::right_ac_camera] = camera_right_pts;
}

RS_REGISTER_MOTION_CAPTURE(TwoStageOpticalMotionCapture);

}
}

