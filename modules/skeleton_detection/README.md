# 骨架检测模块 (Skeleton Detection Module)

## 概述

骨架检测模块是一个用于实时人体骨架检测和分析的模块。该模块可以从RGB图像和深度图像中检测出人体关键点，构建完整的骨架结构，并提供可视化输出。

## 功能特性

- **多模态输入支持**: 支持RGB图像和深度图像输入
- **实时检测**: 高效的骨架检测算法，适用于实时应用
- **多人检测**: 可同时检测多个人的骨架
- **关键点识别**: 基于COCO格式的17个关键点检测
- **置信度评估**: 为每个关键点提供置信度评分
- **可视化输出**: 提供带有骨架标注的可视化图像
- **深度融合**: 可选的深度信息融合，提高3D定位精度

## 关键点定义

模块使用COCO格式的17个关键点：

0. 鼻子 (nose)
1. 左眼 (left_eye)
2. 右眼 (right_eye)
3. 左耳 (left_ear)
4. 右耳 (right_ear)
5. 左肩 (left_shoulder)
6. 右肩 (right_shoulder)
7. 左肘 (left_elbow)
8. 右肘 (right_elbow)
9. 左腕 (left_wrist)
10. 右腕 (right_wrist)
11. 左髋 (left_hip)
12. 右髋 (right_hip)
13. 左膝 (left_knee)
14. 右膝 (right_knee)
15. 左踝 (left_ankle)
16. 右踝 (right_ankle)

## 模块结构

```
skeleton_detection/
├── cmake/
│   └── srcs.cmake                    # CMake源文件列表
├── include/
│   └── hyper_vision/
│       └── skeleton_detection/
│           ├── skeleton_detection.h  # 主要类定义
│           └── message/
│               ├── message.h         # 消息头文件
│               └── skeleton_detection_output_msg.h  # 输出消息定义
├── src/
│   └── skeleton_detection/
│       ├── skeleton_detection.cpp    # 主要实现
│       └── message/
│           └── message.cpp           # 消息实现
├── CMakeLists.txt                    # CMake构建配置
└── README.md                         # 本文件
```

## 使用方法

### 1. 初始化

```cpp
#include "hyper_vision/skeleton_detection/skeleton_detection.h"

// 创建配置节点
YAML::Node config;
// ... 设置配置参数

// 创建骨架检测器
auto skeleton_detector = std::make_shared<robosense::skeleton_detection::SkeletonDetection>();

// 初始化
if (skeleton_detector->Init(config) != 0) {
    // 处理初始化失败
}

// 启动
skeleton_detector->Start();
```

### 2. 设置回调函数

```cpp
skeleton_detector->SetCallback([](const robosense::skeleton_detection::SkeletonDetectionOutputMsg::Ptr& msg) {
    std::cout << "检测到 " << msg->total_persons << " 个人" << std::endl;
    
    for (const auto& skeleton : msg->skeletons) {
        std::cout << "人员ID: " << skeleton.person_id 
                  << ", 置信度: " << skeleton.overall_confidence << std::endl;
        
        for (const auto& keypoint : skeleton.keypoints) {
            if (keypoint.confidence > 0.5) {
                std::cout << "关节" << keypoint.joint_id 
                          << ": (" << keypoint.x << ", " << keypoint.y 
                          << "), 置信度: " << keypoint.confidence << std::endl;
            }
        }
    }
});
```

### 3. 输入数据

```cpp
// 添加图像数据
skeleton_detector->AddData(image_frame_ptr);

// 添加深度数据（可选）
skeleton_detector->AddData(depth_frame_ptr);
```

## 配置参数

模块支持以下配置参数：

```yaml
skeleton_detection:
  enable_depth_fusion: true      # 是否启用深度融合
  confidence_threshold: 0.5      # 置信度阈值
  enable_visualization: true     # 是否启用可视化
  model_path: "/path/to/model"   # 模型文件路径
  input_width: 416              # 输入图像宽度
  input_height: 416             # 输入图像高度
```

## 输出数据结构

### SkeletonDetectionOutputMsg

- `header`: 消息头信息
- `skeletons`: 检测到的骨架列表
- `rgb_image`: 处理后的RGB图像（带骨架标注）
- `depth_image`: 深度图像（可选）
- `total_persons`: 检测到的总人数
- `processing_time_ms`: 处理时间（毫秒）

### Skeleton

- `keypoints`: 关键点列表
- `overall_confidence`: 整体置信度
- `person_id`: 人员ID

### KeyPoint

- `x, y, z`: 3D坐标
- `confidence`: 置信度
- `joint_id`: 关节ID

## 依赖项

- OpenCV: 图像处理和可视化
- PCL: 点云处理（用于深度数据）
- rally_utils: 工具库
- common: 通用数据结构

## TODO

- [ ] 集成实际的骨架检测模型（OpenPose、MediaPipe等）
- [ ] 优化性能，支持GPU加速
- [ ] 添加3D骨架重建功能
- [ ] 支持更多的关键点格式
- [ ] 添加骨架跟踪功能
- [ ] 集成姿态分析和动作识别

## 注意事项

当前实现包含模拟数据用于测试。在实际使用时，需要：

1. 集成真实的骨架检测算法
2. 根据具体模型调整输入预处理
3. 优化检测参数以适应特定场景
4. 添加错误处理和异常情况处理