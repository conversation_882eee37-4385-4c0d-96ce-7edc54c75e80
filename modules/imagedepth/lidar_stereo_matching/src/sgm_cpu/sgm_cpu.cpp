#include "sgm_cpu/sgm_cpu.h"
#include "sgm_cpu/delaunator.hpp"
namespace robosense {
namespace listereo
{
void dispToDepth(cv::Mat &_depth_out, const cv::Mat _disp_input, double _baseline, double _focal_length, double _doffs)
{
  _depth_out = cv::Mat::zeros(_disp_input.size(), CV_32FC1);
  for(size_t v=0; v<_disp_input.rows; v++)
  {
    for(size_t u=0; u<_disp_input.cols; u++)
    {
      if(_disp_input.at<float>(v, u)>0)
      {
        _depth_out.at<float>(v, u) = (_baseline * _focal_length) / (_disp_input.at<float>(v, u)+_doffs);
      }
    }
  }
}
void depthToDisp(cv::Mat &_disp_out, const cv::Mat _depth_input, double _baseline, double _focal_length, double _doffs)
{
  _disp_out = cv::Mat::zeros(_depth_input.size(), CV_32FC1);
  for(size_t v=0; v<_depth_input.rows; v++)
  {
    for(size_t u=0; u<_depth_input.cols; u++)
    {
      if(_depth_input.at<float>(v, u)>0)
      {
        _disp_out.at<float>(v, u) = _baseline * _focal_length / _depth_input.at<float>(v, u) - _doffs;
      }
    }
  }
}

inline uint64_t grid_morton(int x, int y) 
{
  uint64_t code = 0;
  for(int i = 0; i < 16; i++) 
  {
    code |= ((x & (1ULL << i)) << i) | ((y & (1ULL << i)) << (i + 1));
  }
  return code;
}
template <typename T>
void sort_by_mortor_code(std::vector<std::pair<cv::Point, T>> &_uv_p3d, const int _x_max, const int _y_max)
{
  std::vector<std::pair<uint64_t, int>> sorted_points;
  sorted_points.reserve(_uv_p3d.size());
  int idx{0};
  for(const auto& [pt, p3d] : _uv_p3d) 
  {
    int rel_x = (pt.x) * 65536 / _x_max;
    int rel_y = (pt.y) * 65536 / _y_max;
    sorted_points.emplace_back(grid_morton(rel_x, rel_y), idx++);
  }
  std::sort(sorted_points.begin(), sorted_points.end(),
    [](const auto& a, const auto& b) 
    {
      return a.first < b.first;
    });
  std::vector<std::pair<cv::Point, T>> tmp;
  tmp.reserve(_uv_p3d.size());
  for(const auto& [code, idx] : sorted_points)
  {
    tmp.emplace_back(_uv_p3d[idx]);
  }
  std::swap(_uv_p3d, tmp);
}
inline int crossProduct(cv::Point a, cv::Point b, cv::Point c) 
{
  return (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
}
std::vector<cv::Point> getPointsInsideTriangle(cv::Point p1, cv::Point p2, cv::Point p3)
{
  std::vector<cv::Point> pts_out;
  
  int minX = std::min({p1.x, p2.x, p3.x});
  int maxX = std::max({p1.x, p2.x, p3.x});
  int minY = std::min({p1.y, p2.y, p3.y});
  int maxY = std::max({p1.y, p2.y, p3.y});
  pts_out.reserve((maxX - minX + 1) * (maxY - minY + 1)/2);

  for(int y = minY; y <= maxY; ++y) 
  {
    for(int x = minX; x <= maxX; ++x) 
    {
      cv::Point p = {x, y};
      int d1 = crossProduct(p1, p2, p);
      int d2 = crossProduct(p2, p3, p);
      int d3 = crossProduct(p3, p1, p);
  
      bool inside = (d1 <= 0 && d2 <= 0 && d3 <= 0) || 
                    (d1 >= 0 && d2 >= 0 && d3 >= 0);
      
      if(inside) 
      {
        pts_out.emplace_back(p);
      }
    }
  }
  return pts_out;
}

typedef __int128_t longer_t;
typedef int64_t fixed_t;
const int32_t FIXED_SCALE_BITS = 32;
const fixed_t FIXED_SCALE = 1LL << FIXED_SCALE_BITS;
const int32_t NORM_SCALE_BITS = 10;
const fixed_t NORMAL_SCALE = 1LL << FIXED_SCALE_BITS;
const fixed_t NORMAL_SCALE_NAG = -1LL << FIXED_SCALE_BITS;
inline fixed_t double_to_fixed(double x) 
{
  return static_cast<fixed_t>(x * FIXED_SCALE);
}
inline double fixed_to_double(fixed_t x) 
{
  return static_cast<double>(x) / FIXED_SCALE;
}
inline fixed_t fixed_mul(fixed_t a, fixed_t b) 
{
  return (static_cast<longer_t>(a) * b) >> FIXED_SCALE_BITS;
}
inline fixed_t fixed_div(fixed_t a, fixed_t b) 
{
  return (static_cast<longer_t>(a) << FIXED_SCALE_BITS) / b;
}
inline fixed_t fixed_abs(fixed_t x) 
{
  return x < 0 ? -x : x;
}
fixed_t fixed_sqrt(fixed_t _x) 
{
  fixed_t a = _x;
  if (_x <= 0) return 0;
  
  fixed_t x = (a > FIXED_SCALE) ? a : FIXED_SCALE;
  fixed_t prev_x = 0;
  
  for (int i = 0; i < 20; i++) 
  {
    prev_x = x;
    fixed_t a_div_x = fixed_div(a, x);
    x = (x + a_div_x) >> 1;
    if (abs(x - prev_x) < 1) break;
  }
  return x;
}
struct FixedVec3 
{
  fixed_t x{0}, y{0}, z{0};

  FixedVec3() = default;

  FixedVec3(fixed_t _x, fixed_t _y, fixed_t _z, bool _need_scale=false) 
  : x(_x), y(_y), z(_z) 
  {
    if(_need_scale)
    {
      x = _x << FIXED_SCALE_BITS;
      y = _y << FIXED_SCALE_BITS;
      z = _z << FIXED_SCALE_BITS;
    }
  }
  
  FixedVec3(double dx, double dy, double dz) 
  : x(double_to_fixed(dx)), y(double_to_fixed(dy)), z(double_to_fixed(dz)) 
  {}
  
  FixedVec3 operator+(const FixedVec3& other) const 
  {
    return {x + other.x, y + other.y, z + other.z};
  }

  FixedVec3 operator-(const FixedVec3& other) const 
  {
    return {x - other.x, y - other.y, z - other.z};
  }
  
  FixedVec3 cross(const FixedVec3& v) const 
  {
    return {
        fixed_mul(y, v.z) - fixed_mul(z, v.y),
        fixed_mul(z, v.x) - fixed_mul(x, v.z),
        fixed_mul(x, v.y) - fixed_mul(y, v.x)
    };
  }
  
  fixed_t dot(const FixedVec3& v) const 
  {
    return fixed_mul(x, v.x) + fixed_mul(y, v.y) + fixed_mul(z, v.z);
  }
  
  void normalize() 
  {
    while(x>NORMAL_SCALE || y>NORMAL_SCALE || z>NORMAL_SCALE ||
      x<NORMAL_SCALE_NAG || y<NORMAL_SCALE_NAG || z<NORMAL_SCALE_NAG)
    {
      x = x >> NORM_SCALE_BITS;
      y = y >> NORM_SCALE_BITS;
      z = z >> NORM_SCALE_BITS;
    }
    while(true)
    {
      fixed_t sq = fixed_mul(x, x) + fixed_mul(y, y) + fixed_mul(z, z);
      if(sq>NORMAL_SCALE)
      {
        x = x >> NORM_SCALE_BITS;
        y = y >> NORM_SCALE_BITS;
        z = z >> NORM_SCALE_BITS;
        continue;
      }
      else
      {
        break;
      }
    }
    fixed_t sq = fixed_mul(x, x) + fixed_mul(y, y) + fixed_mul(z, z);
    fixed_t norm = fixed_sqrt(sq);
    x = fixed_div(x, norm);
    y = fixed_div(y, norm);
    z = fixed_div(z, norm);
  }
  FixedVec3 normalized() const 
  {
    fixed_t x_cp = x;
    fixed_t y_cp = y;
    fixed_t z_cp = z;
    while(x_cp>NORMAL_SCALE || y_cp>NORMAL_SCALE || z_cp>NORMAL_SCALE ||
      x_cp<NORMAL_SCALE_NAG || y_cp<NORMAL_SCALE_NAG || z_cp<NORMAL_SCALE_NAG)
    {
      x_cp = x_cp >> NORM_SCALE_BITS;
      y_cp = y_cp >> NORM_SCALE_BITS;
      z_cp = z_cp >> NORM_SCALE_BITS;
    }
    while(true)
    {
      fixed_t sq = fixed_mul(x_cp, x_cp) + fixed_mul(y_cp, y_cp) + fixed_mul(z_cp, z_cp);
      if(sq>NORMAL_SCALE)
      {
        x_cp = x_cp >> NORM_SCALE_BITS;
        y_cp = y_cp >> NORM_SCALE_BITS;
        z_cp = z_cp >> NORM_SCALE_BITS;
        continue;
      }
      else
      {
        break;
      }
    }
    fixed_t sq = fixed_mul(x_cp, x_cp) + fixed_mul(y_cp, y_cp) + fixed_mul(z_cp, z_cp);
    fixed_t norm = fixed_sqrt(sq);
    return {fixed_div(x_cp, norm), fixed_div(y_cp, norm), fixed_div(z_cp, norm)};
  }
};

struct DynamicProgramming
{
	CostType last_min_;
	std::vector<CostType> dp_;
  int disp_num_;

	DynamicProgramming(int _disp_num) 
  : disp_num_(_disp_num), last_min_(0)
	{
    dp_.resize(disp_num_, 0);
	}

	void update(const CostType* local_costs, CostType p1, CostType p2)
	{
    if(p2<p1)
    {
      p2 = p1;
    }
		CostType lazy_out = 0, local_min = 0;
		{
			CostType out = std::min(CostType(dp_[0] - last_min_), p2);
			out = std::min(out, CostType(dp_[1] - last_min_ + p1));
			lazy_out = local_min = out + local_costs[0];
		}
		for (size_t k = 1; k + 1 < disp_num_; ++k)
		{
			CostType out = std::min(CostType(dp_[k] - last_min_), p2);
			out = std::min(out, CostType(dp_[k - 1] - last_min_ + p1));
			out = std::min(out, CostType(dp_[k + 1] - last_min_ + p1));
			dp_[k - 1] = lazy_out;
			lazy_out = out + local_costs[k];
			local_min = std::min(local_min, lazy_out);
		}
		{
			CostType out = std::min(CostType(dp_[disp_num_ - 1] - last_min_), p2);
			out = std::min(out, CostType(dp_[disp_num_ - 2] - last_min_ + p1));
			dp_[disp_num_ - 2] = lazy_out;
			dp_[disp_num_ - 1] = out + local_costs[disp_num_ - 1];
			local_min = std::min(local_min, dp_[disp_num_ - 1]);
		}
		last_min_ = local_min;
	}

	void reset()
	{
		last_min_ = 0;
		dp_.assign(dp_.size(), 0);
	}
};

void context_error_callback(const char* errinfo, const void* private_info, size_t cb, void* user_data)
{
    std::cout << "opencl error : " << errinfo << std::endl;
}

StereoSGMCPU::StereoSGMCPU(const LiSGMParam &_param)
: param_(_param), width_(_param.img_width), height_(_param.img_height), K_(_param.leftCamK_), baseline_(_param.baseline_), focal_length_(_param.focal_length_), doffs_(_param.doffs_)
{
  clahe_->setClipLimit(_param.clip_limit_);
  clahe_->setTilesGridSize(_param.tile_grid_size_);

  param_.scale_vis_ = std::max(1., double(2.*(2*param_.r_h_sad_+1)*(2*param_.r_w_sad_+1)/(param_.r_h_sad_*param_.r_w_sad_)+1)*(param_.r_h_sad_)*(param_.r_w_sad_)/10.);
}

void StereoSGMCPU::run(cv::Mat &_depth_out, cv::Mat &_depth_vis, cv::Mat &_depth_semi, const cv::Mat _gray_l, const cv::Mat _gray_r, const std::vector<Eigen::Vector3f> &_prior_p3d)
{
  const auto t1 = std::chrono::system_clock::now();
  CostType *cost_vis{nullptr};
  CostType *cost_prior{nullptr};
  CostType *cost_sum{nullptr};
  CostType *cost_agg{nullptr};
  CostType *cost_agg_right{nullptr};

  // Note: 1-a and 1-b can be parallelized
  // 1-a compute depth from lidar prior
  std::vector<Eigen::Vector3i> prior_p3d;
  prior_p3d.reserve(_prior_p3d.size());
  for(auto &p3d:_prior_p3d)
  {
    prior_p3d.emplace_back(Eigen::Vector3i(std::round(p3d[0]*1000), std::round(p3d[1]*1000), std::round(p3d[2]*1000)));
  }
  // 1-b compute cost from visual
  cv::Mat gray_l = _gray_l.clone();
  cv::Mat gray_r = _gray_r.clone();
  preProcess(gray_l, gray_r);
  
  computeDepthFromLidar(_depth_semi, prior_p3d, K_, height_, width_, param_); // 120.336ms

  cv::Mat disp_semi;
  depthToDisp(disp_semi, _depth_semi, baseline_, focal_length_, doffs_);

  computePriorCost(cost_prior, disp_semi);
  

  const auto t2 = std::chrono::system_clock::now();  

  
  computeVisCost(cost_vis, gray_l, gray_r, param_); // 214003ms

  //2. cost sum
  addWeightedCost(cost_sum, cost_vis, cost_prior, param_); // 519.731ms

  //3. aggregate cost
  costAggregation(cost_agg, cost_sum, _gray_l, param_); // 11869.4ms

  computeAggFromLeft(cost_agg_right, cost_agg, param_); // 750.698ms

  //4. compute disp 2002.49ms
  cv::Mat disp_vis, disp_vis_right;
  computeDispFromAgg(disp_vis, cost_agg, param_, true);
  computeDispFromAgg(disp_vis_right, cost_agg_right, param_, false);

  //5. postProcess 41.057ms
  lrConsistencyCheck(disp_vis, disp_vis_right, param_);
  postProcess(_depth_vis, disp_vis, param_);

  // 6. cross check 3.296ms
  crossCheck(_depth_out, _depth_vis, _depth_semi, param_);
   
  // free memory
  if(cost_vis!=nullptr) free(cost_vis);
  if(cost_prior!=nullptr) free(cost_prior);
  if(cost_sum!=nullptr) free(cost_sum);
  if(cost_agg!=nullptr) free(cost_agg);
  if(cost_agg_right!=nullptr) free(cost_agg_right);

  const auto t3 = std::chrono::system_clock::now();
  const auto duration_lidar = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
  const auto duration_vision = std::chrono::duration_cast<std::chrono::milliseconds>(t3 - t2).count();
  const auto duration_all = std::chrono::duration_cast<std::chrono::milliseconds>(t3 - t1).count();
  const double fps = 1e3 / duration_all;

  // for debug
  //cv::Mat debug_8u, debug_color;
  // _depth_out.convertTo(debug_8u, CV_8U, 2);
  // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
  // cv::putText(debug_8u, "lidar_time: " + std::to_string(duration_lidar) + "[ms] " 
  //     "vision_time: " + std::to_string(duration_vision) + "[ms] "
  //     "aio_time: " + std::to_string(duration_all) + "[ms] "+ std::to_string(fps) + "[FPS]",
  //     cv::Point(50, 50), 2, 0.75, cv::Scalar(255, 255, 255));
  // cv::imshow("depth_out", debug_8u); 
  cv::Mat depth_out_save, debug_color;
  _depth_out.convertTo(depth_out_save, CV_32SC1, 1000);
  cv::applyColorMap(depth_out_save, debug_color, cv::COLORMAP_JET);
  cv::imwrite("DepthSample.png", depth_out_save);
  cv::imshow("depth_out_save", debug_color);
  cv::waitKey(0); 
  std::cout << "LidarCost: " << std::to_string(duration_lidar) 
            << " VisionCost: " << std::to_string(duration_vision) 
            << " AIOCost: " << std::to_string(duration_all) 
            << " fps " << fps << std::endl;
}

void StereoSGMCPU::computeDepthFromLidar(cv::Mat &_depth_semi, const std::vector<Eigen::Vector3i> &_prior_p3d, const Eigen::Matrix3d _K, const int _height, const int _width, const LiSGMParam &_param)
{
  if(_depth_semi.empty() || _depth_semi.size() != cv::Size(_width, _height))
  {
    _depth_semi.create(cv::Size(_width, _height), CV_32FC1);
  }
  _depth_semi.setTo(0.0);
  if(_prior_p3d.size()<3)
  {
    return;
  }
  // Step 0 preprocess 3.837 ms
  std::vector<std::pair<cv::Point, Eigen::Vector3i>> uv_p3d;
  cv::Mat idx_mat(_height, _width, CV_32SC1, cv::Scalar(-1));
  for(auto &p3d:_prior_p3d)
  {
    int u = std::round(_K(0,0)*p3d[0]/p3d[2] + _K(0,2));
    int v = std::round(_K(1,1)*p3d[1]/p3d[2] + _K(1,2));
    if(u<0 || u>=_width || v<0 || v>=_height) continue;
    uv_p3d.push_back(std::make_pair(cv::Point(u,v),p3d));
  }
  // 3d点按照Morton码排序，保留局部关联性（二维坐标相近的点相邻），二维索引和一维索引的映射保存在idx_mat中
  sort_by_mortor_code(uv_p3d, _height, _width);
  int idx{0};
  for(auto &[pt,p3d]:uv_p3d)
  {
    idx_mat.at<int32_t>(pt) = idx++;
  }
  // Step 1 triangulation
  std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> triangles = uv2Trianlges(uv_p3d); // 9.995 ms
  // Step 2 filt invalid triangles
  filtInvalidTriangles<int32_t>(triangles, uv_p3d, idx_mat, _param); // 1.656 ms

  // cv::Mat debug_img = cv::Mat::zeros(cv::Size(_width, _height), CV_8UC3);
  // for(const auto &[pt1, pt2, pt3] : triangles) 
  // {
  //   cv::line(debug_img, pt1, pt2, cv::Scalar(0, 255, 0), 1);
  //   cv::line(debug_img, pt2, pt3, cv::Scalar(0, 255, 0), 1);
  //   cv::line(debug_img, pt3, pt1, cv::Scalar(0, 255, 0), 1);
  //   cv::circle(debug_img, pt1, 2, cv::Scalar(255, 0, 0), -1);
  //   cv::circle(debug_img, pt2, 2, cv::Scalar(255, 0, 0), -1);
  //   cv::circle(debug_img, pt3, 2, cv::Scalar(255, 0, 0), -1);
  // }
  // cv::imshow("triangles", debug_img);
  // cv::waitKey(1);
  
  // Step 3 compute semi-depth
  triangleToSemi<int32_t>(_depth_semi, uv_p3d, triangles, idx_mat, _K, height_, width_, _param); // 103.84 ms
}
std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> StereoSGMCPU::uv2Trianlges(std::vector<std::pair<cv::Point, Eigen::Vector3i>> &_uv_p3d) const
{
  
  std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> triangles_out;
  std::vector<double> coords;
  coords.reserve(_uv_p3d.size() * 2);
  for(auto &[pt,p3d]:_uv_p3d)
  {
    coords.push_back(pt.x);
    coords.push_back(pt.y);
  }
  delaunator::Delaunator d(coords);
  triangles_out.reserve(d.triangles.size() / 3);
  for (size_t i = 0; i < d.triangles.size(); i += 3) 
  {
    const size_t i0 = d.triangles[i] * 2;
    const size_t i1 = d.triangles[i + 1] * 2;
    const size_t i2 = d.triangles[i + 2] * 2;
    
    triangles_out.emplace_back(
      std::make_tuple(
        cv::Point(coords[i0], coords[i0 + 1]),
        cv::Point(coords[i1], coords[i1 + 1]),
        cv::Point(coords[i2], coords[i2 + 1])
      )
    );
  }
  
  return std::move(triangles_out);
}

template <typename T>
void StereoSGMCPU::filtInvalidTriangles(std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &_triangles, const std::vector<std::pair<cv::Point, Eigen::Vector3i>> &_uv_p3d, const cv::Mat _idx_map, const LiSGMParam &_param) const
{
  const int r_2d = _param.r_triangle_;
  const int r_3d_max = _param.r_3d_max_*_param.r_3d_max_;
  const int r_3d_min = _param.r_3d_min_*_param.r_3d_min_;
  const int r_3d_k = _param.r_3d_k_*_param.r_3d_k_;
  
  std::vector<bool> is_valid(_triangles.size(), false);
  int idx{0};
  for(const auto &[pt1, pt2, pt3] : _triangles) 
  {
    if(cv::norm(pt1-pt2)<r_2d && cv::norm(pt1-pt3)<r_2d && cv::norm(pt2-pt3)<r_2d)
    {
      const Eigen::Vector3i &p1 = _uv_p3d[_idx_map.at<T>(pt1)].second;
      const Eigen::Vector3i &p2 = _uv_p3d[_idx_map.at<T>(pt2)].second;
      const Eigen::Vector3i &p3 = _uv_p3d[_idx_map.at<T>(pt3)].second;
      
      const int threshold1 = std::min(r_3d_max,std::max(r_3d_min,p1.squaredNorm()/r_3d_k));
      const int threshold2 = std::min(r_3d_max,std::max(r_3d_min,p2.squaredNorm()/r_3d_k));
      const int threshold3 = std::min(r_3d_max,std::max(r_3d_min,p3.squaredNorm()/r_3d_k));
      int l12 = (p1-p2).squaredNorm();
      int l13 = (p1-p3).squaredNorm();
      int l23 = (p2-p3).squaredNorm();
      if(l12<threshold1 && l12<threshold2 && l13<threshold1 && l13<threshold3 && l23<threshold2 && l23<threshold3)
      {
        is_valid[idx] = true;
      }
    }
    idx++;
  }
  idx = 0;
  for(int i=0; i<is_valid.size(); i++)
  {
    if(is_valid[i])
    {
      _triangles[idx++] = _triangles[i];
    }
  }
  _triangles.resize(idx);
}
template <typename T>
void StereoSGMCPU::triangleToSemi(cv::Mat &_depth_semi, const std::vector<std::pair<cv::Point, Eigen::Vector3i>> &_uv_p3d, const std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &_tri_list, cv::Mat _idx_map, const Eigen::Matrix3d _K, const int _height, const int _width, const LiSGMParam &_param)
{
  if(_depth_semi.empty() || _uv_p3d.empty() || _tri_list.empty())
  {
    return;
  }
  const int height = _height;
  const int width = _width;
  const int r = _param.r_prior_;
  const fixed_t ks_fixed[4] = {
    double_to_fixed(1./_K(0,0)), 
    double_to_fixed(1./_K(1,1)), 
    double_to_fixed(-_K(0,2)/_K(0,0)), 
    double_to_fixed(-_K(1,2)/_K(1,1))
    };
  // Step 0 preprocess 0.352 ms
  std::vector<Eigen::Vector3d> normals;
  normals.resize(_uv_p3d.size(), Eigen::Vector3d::Zero());
  std::vector<FixedVec3> normals_fixed;
  normals_fixed.resize(_uv_p3d.size(), FixedVec3());
  std::vector<int> normal_counts;
  normal_counts.resize(_uv_p3d.size(), 0);
  std::vector<std::pair<cv::Point, FixedVec3>> uv_p3d_fixed;
  uv_p3d_fixed.reserve(_uv_p3d.size());
  for(const auto &[pt, p3d]:_uv_p3d)
  {
    uv_p3d_fixed.emplace_back(std::make_pair(pt, FixedVec3(p3d[0], p3d[1], p3d[2], true)));
  }

  std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> uv_p3d_float;
  uv_p3d_float.reserve(_uv_p3d.size());
  for(const auto &[pt, p3d]:_uv_p3d)
  {
    uv_p3d_float.emplace_back(std::make_pair(cv::Point2f(pt), Eigen::Vector3f(p3d[0], p3d[1], p3d[2])));
  }  
  // Step 1 compute depth_semi and normals 69.744 ms
  std::vector<float> depth_semi(height*width, 0.f);
  std::vector<fixed_t> depth_semi_fixed(height*width, 0);
  const double thres_cos = std::cos(_param.thres_angle_/180.*M_PI);
  fixed_t thres_cos_fixed = double_to_fixed(thres_cos);

  for(const auto &[pt1,pt2,pt3]:_tri_list)
  {
    const int idx1 = _idx_map.at<T>(pt1);
    const int idx2 = _idx_map.at<T>(pt2);
    const int idx3 = _idx_map.at<T>(pt3);

    FixedVec3 p1 = uv_p3d_fixed[idx1].second;
    FixedVec3 p2 = uv_p3d_fixed[idx2].second;
    FixedVec3 p3 = uv_p3d_fixed[idx3].second;
    FixedVec3 n = (p2-p1).cross(p3-p1);
    if(0==n.x && 0==n.y && 0==n.z)
    {
      continue;
    }
    // n.x = n.x / 1000000;
    // n.y = n.y / 1000000;
    // n.z = n.z / 1000000;
    n.normalize();
    if(n.z>0)
    {
      n.x *= -1;
      n.y *= -1;
      n.z *= -1;
    }
    // 三角形的法向量n和点p1, p2, p3任意的法向量的夹角小于阈值
    if(std::abs(n.dot(p1.normalized()))<thres_cos_fixed || std::abs(n.dot(p2.normalized()))<thres_cos_fixed || std::abs(n.dot(p3.normalized()))<thres_cos_fixed)
    {
      continue;
    }
    // 二维三角形的边长，选取最长的边作为权重
    int l12 = (pt1-pt2).x*(pt1-pt2).x + (pt1-pt2).y*(pt1-pt2).y; //cv::norm(pt1-pt2, cv::NORM_L2SQR);
    int l13 = (pt1-pt3).x*(pt1-pt3).x + (pt1-pt3).y*(pt1-pt3).y; //cv::norm(pt1-pt3, cv::NORM_L2SQR);
    int l23 = (pt2-pt3).x*(pt2-pt3).x + (pt2-pt3).y*(pt2-pt3).y; //cv::norm(pt2-pt3, cv::NORM_L2SQR);
    int l = std::max(l12, std::max(l13, l23));
    int w = l+1;
    FixedVec3 n_w = n;
    n_w.x /= w;
    n_w.y /= w;
    n_w.z /= w;
    // 将加权的三维法向量累加到每个点的法向量上
    normals_fixed[idx1] = normals_fixed[idx1] + n_w;
    normals_fixed[idx2] = normals_fixed[idx2] + n_w;
    normals_fixed[idx3] = normals_fixed[idx3] + n_w;
    normal_counts[idx1]++;
    normal_counts[idx2]++;
    normal_counts[idx3]++;
    
    // 计算三角形的平面方程
    // ax + by + cz + d = 0
    // a = n.x, b = n.y, c = n.z, d = -n.dot(p1)    
    // a, b, c是三角形平面的法向量的分量
    // p1是三角形的一个顶点
    // n是三角形的法向量
    fixed_t a = fixed_mul(n.x,ks_fixed[0]);
    fixed_t b = fixed_mul(n.y,ks_fixed[1]);
    fixed_t c = n.z + fixed_mul(n.x,ks_fixed[2]) + fixed_mul(n.y,ks_fixed[3]);
    fixed_t d = n.dot(p1);
    if(d<0)
    {
      a *= -1;
      b *= -1;
      c *= -1;
      d *= -1;
    }
    // 遍历三角形内部的点，计算深度值
    // 这里的点是二维平面上的点，深度值是相对于三角形平面的深度值
    // 计算三角形平面上的点的深度值
    // depth_semi_fixed[idx] = d / (a*x + b*y + c);
    // 这里的depth_semi_fixed是一个一维数组，存储每个点的深度值
    // idx是二维点在一维数组中的索引
    // a*x + b*y + c是三角形平面方程的分母
    // d是三角形平面方程的常数项
    // 如果分母为0，则不计算深度值
    // 如果分母大于0，则计算深度值
    // 如果分母小于0，则不计算深度值
    auto pt_list = getPointsInsideTriangle(pt1, pt2, pt3);
    for(auto &pt:pt_list)
    {
      int idx = pt.y*width + pt.x;
      if(depth_semi_fixed[idx]==0)
      {
        fixed_t den = a*pt.x+b*pt.y+c;
        if(den>0)
        {
          depth_semi_fixed[idx] = fixed_div(d,den);
        }
      }
    }
  }
  // Step 2 filt and noramlize noramls 1.68 ms
  for(int i=0; i<normal_counts.size(); i++)
  {
    if(normal_counts[i]>=2)
    {
      normals_fixed[i].normalize();
    }
    else
    {
      normals_fixed[i].x = 0;
      normals_fixed[i].y = 0;
      normals_fixed[i].z = 0;
    }
  }
  //Step 3 filt bad depth 28.001 ms
  for(int i=0; i<_uv_p3d.size(); i++)
  {
    FixedVec3 p0 = uv_p3d_fixed[i].second;
    FixedVec3 n = normals_fixed[i];
    int v = uv_p3d_fixed[i].first.y;
    int u = uv_p3d_fixed[i].first.x;
    int idx = v*width + u;
    depth_semi_fixed[idx] = p0.z;
    if(2*n.dot(n)<FIXED_SCALE)
    {
      continue;
    }
    fixed_t a = fixed_mul(n.x,ks_fixed[0]);
    fixed_t b = fixed_mul(n.y,ks_fixed[1]);
    fixed_t c = n.z + fixed_mul(n.x,ks_fixed[2]) + fixed_mul(n.y,ks_fixed[3]);
    fixed_t d = -n.dot(p0);
    
    for(int dv=-r; dv<=r; dv++)
    {
      for(int du=-r; du<=r; du++)
      {
        int v1 = v+dv;
        int u1 = u+du;
        if(v1<0 || v1>=height || u1<0 || u1>=width) continue;

        if(_idx_map.at<T>(v1,u1)>=0) continue;
        fixed_t &z = depth_semi_fixed[v1*width+u1];
        if(z>0)
        {
          fixed_t delta = std::abs(fixed_mul(z,(a*u1+b*v1+c))+d);
          if(delta>double_to_fixed(100.))
          {
            z = 0;
          }
        }
      }
    }
  }
  //Step 4 convert depth-results from fixed_t to float and [mm] to [m] 3.958 ms
  if(_depth_semi.empty() || _depth_semi.size() != cv::Size(_width, _height))
  {
    _depth_semi.create(cv::Size(_width, _height), CV_32FC1);
  }
  for(int i=0; i<depth_semi.size(); i++)
  {
    _depth_semi.ptr<float>(0)[i] = fixed_to_double(depth_semi_fixed[i]/1000);
  }

  
  
  // cv::Mat debug_8u, debug_color;
  // _depth_semi.convertTo(debug_8u, CV_8U, 10.0);
  // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
  // cv::imshow("prior depth", debug_8u);
  //cv::waitKey(0);
}

void StereoSGMCPU::computePriorCost(CostType* &cost_prior_, const cv::Mat _disp_semi)
{
  if(nullptr == cost_prior_)
  {
    cost_prior_ = (CostType*)aligned_alloc(SIMD_WIDTH, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  }
  memset(cost_prior_, 0, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  for(int v=0; v<height_; v++)
  {
    for(int u=0; u<width_; u++)
    {
      float d_ref = _disp_semi.at<float>(v,u);
      if(d_ref>0.f)
      {
        for(int d=0; d<param_.num_disp_; d++)
        {
          float delta = std::abs(d_ref-d);
          if(delta>param_.r_pd_)
          {
            cost_prior_[v*width_*param_.num_disp_+u*param_.num_disp_+d] = param_.pd2_;
          }
          else
          {
            cost_prior_[v*width_*param_.num_disp_+u*param_.num_disp_+d] = param_.pd1_ * delta * delta;
          }
        }
      }
    }
  }
}

void StereoSGMCPU::preProcess(cv::Mat _gray_l, cv::Mat _gray_r)
{
  // cv::equalizeHist(_gray_l, _gray_l);
	// cv::equalizeHist(_gray_r, _gray_r);
  // clahe_->apply(_gray_l, _gray_l);
	// clahe_->apply(_gray_r, _gray_r);
}
void StereoSGMCPU::computeVisCost(CostType* &cost_vis_, const cv::Mat _gray_l, const cv::Mat _gray_r, const LiSGMParam &_param)
{
  if(nullptr == cost_vis_)
  {
    cost_vis_ = (CostType*)aligned_alloc(SIMD_WIDTH, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  }
  memset(cost_vis_, 0, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  const int min_disp = param_.min_disp_;
  const int num_disp = param_.num_disp_;
  const int r_h_sad = param_.r_h_sad_;
  const int r_w_sad = param_.r_w_sad_;
  const uint16_t max_gray_cost = param_.max_pg_[0];
  const uint16_t max_grad_cost = param_.max_pg_[1];
  const uint16_t weight_gray = param_.weight_gray_;
  const uint16_t weight_grad = param_.weight_grad_;

  cv::Mat dx_l, dx_r;
  cv::Sobel(_gray_l, dx_l, CV_16S, 1, 0);
  cv::Sobel(_gray_r, dx_r, CV_16S, 1, 0);

  for(int v=0; v<height_; v++)
  {
    for(int u=0; u<width_; u++)
    {
      CostType* C = cost_vis_ + v*width_*num_disp + u*num_disp;
      for(int d=0; d<param_.num_disp_; d++)
      {
        int u_r = u-d-param_.min_disp_;
        for(int dv = -r_h_sad; dv<=r_h_sad; dv++)
        {
          for(int du = -r_w_sad; du<=r_w_sad; du++)
          {
            int v_new = std::max(0, std::min(height_-1, v+dv));
            int u_new = std::max(0, std::min(width_-1, u+du));
            int u_r_new = std::max(0, std::min(width_-1, u_r+du));
            CostType gray_cost = std::abs(_gray_l.at<uchar>(v_new, u_new) - _gray_r.at<uchar>(v_new, u_r_new));
            CostType grad_cost = std::abs(dx_l.at<int16_t>(v_new, u_new) - dx_r.at<int16_t>(v_new, u_r_new));
            C[d] += weight_gray*std::min(gray_cost, max_gray_cost) + weight_grad*std::min(grad_cost, max_grad_cost);
          }
        }
        C[d] /= param_.scale_vis_;
      }
    }
  }
}

void StereoSGMCPU::addWeightedCost(CostType* &_cost_sum, const CostType* const cost_vis_, const CostType* const cost_prior_, const LiSGMParam &_param)
{
  if(nullptr == _cost_sum)
  {
    _cost_sum = (CostType*)aligned_alloc(SIMD_WIDTH, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  }
  memset(_cost_sum, 0, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  const uint16_t weight_shift = param_.weight_shift_;
  const uint16_t den = 1<<weight_shift;
  uint16_t alpha = std::min(param_.weight_depth_, den);
  uint16_t beta = den-alpha;
  for(int i=0; i<height_*width_*param_.num_disp_; i++)
  {
    _cost_sum[i] = uint32_t(beta*cost_vis_[i] + alpha*cost_prior_[i]) >> weight_shift;
  }
}
void StereoSGMCPU::costAggregation(CostType* &_cost_agg, const CostType* const _cost_sum, const cv::Mat _gray, const LiSGMParam &_param)
{
  if(nullptr == _cost_agg)
  {
    _cost_agg = (CostType*)aligned_alloc(SIMD_WIDTH, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  }
  memset(_cost_agg, 0, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  const int width_da = width_*param_.num_disp_;
  CostType p1 = param_.p1_;
  CostType p2 = param_.p2_;
  DynamicProgramming dp(param_.num_disp_);
  // up2down & down2up
  int last_gray{-1};
  for(int dir=-1; dir<=1; dir+=2)
  {
    const int v_start = dir>0 ? 0 : height_-1;
    const int v_end = height_ -1 - v_start + dir;
    for(int u=0; u<width_; u++)
    {
      dp.reset();
      last_gray = -1;
      for(int v=v_start; v!=v_end; v+=dir)
      {
        const CostType* local_costs = _cost_sum + v*width_da + u*param_.num_disp_;
        CostType* agg_costs = _cost_agg + v*width_da + u*param_.num_disp_;
        int diff = 1;
        if(last_gray>=0 && _param.k_diff_>0)
        {
          diff = std::max(1,std::abs(_gray.at<uint8_t>(v,u) - last_gray)/_param.k_diff_);
        }
        last_gray = _gray.at<uint8_t>(v,u);
        dp.update(local_costs, p1, p2/diff);
        for (size_t d = 0; d < param_.num_disp_; ++d) 
        {
          agg_costs[d] += dp.dp_[d];
        }
      }
    }
  }
  // left2right & right2left
  for(int dir=-1; dir<=1; dir+=2)
  {
    const int u_start = dir > 0 ? 0 : width_ - 1;
    const int u_end = width_ - 1 - u_start + dir;
    for(int v=0; v<height_; v++) 
    {
      dp.reset();
      last_gray = -1;
      for(int u=u_start; u!=u_end; u+=dir)
      {
        const CostType* local_costs = _cost_sum + v*width_da + u*param_.num_disp_;
        CostType* agg_costs = _cost_agg + v*width_da + u*param_.num_disp_;
        int diff = 1;
        if(last_gray>=0 && _param.k_diff_>0)
        {
          diff = std::max(1,std::abs(_gray.at<uint8_t>(v,u) - last_gray)/_param.k_diff_);
        }
        last_gray = _gray.at<uint8_t>(v,u);
        dp.update(local_costs, p1, p2/diff);
        for (size_t d = 0; d < param_.num_disp_; ++d) 
        {
          agg_costs[d] += dp.dp_[d];
        }
      }
    }
  }

  // upleft2downright & downright2upleft & upright2downleft & downleft2upright
  for(int v_dir=-1; v_dir<=1; v_dir+=2)
  {
    for(int u_dir=-1; u_dir<=1; u_dir+=2)
    {
      const int v_start = v_dir >0 ? 0 : height_ - 1;
      const int v_end = height_ - 1 - v_start + v_dir;
      const int u_start = u_dir > 0 ? 0 : width_ - 1;
      const int u_end = width_ - 1 - u_start + u_dir;

      for(int v0=v_start; v0!=v_end; v0+=v_dir)
      {
        dp.reset();
        last_gray = -1;
        for(int u=u_start, v=v0; u!=u_end; u+=u_dir, v+=v_dir)
        {
          if(v_end==v)
          {
            v = v_start;
            dp.reset();
            last_gray = -1;
          }
          const CostType* local_costs = _cost_sum + v*width_da + u*param_.num_disp_;
          CostType* agg_costs = _cost_agg + v*width_da + u*param_.num_disp_;
          int diff = 1;
          if(last_gray>=0 && _param.k_diff_>0)
          {
            diff = std::max(1,std::abs(_gray.at<uint8_t>(v,u) - last_gray)/_param.k_diff_);
          }
          last_gray = _gray.at<uint8_t>(v,u);
          dp.update(local_costs, p1, p2/diff);
          for (size_t d = 0; d < param_.num_disp_; ++d) 
          {
            agg_costs[d] += dp.dp_[d];
          }
        }
      }
    }
  }
}
void StereoSGMCPU::computeAggFromLeft(CostType* &_cost_agg_right, const CostType* const _cost_agg, const LiSGMParam &_param)
{
  if(nullptr == _cost_agg_right)
  {
    _cost_agg_right = (CostType*)aligned_alloc(SIMD_WIDTH, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  }
  memset(_cost_agg_right, param_.max_cost_, height_ * width_ * param_.num_disp_ * sizeof(CostType));
  const int width_da = width_*param_.num_disp_;
  for(int v=0; v<height_; v++)
  {
    for(int u=0; u<width_; u++)
    {
      const CostType *Sp = _cost_agg + v*width_da + u*param_.num_disp_;
      // CostType *Sp_r = _cost_agg_right + v*width_da + u*param_.num_disp_;
      for(int d=0; d<param_.num_disp_; d++)
      {
        int u_r = u - param_.min_disp_ - d;
        if(u_r>=0)
        {
          _cost_agg_right[v*width_da + u_r*param_.num_disp_ + d] = Sp[d];
        }
      }
    }
  }
}
void StereoSGMCPU::computeDispFromAgg(cv::Mat &_disp_vis, const CostType* const _cost_agg, const LiSGMParam &_param, const bool _is_left)
{
  if(_disp_vis.empty() || _disp_vis.size() != cv::Size(width_, height_))
  {
    _disp_vis.create(cv::Size(width_, height_), CV_16SC1);
  }
  _disp_vis.setTo(0);
  int dir_multi = _is_left ? -1 : 1;
  for(int v=0; v<height_; v++)
  {
    for(int u=0; u<width_; u++)
    {
      const CostType *Sp = _cost_agg + v*width_*param_.num_disp_ + u*param_.num_disp_;
      CostType min_cost = Sp[0];
      DispType best_disp = 0;
      for(int d=1; d<param_.num_disp_; d++)
      {
        CostType cost = Sp[d];
        if(cost < min_cost)
        {
          min_cost = cost;
          best_disp = d;
        }
      }
      bool is_unique{true};
      for(int d=0; d<param_.num_disp_; d++)
      {
        if(std::abs(best_disp-d) > 1 && Sp[d]*(100-param_.uniqueness_ratio_)<min_cost*100)
        {
          is_unique = false;
          break;
        }
      }
      if(!is_unique) continue;
      int u_else = u + dir_multi*(param_.min_disp_+best_disp);
      if(u_else<0 || u_else>=width_) continue;
      if(best_disp>0 && best_disp<param_.num_disp_-1)
      {
        _disp_vis.at<DispType>(v,u) = best_disp;
        int denom2 = std::max(Sp[best_disp-1] + Sp[best_disp+1] - 2*min_cost, 1);
        best_disp = (best_disp << param_.sub_shift_) + 
          ((Sp[best_disp-1]-Sp[best_disp+1]) << param_.sub_shift_) / (denom2<<1);
      }
      else
      {
        best_disp = best_disp << param_.sub_shift_;
      }
      _disp_vis.at<DispType>(v, u) = best_disp + (param_.min_disp_ << param_.sub_shift_);
    }
  }
}
void StereoSGMCPU::lrConsistencyCheck(cv::Mat &_disp_left, const cv::Mat _disp_right, const LiSGMParam &_param)
{
  int disp_scale = 1 << param_.sub_shift_;
  int disp_scale_mod = disp_scale - 1;
  int disp_scale2 = disp_scale >> 1;
  int thres_lr_scale = param_.disp_lr_max_diff_ << param_.sub_shift_;

  for(int v=0; v<height_; v++)
  {
    for(int u=0; u<width_; u++)
    {
      DispType &disp_l = _disp_left.at<DispType>(v,u);
      if(disp_l>0)
      {
        int d_tmp = disp_l;
        if(param_.sub_shift_>0)
        {
          int quotient = d_tmp >> param_.sub_shift_;
          int remainder = d_tmp & disp_scale_mod;
          if(remainder >= disp_scale2)
          {
            quotient++;
          }
          d_tmp = quotient;
        }
        int u_r = u - (param_.min_disp_ + d_tmp);
        if(u_r>=0 && u_r<width_)
        {
          DispType disp_r = _disp_right.at<DispType>(v, u_r);
          if(0==disp_r || std::abs(disp_l-disp_r) > thres_lr_scale)
          {
            disp_l = 0;
          }
        }
        else
        {
          disp_l = 0;
        }
      }
    }
  }
}
void StereoSGMCPU::postProcess(cv::Mat &_depth_vis, cv::Mat &_disp_vis, const LiSGMParam &_param)
{
  const int disp_scale = (1 << param_.sub_shift_);
  //cv::medianBlur(_disp_vis, _disp_vis, 3);
  if(param_.max_speckle_size_ > 0)
  {
    cv::filterSpeckles(_disp_vis, 0, param_.max_speckle_size_,
                      param_.speckle_diff_px_<<param_.sub_shift_);
    _disp_vis.convertTo(_disp_vis, CV_32F, 1. / disp_scale);
    dispToDepth(_depth_vis, _disp_vis, baseline_, focal_length_, doffs_);
    cv::Mat depth_int;
    _depth_vis.convertTo(depth_int, CV_16S, 1000);
    cv::filterSpeckles(depth_int, 0, param_.max_speckle_size_,
                        param_.speckle_diff_mm_);
    depth_int.convertTo(_depth_vis, CV_32F, 1.0/1000.0);
  }
  else
  {
    _disp_vis.convertTo(_disp_vis, CV_32F, 1. / disp_scale);
    dispToDepth(_depth_vis, _disp_vis, baseline_, focal_length_, doffs_);
  }
}
void StereoSGMCPU::crossCheck(cv::Mat &_depth_out, const cv::Mat _depth_vis, const cv::Mat _depth_semi, const LiSGMParam &_param)
{
  _depth_out = _depth_semi.clone();
  for(int v=0; v<height_; v++)
  {
    for(int u=0; u<width_; u++)
    {
      float z_semi = _depth_out.at<float>(v,u);
      float z_vis = _depth_vis.at<float>(v,u);
      if(z_semi>0 && z_vis>0)
      {
        float delta_z = std::abs(z_semi - z_vis);
        if(delta_z>_param.semi_rel_error_*z_semi && baseline_ * focal_length_ * std::abs(z_semi - z_vis) > param_.thres_cross_check_ * z_semi * z_vis)
        {
          _depth_out.at<float>(v,u) = 0.;
        }
      }
      else if(z_vis>0)
      {
        _depth_out.at<float>(v,u) = z_vis;
      }
    }
  }
}

void StereoSGMCPU::computeTriangles(   
  std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &triangles, 
  std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> &uv_p3d, 
  cv::Mat &idx_map,
  const std::vector<Eigen::Vector3f> &prior_p3d,  
  const Eigen::Matrix3d _K, 
  const int _height, 
  const int _width,
  const LiSGMParam &_param)
{
  if(prior_p3d.size()<3 || _height <= 0 || _width <= 0)
  {
    return;
  }
  const auto t0 = std::chrono::system_clock::now();
  std::vector<Eigen::Vector3i> prior_p3d_i;
  prior_p3d_i.reserve(prior_p3d.size());
  std::vector<std::pair<cv::Point, Eigen::Vector3i>> uv_p3d_i;
  for(auto &p3d:prior_p3d)
  {
    Eigen::Vector3i p3d_i = Eigen::Vector3i(std::round(p3d[0]*1000), std::round(p3d[1]*1000), std::round(p3d[2]*1000));
    prior_p3d_i.emplace_back(p3d_i);
    int u = std::round(_K(0,0)*p3d_i[0]/p3d_i[2] + _K(0,2));
    int v = std::round(_K(1,1)*p3d_i[1]/p3d_i[2] + _K(1,2));
    if(u<0 || u>=_width || v<0 || v>=_height) continue;
    uv_p3d_i.push_back(std::make_pair(cv::Point2f(u,v), p3d_i));
  }
  idx_map = cv::Mat(_height, _width, CV_32SC1, cv::Scalar(-1));

  // 3d点按照Morton码排序，保留局部关联性（二维坐标相近的点相邻），二维索引和一维索引的映射保存在idx_mat中
  sort_by_mortor_code(uv_p3d_i, _height, _width);
  int idx{0};
  for(auto &[pt,p3d]:uv_p3d_i)
  {
    idx_map.at<int32_t>(pt) = idx++;
  }
  const auto t1 = std::chrono::system_clock::now();

  // std::vector<std::pair<cv::Point, std::vector<cv::Point>>> neighbor_points;
  // for(int i = 0; i < idx_map.rows; i+=30){
  //   for(int j = 0; j < idx_map.cols; j+=30){
  //     cv::Point pt(j, i);
  //     std::vector<cv::Point> neighbor;
  //     if(idx_map.at<int32_t>(pt) >= 0){
  //       neighbor.push_back(pt);
  //       neighbor_points.push_back(std::make_pair(pt, neighbor));
  //       continue;
  //     }
  //     for (int u = -7; u < 7; u++){
  //       for(int v = -7; v < 7; v++){
  //         int m = i + u;
  //         int n = j + v;
  //         if(u == 0 && v == 0 || (m < 0 || m >=idx_map.rows) || (n < 0 || n >=idx_map.cols) ) continue;          
  //         cv::Point pt_n(n, m);
  //         if(idx_map.at<int32_t>(pt_n) >= 0){
  //           neighbor.push_back(pt_n);
  //         }
  //       }
  //     }      
  //     neighbor_points.push_back(std::make_pair(pt, neighbor));
  //   }
  // }

  // cv::Mat debug_img = cv::Mat::zeros(cv::Size(_width, _height), CV_8UC3);
  // for(auto &[pt, neighbor_pts]: neighbor_points){
  //   // if(neighbor_pts.size() == 1){
  //   //   cv::circle(debug_img, neighbor_pts[0], 3, cv::Scalar(0, 0, 255), -1);
  //   // }
  //   cv::RNG rng(cv::getTickCount());
  //   cv::Scalar random_color = cv::Scalar(
  //       rng.uniform(0, 256), // B
  //       rng.uniform(0, 256), // G
  //       rng.uniform(0, 256)  // R
  //   );
  //   if(neighbor_pts.size() >= 3){
  //     for(auto &pt_n : neighbor_pts)
  //       cv::circle(debug_img, pt_n, 1, random_color, -1);
  //   }
  // }
  // cv::imshow("triangles", debug_img);
  // cv::waitKey(0);
  
  // triangulation
  // std::cout << "triangulation..." << std::endl;
  triangles.clear();
  triangles = uv2Trianlges(uv_p3d_i); // 9.995 ms

  // cv::Mat debug_img = cv::Mat::zeros(cv::Size(_width, _height), CV_8UC3);
  // for(const auto &[pt1, pt2, pt3] : triangles) 
  // {
  //   cv::line(debug_img, pt1, pt2, cv::Scalar(0, 255, 0), 1);
  //   cv::line(debug_img, pt2, pt3, cv::Scalar(0, 255, 0), 1);
  //   cv::line(debug_img, pt3, pt1, cv::Scalar(0, 255, 0), 1);
  //   cv::circle(debug_img, pt1, 2, cv::Scalar(255, 0, 0), -1);
  //   cv::circle(debug_img, pt2, 2, cv::Scalar(255, 0, 0), -1);
  //   cv::circle(debug_img, pt3, 2, cv::Scalar(255, 0, 0), -1);
  // }
  // cv::imshow("triangles", debug_img);
  // cv::waitKey(0);

  filtInvalidTriangles<int32_t>(triangles, uv_p3d_i, idx_map, _param); // 1.656 ms

  // cv::Mat debug_img = cv::Mat::zeros(cv::Size(_width, _height), CV_8UC3);
  // for(const auto &[pt1, pt2, pt3] : triangles) 
  // {
  //   cv::line(debug_img, pt1, pt2, cv::Scalar(0, 255, 0), 1);
  //   cv::line(debug_img, pt2, pt3, cv::Scalar(0, 255, 0), 1);
  //   cv::line(debug_img, pt3, pt1, cv::Scalar(0, 255, 0), 1);
  //   cv::circle(debug_img, pt1, 2, cv::Scalar(255, 0, 0), -1);
  //   cv::circle(debug_img, pt2, 2, cv::Scalar(255, 0, 0), -1);
  //   cv::circle(debug_img, pt3, 2, cv::Scalar(255, 0, 0), -1);
  // }
  // cv::imshow("triangles", debug_img);
  // cv::waitKey(0);

  uv_p3d.reserve(uv_p3d_i.size());
  for(const auto &[pt, p3d]:uv_p3d_i)
  {
    uv_p3d.emplace_back(std::make_pair(cv::Point2f(pt), Eigen::Vector3f(p3d[0] * 0.001f, p3d[1] * 0.001f, p3d[2] * 0.001f)));
  } 
  const auto t2 = std::chrono::system_clock::now();
  // const auto d01 = std::chrono::duration_cast<std::chrono::microseconds>(t1 - t0).count();
  // const auto d12 = std::chrono::duration_cast<std::chrono::microseconds>(t2 - t1).count();

  // // 打印耗时
  // // 标题行
  // std::cout << std::left 
  //             << std::setw(8) << "Steps:" 
  //             << std::setw(15) << "triangles0" 
  //             << std::setw(15) << "triangles1" 
  //             << std::endl;
  // // 数据行
  // std::cout << std::setw(8) << "Costs:" 
  //           << std::setw(15) << (float)d01 / 1000.0f 
  //           << std::setw(15) << (float)d12 / 1000.0f 
  //           << std::endl;
} 
}
}