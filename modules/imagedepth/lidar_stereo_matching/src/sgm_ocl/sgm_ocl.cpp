#if ALG_RUNNING_MODE == 2
#include "sgm_ocl/sgm_ocl.h"
#include <regex>
#include <cmrc/cmrc.hpp>
#include <fstream>
CMRC_DECLARE(ocl_sgm);
namespace robosense {
listereo::StereoSGMOpenCL::StereoSGMOpenCL(cl_context context, 
  cl_device_id device, 
  const listereo::LiSGMParam &params,
  Eigen::Matrix3d K,
  float baseline,
  float focal_length,
  float doffs):
  m_cl_ctx(context), 
  m_cl_device(device),  
  m_params_(params), 
  mm_K_(K),
  mf_baseline_(baseline),
  mf_focal_length_(focal_length),
  mf_doffs_(doffs)
{
  // 创建OpenCL命令队列
  for (size_t i = 0; i < MAX_NUM_PATHS; ++i) {
      cl_int err;
      m_agg_streams_[i] = clCreateCommandQueue(context, device, 0, &err);
      CHECK_OCL_ERROR(err, "Failed to create command queue");
  }

  // 预设图像参数
  int width = params.img_width;
  int height = params.img_height;
  int depth_type = params.depth_type;
  mi_src_pitch_ = width * sizeof(uint8_t);
  mi_dst_pitch_ = width * sizeof(int16_t);

  mf_ks_[0] = 1.f/K(0,0);
  mf_ks_[1] = 1.f/K(1,1); 
  mf_ks_[2] = -K(0,2)/K(0,0);
  mf_ks_[3] = -K(1,2)/K(1,1);
  mf_thres_cos_ = std::cos(params.thres_angle_/180.f*M_PI);

  // 创建OpenCL buffer
  m_d_left_ = clCreateBuffer(context, CL_MEM_READ_WRITE, width * height, nullptr, nullptr);
  m_d_right_ = clCreateBuffer(context, CL_MEM_READ_WRITE, width * height, nullptr, nullptr);
  m_d_disp_ = clCreateBuffer(context, CL_MEM_READ_WRITE, 2 * width * height * sizeof(uint16_t), nullptr, nullptr);
  m_d_census_ = clCreateBuffer(context, CL_MEM_READ_WRITE, 2 * width * height * sizeof(uint32_t), nullptr, nullptr);
  m_d_gx_census_ = clCreateBuffer(context, CL_MEM_READ_WRITE, 2 * width * height * sizeof(uint32_t), nullptr, nullptr);
  m_d_sobel_ = clCreateBuffer(context, CL_MEM_READ_WRITE, 2 * width * height * sizeof(uint8_t), nullptr, nullptr);
  m_d_aggregation_census_cost_ = clCreateBuffer(context, CL_MEM_READ_WRITE, MAX_NUM_PATHS * width * height * params.num_disp_ * sizeof(uint8_t), nullptr, nullptr);  
  m_d_filtered_disp_ = clCreateBuffer(context, CL_MEM_READ_WRITE, 2 * width * height * sizeof(uint16_t), nullptr, nullptr);
  m_d_result_disp_ = clCreateBuffer(context, CL_MEM_READ_WRITE, width * height * sizeof(uint16_t), nullptr, nullptr);
  m_d_result_depth_ = clCreateBuffer(context, CL_MEM_READ_WRITE, width * height * sizeof(uint16_t), nullptr, nullptr);
  m_d_prior_depth_ = clCreateBuffer(context, CL_MEM_READ_WRITE, width * height * sizeof(uint32_t), nullptr, nullptr);
  m_d_prior_disp_ = clCreateBuffer(context, CL_MEM_READ_WRITE, width * height * sizeof(float), nullptr, nullptr);
  m_d_ks_ = clCreateBuffer(context, CL_MEM_READ_WRITE, 4 * sizeof(float), nullptr, nullptr);

  // 利用 DeviceBuffer关联并管理设备内存
  m_d_left_buffer_ = sgm::cl::DeviceBuffer<uint8_t>(m_cl_ctx, width * height, m_d_left_);
  m_d_right_buffer_ = sgm::cl::DeviceBuffer<uint8_t>(m_cl_ctx, width * height, m_d_right_); 
  m_d_aggregation_census_cost_buffer_ = sgm::cl::DeviceBuffer<uint8_t> (m_cl_ctx, MAX_NUM_PATHS * width * height * params.num_disp_, m_d_aggregation_census_cost_);  
  m_d_census_buffer_all_ = sgm::cl::DeviceBuffer<uint32_t> (m_cl_ctx, 2 * width * height, m_d_census_);  
  m_d_gx_census_buffer_all_ = sgm::cl::DeviceBuffer<uint32_t> (m_cl_ctx, 2 * width * height, m_d_gx_census_);
  m_d_sobel_buffer_all_ = sgm::cl::DeviceBuffer<uint8_t> (m_cl_ctx, 2 * width * height, m_d_sobel_);  
  m_d_disp_buffer_all_ = sgm::cl::DeviceBuffer<uint16_t> (m_cl_ctx, 2 * width * height, m_d_disp_);  
  m_d_filtered_disp_buffer_all_ = sgm::cl::DeviceBuffer<uint16_t> (m_cl_ctx, 2 * width * height, m_d_filtered_disp_);  
  m_d_result_disp_buffer_ = sgm::cl::DeviceBuffer<uint16_t> (m_cl_ctx, width * height, m_d_result_disp_); 
  m_d_result_depth_buffer_ = sgm::cl::DeviceBuffer<uint16_t> (m_cl_ctx, width * height, m_d_result_depth_);
  m_d_prior_depth_buffer_ = sgm::cl::DeviceBuffer<uint32_t>(m_cl_ctx, width * height, m_d_prior_depth_);
  m_d_prior_disp_buffer_ = sgm::cl::DeviceBuffer<float> (m_cl_ctx, width * height, m_d_prior_disp_);
  m_d_ks_buffer_ = sgm::cl::DeviceBuffer<float> (m_cl_ctx, 4, m_d_ks_); 

  // 初始化子各个方向的聚合代价缓冲区
  const unsigned int num_paths = MAX_NUM_PATHS;
  const size_t buffer_step = width * height * params.num_disp_;

  m_d_sub_cost_buffers_.resize(num_paths);
  for (unsigned i = 0; i < num_paths; ++i){
      cl_buffer_region region = { buffer_step * i, buffer_step };
      cl_int err;
      m_d_sub_cost_buffers_[i].setBufferData(nullptr, buffer_step, clCreateSubBuffer(m_d_aggregation_census_cost_buffer_.data(),
          CL_MEM_READ_WRITE,
          CL_BUFFER_CREATE_TYPE_REGION,
          &region, &err));
      CHECK_OCL_ERROR(err, "Error creating subbuffer!");
  }

  // 创建子缓冲区以存储左右图像的视差和census变换结果
  size_t disp_buffer_step = width * height * sizeof(uint16_t);
  size_t census_buffer_step = width * height * sizeof(uint32_t);
  size_t sobel_buffer_step = width * height * sizeof(uint8_t);
  m_d_disp_buffer_.resize(2);
  m_d_census_buffer_.resize(2);
  m_d_gx_census_buffer_.resize(2);
  m_d_sobel_buffer_.resize(2);
  m_d_filtered_disp_buffer_.resize(2);
  for (unsigned int i = 0; i < 2; ++i){
      cl_buffer_region region = { disp_buffer_step * i, disp_buffer_step };
      cl_buffer_region region_census = { census_buffer_step * i, census_buffer_step };
      cl_buffer_region region_sobel = { sobel_buffer_step * i, sobel_buffer_step };
      cl_int err;
      m_d_disp_buffer_[i].setBufferData(nullptr, disp_buffer_step, clCreateSubBuffer(m_d_disp_buffer_all_.data(),
          CL_MEM_READ_WRITE,
          CL_BUFFER_CREATE_TYPE_REGION,
          &region, &err));
      m_d_filtered_disp_buffer_[i].setBufferData(nullptr, disp_buffer_step, clCreateSubBuffer(m_d_filtered_disp_buffer_all_.data(),
          CL_MEM_READ_WRITE,
          CL_BUFFER_CREATE_TYPE_REGION,
          &region, &err));
      m_d_census_buffer_[i].setBufferData(nullptr, census_buffer_step, clCreateSubBuffer(m_d_census_buffer_all_.data(),
          CL_MEM_READ_WRITE,
          CL_BUFFER_CREATE_TYPE_REGION,
          &region_census, &err));
      m_d_gx_census_buffer_[i].setBufferData(nullptr, census_buffer_step, clCreateSubBuffer(m_d_gx_census_buffer_all_.data(),
          CL_MEM_READ_WRITE,
          CL_BUFFER_CREATE_TYPE_REGION,
          &region_census, &err));
      m_d_sobel_buffer_[i].setBufferData(nullptr, sobel_buffer_step, clCreateSubBuffer(m_d_sobel_buffer_all_.data(),
          CL_MEM_READ_WRITE,
          CL_BUFFER_CREATE_TYPE_REGION,
          &region_sobel, &err));
      CHECK_OCL_ERROR(err, "Error creating subbuffer!");
  }

  // 初始化OpenCL内核
  if (m_census_kernel == nullptr){
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/census.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    m_census_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_census_kernel = m_census_program_.getKernel("census_transform_kernel");
  }

  if (m_sobel_kernel == nullptr){
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/sobel.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_BLOCK_DIM_X = "#define BLOCK_DIM_X " + std::to_string(BLOCK_DIM_X) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@BLOCK_DIM_X@"), kernel_BLOCK_DIM_X); 
    std::string kernel_BLOCK_DIM_Y = "#define BLOCK_DIM_Y " + std::to_string(BLOCK_DIM_Y) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@BLOCK_DIM_Y@"), kernel_BLOCK_DIM_Y); 
    m_sobel_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_sobel_kernel = m_sobel_program_.getKernel("sobel_kernel");
  }

  if (m_cost_and_aggregation_vertical_kernel == nullptr) {      
    // 读取vertical_cost_aggregation.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/vertical_cost_aggregation.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());

    const unsigned int VERTICAL_SUBGROUP_SIZE = params.num_disp_ / VERTICAL_DP_BLOCK_SIZE;
    std::string kernel_max_disparoty = "#define MAX_DISP " + std::to_string(params.num_disp_) + "\n";    
    std::string kernel_warp_size = "#define WARP_SIZE " + std::to_string(VERTICAL_WARP_SIZE) + "\n";    
    std::string kernel_dp_block_size = "#define DP_BLOCK_SIZE " + std::to_string(VERTICAL_DP_BLOCK_SIZE) + "\n";    
    std::string kernel_SUBGROUP_SIZE = "#define SUBGROUP_SIZE " + std::to_string(VERTICAL_SUBGROUP_SIZE) + "\n";
    std::string kernel_BLOCK_SIZE = "#define BLOCK_SIZE " + std::to_string(VERTICAL_BLOCK_SIZE) + "\n";
    std::string kernel_SIZE = "#define SIZE " + std::to_string(VERTICAL_SUBGROUP_SIZE) + "\n";
    
    kernel = std::regex_replace(kernel, std::regex("@MAX_DISP@"), kernel_max_disparoty);
    kernel = std::regex_replace(kernel, std::regex("@WARP_SIZE@"), kernel_warp_size);
    kernel = std::regex_replace(kernel, std::regex("@DP_BLOCK_SIZE@"), kernel_dp_block_size);    
    kernel = std::regex_replace(kernel, std::regex("@SUBGROUP_SIZE@"), kernel_SUBGROUP_SIZE);
    kernel = std::regex_replace(kernel, std::regex("@SIZE@"), kernel_SIZE);
    kernel = std::regex_replace(kernel, std::regex("@BLOCK_SIZE@"), kernel_BLOCK_SIZE);
    m_cost_and_aggregation_vertical_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_cost_and_aggregation_vertical_kernel = m_cost_and_aggregation_vertical_program_.getKernel("vertical_cost_aggregation_kernel");
  }

  if (m_cost_and_aggregation_horizontal_kernel == nullptr) {      
    // 读取horizontal_cost_aggregation.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/horizontal_cost_aggregation.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    const unsigned int HORIZONTAL_SUBGROUP_SIZE = params.num_disp_ /HORIZONTAL_DP_BLOCK_SIZE;
    std::string kernel_max_disparoty = "#define MAX_DISP " + std::to_string(params.num_disp_) + "\n";    
    std::string kernel_warp_size = "#define WARP_SIZE " + std::to_string(HORIZONTAL_WARP_SIZE) + "\n";    
    std::string kernel_dp_block_size = "#define DP_BLOCK_SIZE " + std::to_string(HORIZONTAL_DP_BLOCK_SIZE) + "\n";    
    std::string kernel_SUBGROUP_SIZE = "#define SUBGROUP_SIZE " + std::to_string(HORIZONTAL_SUBGROUP_SIZE) + "\n";
    std::string kernel_BLOCK_SIZE = "#define BLOCK_SIZE " + std::to_string(HORIZONTAL_BLOCK_SIZE) + "\n";
    std::string kernel_SIZE = "#define SIZE " + std::to_string(HORIZONTAL_SUBGROUP_SIZE) + "\n";
    
    kernel = std::regex_replace(kernel, std::regex("@MAX_DISP@"), kernel_max_disparoty);
    kernel = std::regex_replace(kernel, std::regex("@WARP_SIZE@"), kernel_warp_size);
    kernel = std::regex_replace(kernel, std::regex("@DP_BLOCK_SIZE@"), kernel_dp_block_size);    
    kernel = std::regex_replace(kernel, std::regex("@SUBGROUP_SIZE@"), kernel_SUBGROUP_SIZE);
    kernel = std::regex_replace(kernel, std::regex("@SIZE@"), kernel_SIZE);
    kernel = std::regex_replace(kernel, std::regex("@BLOCK_SIZE@"), kernel_BLOCK_SIZE);
    m_cost_and_aggregation_horizontal_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_cost_and_aggregation_horizontal_kernel = m_cost_and_aggregation_horizontal_program_.getKernel("horizontal_cost_aggregation_kernel");
  }

  if (m_disparity_from_agg_kernel == nullptr) {      
    // 读取disp_from_agg.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/disp_from_agg.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_WARP_SIZE = "#define WARP_SIZE " + std::to_string(DISP_WARP_SIZE) + "\n";
    std::string kernel_ACCUMULATION_PER_THREAD = "#define ACCUMULATION_PER_THREAD " + std::to_string(DISP_ACCUMULATION_PER_THREAD) + "\n";
    std::string kernel_max_disparoty = "#define MAX_DISPARITY " + std::to_string(params.num_disp_) + "\n";
    std::string kernel_NUM_PATHS = "#define NUM_PATHS " + std::to_string(MAX_NUM_PATHS) + "\n";
    std::string kernel_COMPUTE_SUBPIXEL = "#define COMPUTE_SUBPIXEL 1\n";
    std::string kernel_WARPS_PER_BLOCK = "#define WARPS_PER_BLOCK " + std::to_string(DISP_WARPS_PER_BLOCK) + "\n";
    std::string kernel_BLOCK_SIZE = "#define BLOCK_SIZE " + std::to_string(DISP_BLOCK_SIZE) + "\n";
    std::string kernel_SUBPIXEL_SHIFT = "#define SUBPIXEL_SHIFT " + std::to_string(params.sub_shift_) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@WARP_SIZE@"), kernel_WARP_SIZE);
    kernel = std::regex_replace(kernel, std::regex("@ACCUMULATION_PER_THREAD@"), kernel_ACCUMULATION_PER_THREAD);
    kernel = std::regex_replace(kernel, std::regex("@MAX_DISPARITY@"), kernel_max_disparoty);
    kernel = std::regex_replace(kernel, std::regex("@NUM_PATHS@"), kernel_NUM_PATHS);
    kernel = std::regex_replace(kernel, std::regex("@COMPUTE_SUBPIXEL@"), kernel_COMPUTE_SUBPIXEL);
    kernel = std::regex_replace(kernel, std::regex("@WARPS_PER_BLOCK@"), kernel_WARPS_PER_BLOCK);
    kernel = std::regex_replace(kernel, std::regex("@BLOCK_SIZE@"), kernel_BLOCK_SIZE);
    kernel = std::regex_replace(kernel, std::regex("@SUBPIXEL_SHIFT@"), kernel_SUBPIXEL_SHIFT);    
    m_disparity_from_agg_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_disparity_from_agg_kernel = m_disparity_from_agg_program_.getKernel("compute_disparity_from_agg_kernel");
  }

  if (m_median_filter_kernel == nullptr){
    // 读取median_filter.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/median_filter.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    m_median_filter_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_median_filter_kernel = m_median_filter_program_.getKernel("median3x3");
  }

  if (m_consistency_check_kernel == nullptr){
    // 读取check_consistency.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/check_consistency.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_SUBPIXEL_SHIFT = "#define SUBPIXEL_SHIFT " + std::to_string(params.sub_shift_) + "\n";        
    kernel = std::regex_replace(kernel, std::regex("@SUBPIXEL_SHIFT@"), kernel_SUBPIXEL_SHIFT);
    m_consistency_check_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_consistency_check_kernel = m_consistency_check_program_.getKernel("check_consistency_kernel");
  }

  if (m_correct_range_kernel == nullptr){
    // 读取correct_range.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/correct_range.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    m_correct_range_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_correct_range_kernel = m_correct_range_program_.getKernel("correct_range_kernel");
  }
  
  if (m_disp2depth_kernel == nullptr){
    // 读取dd_convert.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/disp2depth.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_DEPTH_FIXED_SCALE = "#define DEPTH_FIXED_SCALE " + std::to_string(DEPTH_FIXED_SCALE) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@DEPTH_FIXED_SCALE@"), kernel_DEPTH_FIXED_SCALE);    
    m_disp2depth_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_disp2depth_kernel = m_disp2depth_program_.getKernel("disp2depth_kernel");
  }

  if(m_depth2disp_kernel == nullptr){
    // 读取dd_convert.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/depth2disp.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_DEPTH_FIXED_SCALE = "#define DEPTH_FIXED_SCALE " + std::to_string(DEPTH_FIXED_SCALE) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@DEPTH_FIXED_SCALE@"), kernel_DEPTH_FIXED_SCALE); 
    m_depth2disp_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_depth2disp_kernel = m_depth2disp_program_.getKernel("depth2disp_kernel");
  }

  if (m_triangle2depth_kernel == nullptr){
    // 读取 triangle2depth.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/triangle2depth.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_DEPTH_FIXED_SCALE = "#define DEPTH_FIXED_SCALE " + std::to_string(DEPTH_FIXED_SCALE) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@DEPTH_FIXED_SCALE@"), kernel_DEPTH_FIXED_SCALE); 
    m_triangle2depth_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_triangle2depth_kernel = m_triangle2depth_program_.getKernel("triangle2depth_kernel");
  }

  if (m_average_normals_kernel == nullptr){
    // 读取 average_normals.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/average_normals.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    m_average_normals_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_average_normals_kernel = m_average_normals_program_.getKernel("average_normals_kernel");
  }

  if (m_filter_bad_prior_depth_kernel == nullptr){
    // 读取 filter_bad_prior_depth.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/filter_bad_prior_depth.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_DEPTH_FIXED_SCALE = "#define DEPTH_FIXED_SCALE " + std::to_string(DEPTH_FIXED_SCALE) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@DEPTH_FIXED_SCALE@"), kernel_DEPTH_FIXED_SCALE); 
    m_filter_bad_prior_depth_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_filter_bad_prior_depth_kernel = m_filter_bad_prior_depth_program_.getKernel("filter_bad_prior_depth_kernel");
  }

  if (m_median_filter_kernel == nullptr){
    // 读取median_filter.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/median_filter.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    m_median_filter_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_median_filter_kernel = m_median_filter_program_.getKernel("median3x3");
  }

  if (m_consistency_check_kernel == nullptr){
    // 读取check_consistency.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/check_consistency.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_SUBPIXEL_SHIFT = "#define SUBPIXEL_SHIFT " + std::to_string(params.sub_shift_) + "\n";        
    kernel = std::regex_replace(kernel, std::regex("@SUBPIXEL_SHIFT@"), kernel_SUBPIXEL_SHIFT);
    m_consistency_check_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_consistency_check_kernel = m_consistency_check_program_.getKernel("check_consistency_kernel");
  }

  if (m_correct_range_kernel == nullptr){
    // 读取correct_range.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/correct_range.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    m_correct_range_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_correct_range_kernel = m_correct_range_program_.getKernel("correct_range_kernel");
  }
  
  if (m_disp2depth_kernel == nullptr){
    // 读取dd_convert.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/disp2depth.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_DEPTH_FIXED_SCALE = "#define DEPTH_FIXED_SCALE " + std::to_string(DEPTH_FIXED_SCALE) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@DEPTH_FIXED_SCALE@"), kernel_DEPTH_FIXED_SCALE);    
    m_disp2depth_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_disp2depth_kernel = m_disp2depth_program_.getKernel("disp2depth_kernel");
  }

  if(m_depth2disp_kernel == nullptr){
    // 读取dd_convert.cl源码文件
    auto fs = cmrc::ocl_sgm::get_filesystem();
    auto kernel_rc = fs.open("src/sgm_ocl/ocl/depth2disp.cl");
    auto kernel = std::string(kernel_rc.begin(), kernel_rc.end());
    std::string kernel_DEPTH_FIXED_SCALE = "#define DEPTH_FIXED_SCALE " + std::to_string(DEPTH_FIXED_SCALE) + "\n";
    kernel = std::regex_replace(kernel, std::regex("@DEPTH_FIXED_SCALE@"), kernel_DEPTH_FIXED_SCALE); 
    m_depth2disp_program_.init(m_cl_ctx, m_cl_device, kernel);
    m_depth2disp_kernel = m_depth2disp_program_.getKernel("depth2disp_kernel");
  }
}

int listereo::StereoSGMOpenCL::run_lidar(const std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> &uv_p3d, const std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &triangles, const cv::Mat &idx_mat, cl_command_queue stream)
{
  // 将内存数据写入opencl设备内存
  int width = m_params_.img_width;
  int height = m_params_.img_height;
  int point_num = uv_p3d.size();
  int triangle_num = triangles.size();  

  // 创建OpenCL缓冲区
  cl_mem d_uv_p3d = clCreateBuffer(m_cl_ctx, CL_MEM_READ_ONLY, point_num * sizeof(std::pair<cv::Point2f, Eigen::Vector3f>), nullptr, nullptr);
  cl_mem d_triangles = clCreateBuffer(m_cl_ctx, CL_MEM_READ_ONLY, triangle_num * sizeof(std::tuple<cv::Point, cv::Point, cv::Point>), nullptr, nullptr);
  cl_mem d_idx_mat = clCreateBuffer(m_cl_ctx, CL_MEM_READ_ONLY, idx_mat.total() * sizeof(int), nullptr, nullptr);
  m_d_normals_ = clCreateBuffer(m_cl_ctx, CL_MEM_READ_WRITE, point_num * 3 * sizeof(float), nullptr, nullptr);
  m_d_normals_fixed_ = clCreateBuffer(m_cl_ctx, CL_MEM_READ_WRITE, point_num * 3 * sizeof(int), nullptr, nullptr);
  m_d_normals_count_ = clCreateBuffer(m_cl_ctx, CL_MEM_READ_WRITE, point_num * sizeof(int), nullptr, nullptr);
  clEnqueueWriteBuffer(stream, d_uv_p3d, CL_TRUE, 0, point_num * sizeof(std::pair<cv::Point2f, Eigen::Vector3f>), uv_p3d.data(), 0, nullptr, nullptr);
  clEnqueueWriteBuffer(stream, d_triangles, CL_TRUE, 0, triangle_num * sizeof(std::tuple<cv::Point, cv::Point, cv::Point>), triangles.data(), 0, nullptr, nullptr);   
  clEnqueueWriteBuffer(stream, d_idx_mat, CL_TRUE, 0, idx_mat.total() * sizeof(int), idx_mat.data, 0, nullptr, nullptr);
  clEnqueueWriteBuffer(stream, m_d_ks_, CL_TRUE, 0, 4 * sizeof(float), mf_ks_, 0, nullptr, nullptr);

  m_d_normals_buffer_ = sgm::cl::DeviceBuffer<float>(m_cl_ctx, point_num * 3, m_d_normals_);
  m_d_normals_buffer_.fillZero(stream);
  m_d_normals_fixed_buffer_ = sgm::cl::DeviceBuffer<int>(m_cl_ctx, point_num * 3, m_d_normals_);
  m_d_normals_fixed_buffer_.fillZero(stream);
  m_d_normals_count_buffer_ = sgm::cl::DeviceBuffer<int>(m_cl_ctx, point_num, m_d_normals_count_);
  m_d_normals_count_buffer_.fillZero(stream);
  m_d_prior_depth_buffer_.fillMax(stream);
  
  // 计算先验深度图
  int err = ComputePriorDepthOCL(stream, d_uv_p3d, d_triangles, d_idx_mat, point_num, triangle_num);
  err = ComputeAverageNormalsOCL(stream, point_num);
  // 过滤坏点
  err = FilterBadPriorDepthOCL(stream, d_uv_p3d, d_idx_mat, point_num);
  // 深度图转换为视差图
  err = Depth2DispOCL(stream);

  // 清空缓存数据
  clReleaseMemObject(d_uv_p3d);
  clReleaseMemObject(d_triangles);
  clReleaseMemObject(d_idx_mat);
  clReleaseMemObject(m_d_normals_);
  clReleaseMemObject(m_d_normals_fixed_);
  clReleaseMemObject(m_d_normals_count_);
  clFinish(stream);
  CHECK_OCL_ERROR(err, "Error run lidar finishing queue");
  return 0; // 返回成功
}

int listereo::StereoSGMOpenCL::run_vision(const cv::Mat &left_src, const cv::Mat &right_src,  cl_command_queue stream)
{  
  // 将内存数据写入opencl设备内存
  int width = left_src.cols;
  int height = left_src.rows;
  clEnqueueWriteBuffer(stream, m_d_left_, true, 0, width * height, left_src.data, 0, nullptr, nullptr);    
  clEnqueueWriteBuffer(stream, m_d_right_, true, 0, width * height, right_src.data, 0, nullptr, nullptr); 

  const auto t0 = std::chrono::system_clock::now();
  int err = ComputeSobelOCL(stream);
  // 计算左右图像的Census变换 
  const auto t1 = std::chrono::system_clock::now();
  for(int i = 0; i< 4; i++){
    err = ComputeCensusOCL(stream, i);
  }
  const auto t2 = std::chrono::system_clock::now();
  // left2right/right2left/up2down/down2up aggregation
  int directions[4][2] = {
      {0, 1},   // left to right
      {0, -1},  // right to left
      {1, 0},   // up to down
      {-1, 0}   // down to up
  };
  
  for (unsigned i = 0; i < MAX_NUM_PATHS; ++i){
      err = ComputeVisionCostAndAggOCL(m_agg_streams_[i], directions[i][0], directions[i][1], i);
  }
  const auto t3 = std::chrono::system_clock::now();
  // 计算视差
  err = ComputeDisparityFromAggOCL(stream); 
  const auto t4 = std::chrono::system_clock::now(); 
  // 视差连续性检查
  err = LRConsistencyCheckCL(stream);
  const auto t5 = std::chrono::system_clock::now(); 
  // 视差中值滤波
  err = MedianFilterOCL(stream, 0);
  const auto t6 = std::chrono::system_clock::now();
  // 视差转为深度
  err = Disp2DepthOCL(stream);
  const auto t7 = std::chrono::system_clock::now();
  //注意：即使不调用clFinish，clEnqueueReadBuffer时仍然需要等待所有操作完成
  clFinish(stream);
  CHECK_OCL_ERROR(err, "Error run vision finishing queue");

  // debug 统计耗时
  // const auto d01 = std::chrono::duration_cast<std::chrono::microseconds>(t1 - t0).count();
  // const auto d12 = std::chrono::duration_cast<std::chrono::microseconds>(t2 - t1).count();
  // const auto d23 = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2).count();
  // const auto d34 = std::chrono::duration_cast<std::chrono::microseconds>(t4 - t3).count();
  // const auto d45 = std::chrono::duration_cast<std::chrono::microseconds>(t5 - t4).count();
  // const auto d56 = std::chrono::duration_cast<std::chrono::microseconds>(t6 - t5).count();
  // const auto d67 = std::chrono::duration_cast<std::chrono::microseconds>(t7 - t6).count();

  // // 打印耗时
  // // 标题行
  // std::cout << std::left 
  //             << std::setw(8) << "Steps:" 
  //             << std::setw(15) << "ComputeSobel" 
  //             << std::setw(15) << "ComputeCensus" 
  //             << std::setw(15) << "Aggregation" 
  //             << std::setw(15) << "ComputeDisp" 
  //             << std::setw(15) << "Consistency"
  //             << std::setw(15) << "MeidianFilter"
  //             << std::setw(15) << "Disp2Depth"
  //             << std::endl;

  //   // 数据行
  //   std::cout << std::setw(8) << "Costs:" 
  //             << std::setw(15) << (float)d01 / 1000.0f 
  //             << std::setw(15) << (float)d12 / 1000.0f 
  //             << std::setw(15) << (float)d23 / 1000.0f
  //             << std::setw(15) << (float)d34 / 1000.0f
  //             << std::setw(15) << (float)d45 / 1000.0f
  //             << std::setw(15) << (float)d56 / 1000.0f
  //             << std::setw(15) << (float)d67 / 1000.0f
  //             << std::endl;

  return err;   
}

int listereo::StereoSGMOpenCL::ComputeSobelOCL(cl_command_queue stream)
{
  if (m_sobel_kernel != nullptr) {
    // 设置opencl内核函数参数
    cl_int err = CL_SUCCESS;
    err |= clSetKernelArg(m_sobel_kernel, 0, sizeof(cl_mem), &m_d_sobel_buffer_[0].data());
    err |= clSetKernelArg(m_sobel_kernel, 1, sizeof(cl_mem), &m_d_sobel_buffer_[1].data());
    err |= clSetKernelArg(m_sobel_kernel, 2, sizeof(cl_mem), &m_d_left_buffer_.data());
    err |= clSetKernelArg(m_sobel_kernel, 3, sizeof(cl_mem), &m_d_right_buffer_.data()); 
    err |= clSetKernelArg(m_sobel_kernel, 4, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_sobel_kernel, 5, sizeof(int), &m_params_.img_height);

    // setup kernels
    size_t local_size[2] = { BLOCK_DIM_X, BLOCK_DIM_Y };

    size_t global_size[2] = {
        (size_t)((m_params_.img_width + BLOCK_DIM_X - 1) / BLOCK_DIM_X * local_size[0]),
        (size_t)((m_params_.img_height + BLOCK_DIM_Y - 1) / BLOCK_DIM_Y * local_size[1])
    };
    err |= clEnqueueNDRangeKernel(stream, m_sobel_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing sobel kernel");
    clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_8UC1);
    // clEnqueueReadBuffer(stream, m_d_sobel_buffer_[1].data(), true, 0, m_params_.img_width * m_params_.img_height* sizeof(uint8_t), debug.data, 0, nullptr, nullptr);
    // cv::Mat debug_8u, debug_color;
    // debug.convertTo(debug_8u, CV_8U, 1.0);
    // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
    // cv::imshow("sobel debug", debug_color);
    // cv::waitKey(0);
    return (int)err; 
  } else {
    return -1;
  }  
}

int listereo::StereoSGMOpenCL::ComputeCensusOCL(cl_command_queue stream, int img_idx)
{
  if (m_census_kernel != nullptr) {
    // 设置opencl内核函数参数
    cl_int err = CL_SUCCESS;
    if(img_idx == 0){
      err |= clSetKernelArg(m_census_kernel, 0, sizeof(cl_mem), &m_d_census_buffer_[img_idx].data());
      err |= clSetKernelArg(m_census_kernel, 1, sizeof(cl_mem), &m_d_left_buffer_.data());
    } else if(img_idx == 1) {
      err |= clSetKernelArg(m_census_kernel, 0, sizeof(cl_mem), &m_d_census_buffer_[img_idx].data());
      err |= clSetKernelArg(m_census_kernel, 1, sizeof(cl_mem), &m_d_right_buffer_.data());
    }
    else if(img_idx == 2) {
      err |= clSetKernelArg(m_census_kernel, 0, sizeof(cl_mem), &m_d_gx_census_buffer_[0].data());
      err |= clSetKernelArg(m_census_kernel, 1, sizeof(cl_mem), &m_d_sobel_buffer_[0].data());
    }
    else if(img_idx == 3) {
      err |= clSetKernelArg(m_census_kernel, 0, sizeof(cl_mem), &m_d_gx_census_buffer_[1].data());
      err |= clSetKernelArg(m_census_kernel, 1, sizeof(cl_mem), &m_d_sobel_buffer_[1].data());
    }
    err |= clSetKernelArg(m_census_kernel, 2, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_census_kernel, 3, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_census_kernel, 4, sizeof(int), &m_params_.img_width);

    // 每个kernel处理 120*16 的图像块
    const int BLOCK_SIZE_CENSUS = 128;
    const int WINDOW_WIDTH = 9;
    const int width_per_block = BLOCK_SIZE_CENSUS - WINDOW_WIDTH + 1; // 120
    const int height_per_block = 16; // 16

    // setup kernels
    // 每个group包含{(width + width_per_block - 1) / width_per_block, (height + height_per_block - 1) / height_per_block}个Items
    size_t global_size[2] = {
        (size_t)((m_params_.img_width + width_per_block - 1) / width_per_block * BLOCK_SIZE_CENSUS),
        (size_t)((m_params_.img_height + height_per_block - 1) / height_per_block)
    };
    //std::cout << "Census global_size " << global_size[0] / BLOCK_SIZE << " * " << BLOCK_SIZE << ", "<< global_size[1] << std::endl;
    
    // 总共有BLOCK_SIZE * 1 个group
    size_t local_size[2] = { BLOCK_SIZE_CENSUS, 1 };
    // std::cout << "Census global_size " << global_size[0] << ", " << global_size[1] << std::endl;
    // std::cout << "Census local_size " << local_size[0] << ", " << local_size[1] << std::endl;
    err |= clEnqueueNDRangeKernel(stream, m_census_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing census kernel");
    return (int)err; 
  } else {
    return -1;
  }  
}

int listereo::StereoSGMOpenCL::ComputeVisionCostAndAggOCL(cl_command_queue stream, int direction_x, int direction_y, int idx)
{
  if (m_cost_and_aggregation_vertical_kernel != nullptr && direction_x == 0 && direction_y != 0) {
    // 设置垂直方向代价聚合opencl内核函数参数
    cl_int err = clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 0, sizeof(cl_mem), &m_d_sub_cost_buffers_[idx].data());
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 1, sizeof(cl_mem), &m_d_census_buffer_[0].data());
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 2, sizeof(cl_mem), &m_d_census_buffer_[1].data());
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 3, sizeof(cl_mem), &m_d_gx_census_buffer_[0].data());
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 4, sizeof(cl_mem), &m_d_gx_census_buffer_[1].data());
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 5, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 6, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 7, sizeof(int), &m_params_.min_disp_);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 8, sizeof(uint16_t), &m_params_.p1_);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 9, sizeof(uint16_t), &m_params_.p2_);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 10, sizeof(int), &direction_x);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 11, sizeof(int), &direction_y);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 12, sizeof(uint16_t), &m_params_.weight_gray_);
    err |= clSetKernelArg(m_cost_and_aggregation_vertical_kernel, 13, sizeof(uint16_t), &m_params_.weight_grad_);
    // 分配一维运算资源
    // 局部工作项大小设置为 1，因为每个工作项处理一整条路径
    int BLOCK_SIZE = VERTICAL_BLOCK_SIZE;
    // 逻辑子组是SIMD的一个子集，通常是一个warp
    int SUBGROUP_SIZE = m_params_.num_disp_ / VERTICAL_DP_BLOCK_SIZE;
    // 每个逻辑子组处理的路径数
    int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

    //setup kernels
    const size_t gdim = (m_params_.img_width + PATHS_PER_BLOCK - 1) / PATHS_PER_BLOCK;
    const size_t bdim = BLOCK_SIZE;
    size_t global_size[1] = { gdim * bdim };
    size_t local_size[1] = { bdim };
    err |= clEnqueueNDRangeKernel(stream, m_cost_and_aggregation_vertical_kernel, 1, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing vision cost & vertical aggregation kernel");
    // for debug
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_8UC4);
    // clEnqueueReadBuffer(stream, m_d_sub_cost_buffers_[idx].data(), true, 0, m_params_.img_width * m_params_.img_height * 4, debug.data, 0, nullptr, nullptr);
    // cv::imshow("vertical path aggregation debug", debug);
    // cv::waitKey(0);
    return (int)err; 
  } else if(m_cost_and_aggregation_horizontal_kernel != nullptr && direction_x != 0 && direction_y == 0){
    // 设置水平方向代价聚合opencl内核函数参数
    cl_int err = clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 0, sizeof(cl_mem), &m_d_sub_cost_buffers_[idx].data());
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 1, sizeof(cl_mem), &m_d_census_buffer_[0].data());
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 2, sizeof(cl_mem), &m_d_census_buffer_[1].data());
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 3, sizeof(cl_mem), &m_d_gx_census_buffer_[0].data());
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 4, sizeof(cl_mem), &m_d_gx_census_buffer_[1].data());
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 5, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 6, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 7, sizeof(int), &m_params_.min_disp_);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 8, sizeof(uint16_t), &m_params_.p1_);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 9, sizeof(uint16_t), &m_params_.p2_);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 10, sizeof(int), &direction_x);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 11, sizeof(int), &direction_y);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 12, sizeof(uint16_t), &m_params_.weight_gray_);
    err |= clSetKernelArg(m_cost_and_aggregation_horizontal_kernel, 13, sizeof(uint16_t), &m_params_.weight_grad_);
    // 分配一维运算资源
    int BLOCK_SIZE = HORIZONTAL_BLOCK_SIZE;
    int SUBGROUP_SIZE = m_params_.num_disp_ / HORIZONTAL_DP_BLOCK_SIZE;
    int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

    //setup kernels
    const size_t gdim = (m_params_.img_height + PATHS_PER_BLOCK - 1) / PATHS_PER_BLOCK;
    const size_t bdim = BLOCK_SIZE;

    size_t global_size[1] = { gdim * bdim };
    size_t local_size[1] = { bdim };
    err |= clEnqueueNDRangeKernel(stream, m_cost_and_aggregation_horizontal_kernel, 1, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing vision cost & horizontal aggregation kernel");
    // for debug
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_8UC4);
    // clEnqueueReadBuffer(stream, m_d_sub_cost_buffers_[idx].data(), true, 0, m_params_.img_width * m_params_.img_height * 4, debug.data, 0, nullptr, nullptr);
    // cv::imshow("vertical path aggregation debug", debug);
    // cv::waitKey(0);
    return (int)err;
  }
  else {
    return -1;
  }  
}

int listereo::StereoSGMOpenCL::ComputeDisparityFromAggOCL(cl_command_queue stream)
{
  if (m_disparity_from_agg_kernel != nullptr)  {
    // 设置opencl内核函数参数
    const uint16_t weight_shift = m_params_.weight_shift_;
    const uint16_t den = 1<<weight_shift;
    uint16_t a = std::min(m_params_.weight_depth_, den);
    uint16_t b = den-a;
    float alpha = (float)(a) / float(den);
    float beta = (float)(b) / float(den);
    //std::cout << "alpha " << alpha << ", " << beta << std::endl;
    cl_int err = clSetKernelArg(m_disparity_from_agg_kernel, 0, sizeof(cl_mem), &m_d_disp_buffer_[0].data());
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 1, sizeof(cl_mem), &m_d_disp_buffer_[1].data());
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 2, sizeof(cl_mem), &m_d_aggregation_census_cost_buffer_.data());  
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 3, sizeof(cl_mem), &m_d_prior_disp_buffer_.data());  
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 4, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 5, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 6, sizeof(int), &m_params_.min_disp_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 7, sizeof(int), &m_params_.num_disp_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 8, sizeof(int), &m_params_.uniqueness_ratio_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 9, sizeof(int), &m_params_.sub_shift_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 10, sizeof(uint16_t), &m_params_.r_pd_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 11, sizeof(uint16_t), &m_params_.pd1_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 12, sizeof(uint16_t), &m_params_.pd2_);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 13, sizeof(float), &alpha);
    err |= clSetKernelArg(m_disparity_from_agg_kernel, 14, sizeof(float), &beta);
    // 分配二维运算资源
    //setup kernels
    size_t global_size[1] = { (m_params_.img_height + DISP_WARPS_PER_BLOCK - 1) / DISP_WARPS_PER_BLOCK * DISP_BLOCK_SIZE };
    size_t local_size[1] = {DISP_BLOCK_SIZE};

    err |= clEnqueueNDRangeKernel(stream, m_disparity_from_agg_kernel, 1, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing disparity from agg kernel");
    return (int)err; 
  } else {
    return -1;
  } 
}

int listereo::StereoSGMOpenCL::MedianFilterOCL(cl_command_queue stream, int idx)
{
  if (m_median_filter_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    err |= clSetKernelArg(m_median_filter_kernel, 0, sizeof(cl_mem), &m_d_result_disp_buffer_.data());
    err |= clSetKernelArg(m_median_filter_kernel, 1, sizeof(cl_mem), &m_d_result_disp_buffer_.data());
    err |= clSetKernelArg(m_median_filter_kernel, 2, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_median_filter_kernel, 3, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_median_filter_kernel, 4, sizeof(int), &m_params_.img_width);

    // 分配二维运算资源
    static constexpr int SIZE = 32;
    size_t local_size[2] = { SIZE, SIZE };
    size_t global_size[2] = {
        ((m_params_.img_width + SIZE - 1) / SIZE) * local_size[0],
        ((m_params_.img_height + SIZE - 1) / SIZE) * local_size[1]
    };

    err |= clEnqueueNDRangeKernel(stream, m_median_filter_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing median filter kernel");
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
    // clEnqueueReadBuffer(stream, m_d_filtered_disp_buffer_[idx].data(), true, 0, m_params_.img_width * m_params_.img_height* sizeof(uint16_t), debug.data, 0, nullptr, nullptr);
    // cv::Mat debug_8u, debug_color;
    // debug.convertTo(debug_8u, CV_8U, 255. / (256 * 16));
    // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
    // cv::imshow("median filter debug", debug_color);
    // cv::waitKey(0);
    return (int)err; 
    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::LRConsistencyCheckCL(cl_command_queue stream)
{
  if (m_consistency_check_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    int sub_pixel = 1;
    err |= clSetKernelArg(m_consistency_check_kernel, 0, sizeof(cl_mem), &m_d_result_disp_buffer_.data());
    err |= clSetKernelArg(m_consistency_check_kernel, 1, sizeof(cl_mem), &m_d_disp_buffer_[0].data());
    err |= clSetKernelArg(m_consistency_check_kernel, 2, sizeof(cl_mem), &m_d_disp_buffer_[1].data());
    err |= clSetKernelArg(m_consistency_check_kernel, 3, sizeof(cl_mem), &m_d_left_buffer_.data());    
    err |= clSetKernelArg(m_consistency_check_kernel, 4, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_consistency_check_kernel, 5, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_consistency_check_kernel, 6, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_consistency_check_kernel, 7, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_consistency_check_kernel, 8, sizeof(int), &sub_pixel);
    err |= clSetKernelArg(m_consistency_check_kernel, 9, sizeof(int), &m_params_.disp_lr_max_diff_);
    err |= clSetKernelArg(m_consistency_check_kernel, 10, sizeof(int), &m_params_.min_disp_);

    // 分配二维运算资源
    static constexpr int SIZE = 32;
    size_t local_size[2] = { SIZE, SIZE };
    size_t global_size[2] = {
        ((m_params_.img_width + SIZE - 1) / SIZE) * local_size[0],
        ((m_params_.img_height + SIZE - 1) / SIZE) * local_size[1]
    };

    err |= clEnqueueNDRangeKernel(stream, m_consistency_check_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing consistency check kernel");
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
    // clEnqueueReadBuffer(stream, m_d_result_disp_buffer_.data(), true, 0, m_params_.img_width * m_params_.img_height* sizeof(uint16_t), debug.data, 0, nullptr, nullptr);
    // cv::Mat debug_8u, debug_color;
    // debug.convertTo(debug_8u, CV_8U, 255. / (256 * 16));
    // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
    // cv::imshow("consistency check debug", debug_color);
    // cv::waitKey(0);
    return (int)err; 
    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::CorrectRangeOCL(cl_command_queue stream)
{
  if (m_correct_range_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    const int scale = 1 << m_params_.sub_shift_;
    const int min_disp_scaled = m_params_.min_disp_ * scale;
    const int invalid_disp_scaled = (m_params_.num_disp_ + 1) * scale;
    err |= clSetKernelArg(m_correct_range_kernel, 0, sizeof(cl_mem), &m_d_result_disp_buffer_.data());   
    err |= clSetKernelArg(m_correct_range_kernel, 1, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_correct_range_kernel, 2, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_correct_range_kernel, 3, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_correct_range_kernel, 4, sizeof(int), &min_disp_scaled);
    err |= clSetKernelArg(m_correct_range_kernel, 5, sizeof(int), &invalid_disp_scaled);

    // 分配二维运算资源
    static constexpr int SIZE = 32;
    size_t local_size[2] = { SIZE, SIZE };
    size_t global_size[2] = {
        ((m_params_.img_width + SIZE - 1) / SIZE) * local_size[0],
        ((m_params_.img_height + SIZE - 1) / SIZE) * local_size[1]
    };

    err |= clEnqueueNDRangeKernel(stream, m_correct_range_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    CHECK_OCL_ERROR(err, "Error enequeuing correct range kernel");
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_16UC1);
    // clEnqueueReadBuffer(stream, m_d_result_disp_buffer_.data(), true, 0, m_params_.img_width * m_params_.img_height* sizeof(uint16_t), debug.data, 0, nullptr, nullptr);
    // cv::Mat debug_8u, debug_color;
    // debug.convertTo(debug_8u, CV_8U, 255. / (256 * 16));
    // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
    // cv::imshow("correct range debug", debug_8u);
    // cv::waitKey(0);
    return (int)err; 
    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::Disp2DepthOCL(cl_command_queue stream)
{
  if (m_disp2depth_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    const float sub_pixel_scale = 1. / (float)(1 << m_params_.sub_shift_);
    err |= clSetKernelArg(m_disp2depth_kernel, 0, sizeof(cl_mem), &m_d_result_depth_buffer_.data());   
    err |= clSetKernelArg(m_disp2depth_kernel, 1, sizeof(cl_mem), &m_d_result_disp_buffer_.data());  
    err |= clSetKernelArg(m_disp2depth_kernel, 2, sizeof(cl_mem), &m_d_prior_depth_buffer_.data());
    err |= clSetKernelArg(m_disp2depth_kernel, 3, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_disp2depth_kernel, 4, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_disp2depth_kernel, 5, sizeof(int), &mf_baseline_);
    err |= clSetKernelArg(m_disp2depth_kernel, 6, sizeof(int), &mf_focal_length_);
    err |= clSetKernelArg(m_disp2depth_kernel, 7, sizeof(int), &mf_doffs_);
    err |= clSetKernelArg(m_disp2depth_kernel, 8, sizeof(int), &sub_pixel_scale);
    err |= clSetKernelArg(m_disp2depth_kernel, 9, sizeof(int), &m_params_.enable_lidar);
    err |= clSetKernelArg(m_disp2depth_kernel, 10, sizeof(float), &m_params_.semi_rel_error_);
    err |= clSetKernelArg(m_disp2depth_kernel, 11, sizeof(float), &m_params_.thres_cross_check_);

    // 分配二维运算资源
    static constexpr int SIZE = 32;
    size_t local_size[2] = { SIZE, SIZE };
    size_t global_size[2] = {
        ((m_params_.img_width + SIZE - 1) / SIZE) * local_size[0],
        ((m_params_.img_height + SIZE - 1) / SIZE) * local_size[1]
    };

    err |= clEnqueueNDRangeKernel(stream, m_disp2depth_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing depth to disp kernel");
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
    // clEnqueueReadBuffer(stream, m_d_result_depth_buffer_.data(), true, 0, m_params_.img_width * m_params_.img_height* sizeof(float), debug.data, 0, nullptr, nullptr);
    // cv::Mat debug_8u, debug_color;
    // debug.convertTo(debug_8u, CV_8U, 255. / (m_params_.num_disp_ * sub_pixel_scale));
    // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
    // cv::imshow("correct range debug", debug_8u);
    // cv::waitKey(0);
    return (int)err; 
    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::Depth2DispOCL(cl_command_queue stream)
{
  if (m_depth2disp_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    const float sub_pixel_scale = 1. / (float)(1 << m_params_.sub_shift_);
    err |= clSetKernelArg(m_depth2disp_kernel, 0, sizeof(cl_mem), &m_d_prior_disp_);   
    err |= clSetKernelArg(m_depth2disp_kernel, 1, sizeof(cl_mem), &m_d_prior_depth_);  
    err |= clSetKernelArg(m_depth2disp_kernel, 2, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_depth2disp_kernel, 3, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_depth2disp_kernel, 4, sizeof(int), &mf_baseline_);
    err |= clSetKernelArg(m_depth2disp_kernel, 5, sizeof(int), &mf_focal_length_);
    err |= clSetKernelArg(m_depth2disp_kernel, 6, sizeof(int), &mf_doffs_);
    err |= clSetKernelArg(m_depth2disp_kernel, 7, sizeof(int), &sub_pixel_scale);

    // 分配二维运算资源
    const int SIZE = 32;
    size_t local_size[2] = { SIZE, SIZE };
    size_t global_size[2] = {
        ((m_params_.img_width + SIZE - 1) / SIZE) * local_size[0],
        ((m_params_.img_height + SIZE - 1) / SIZE) * local_size[1]
    };

    err |= clEnqueueNDRangeKernel(stream, m_depth2disp_kernel, 2, nullptr, global_size, local_size, 0, nullptr, nullptr);
    CHECK_OCL_ERROR(err, "Error enequeuing depth to disp kernel");
    // clFinish(stream);
    // cv::Mat debug(m_params_.img_height, m_params_.img_width, CV_32FC1);
    // clEnqueueReadBuffer(stream, m_d_result_depth_buffer_.data(), true, 0, m_params_.img_width * m_params_.img_height* sizeof(float), debug.data, 0, nullptr, nullptr);
    // cv::Mat debug_8u, debug_color;
    // debug.convertTo(debug_8u, CV_8U, 255. / (m_params_.num_disp_ * sub_pixel_scale));
    // cv::applyColorMap(debug_8u, debug_color, cv::COLORMAP_JET);
    // cv::imshow("correct range debug", debug_8u);
    // cv::waitKey(0);
    return (int)err; 
    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::ComputePriorDepthOCL(cl_command_queue stream, cl_mem &d_uv_p3d, cl_mem &d_triangles, cl_mem &d_idx_mat, int point_num, int triangle_num)
{
  if (m_triangle2depth_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    err |= clSetKernelArg(m_triangle2depth_kernel, 0, sizeof(cl_mem), &m_d_prior_depth_); 
    err |= clSetKernelArg(m_triangle2depth_kernel, 1, sizeof(cl_mem), &m_d_normals_fixed_);
    err |= clSetKernelArg(m_triangle2depth_kernel, 2, sizeof(cl_mem), &m_d_normals_count_); 
    err |= clSetKernelArg(m_triangle2depth_kernel, 3, sizeof(cl_mem), &d_uv_p3d);   
    err |= clSetKernelArg(m_triangle2depth_kernel, 4, sizeof(cl_mem), &d_triangles);
    err |= clSetKernelArg(m_triangle2depth_kernel, 5, sizeof(cl_mem), &d_idx_mat); 
    err |= clSetKernelArg(m_triangle2depth_kernel, 6, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_triangle2depth_kernel, 7, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_triangle2depth_kernel, 8, sizeof(int), &point_num);
    err |= clSetKernelArg(m_triangle2depth_kernel, 9, sizeof(int), &triangle_num);
    err |= clSetKernelArg(m_triangle2depth_kernel, 10, sizeof(int), &mf_thres_cos_);
    err |= clSetKernelArg(m_triangle2depth_kernel, 11, sizeof(cl_mem), &m_d_ks_); 

    // 分配二维运算资源
    static constexpr int SIZE = 1024;
    size_t local_size[1] = { SIZE };
    size_t global_size[1] = { ((triangle_num + SIZE - 1) / SIZE) * local_size[0] };

    err |= clEnqueueNDRangeKernel(stream, m_triangle2depth_kernel, 1, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing triangle to depth kernel");
    return (int)err;    
  } else { 
    return -1;
  }
}

int listereo::StereoSGMOpenCL::ComputeAverageNormalsOCL(cl_command_queue stream, int point_num)
{
  if (m_average_normals_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    err |= clSetKernelArg(m_average_normals_kernel, 0, sizeof(cl_mem), &m_d_normals_); 
    err |= clSetKernelArg(m_average_normals_kernel, 1, sizeof(cl_mem), &m_d_normals_fixed_); 
    err |= clSetKernelArg(m_average_normals_kernel, 2, sizeof(cl_mem), &m_d_normals_count_);
    err |= clSetKernelArg(m_average_normals_kernel, 3, sizeof(int), &point_num);

    // 分配一维运算资源
    static constexpr int SIZE = 1024;
    size_t local_size[1] = { SIZE };
    size_t global_size[1] = { ((point_num + SIZE - 1) / SIZE) * local_size[0] };

    err |= clEnqueueNDRangeKernel(stream, m_average_normals_kernel, 1, nullptr, global_size, local_size, 0, nullptr, nullptr);
    clFinish(stream);
    CHECK_OCL_ERROR(err, "Error enequeuing average normals kernel");
    return (int)err;    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::FilterBadPriorDepthOCL(cl_command_queue stream, cl_mem &d_uv_p3d, cl_mem &d_idx_mat, int point_num)
{
  if (m_filter_bad_prior_depth_kernel != nullptr)  {
    cl_int err = CL_SUCCESS;
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 0, sizeof(cl_mem), &m_d_prior_depth_); 
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 1, sizeof(cl_mem), &d_uv_p3d);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 2, sizeof(cl_mem), &m_d_normals_);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 3, sizeof(cl_mem), &d_idx_mat);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 4, sizeof(int), &m_params_.img_width);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 5, sizeof(int), &m_params_.img_height);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 6, sizeof(int), &point_num);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 7, sizeof(int), &m_params_.r_prior_);
    err |= clSetKernelArg(m_filter_bad_prior_depth_kernel, 8, sizeof(cl_mem), &m_d_ks_);

    // 分配一维运算资源
    static constexpr int SIZE = 1024;
    size_t local_size[1] = { SIZE };
    size_t global_size[1] = { ((point_num + SIZE - 1) / SIZE) * local_size[0] };
    // std::cout << "Run FilterBadPriorDepthOCL" << std::endl;
    
    // 分配二维运算资源
    // const int SIZE = 32;
    // size_t local_size[2] = { SIZE, SIZE };
    // size_t global_size[2] = {
    //     ((m_params_.img_width + SIZE - 1) / SIZE) * local_size[0],
    //     ((m_params_.img_height + SIZE - 1) / SIZE) * local_size[1]
    // };

    err |= clEnqueueNDRangeKernel(stream, m_filter_bad_prior_depth_kernel, 1, nullptr, global_size, local_size, 0, nullptr, nullptr);
    CHECK_OCL_ERROR(err, "Error enequeuing filter bad prior depth kernel");
    return (int)err;    
  } else {
    return -1;
  }
}

int listereo::StereoSGMOpenCL::get_ocl_image(cv::Mat &ret_img, OCLImageType image_type, cl_command_queue stream)
{  
  if(ret_img.empty()) {
    if (image_type == OCLImageType::RET_DISP || image_type == OCLImageType::RET_DEPTH ) {
      ret_img = cv::Mat(m_params_.img_height, m_params_.img_width, CV_16UC1);
    } else if (image_type == OCLImageType::PRIOR_DISP || image_type == OCLImageType::PRIOR_DEPTH) {
      ret_img = cv::Mat(m_params_.img_height, m_params_.img_width, CV_32SC1);
    } else {
      return -1;
    }
  }
  size_t offset = 0;
  switch (image_type)
  {
  case OCLImageType::RET_DISP:
    offset = 0;
    clEnqueueReadBuffer(stream, m_d_result_disp_, true, offset, m_params_.img_height * m_params_.img_width * sizeof(uint16_t), ret_img.data, 0, nullptr, nullptr);    
    break;
  case OCLImageType::RET_DEPTH:   
    offset = 0;
    clEnqueueReadBuffer(stream, m_d_result_depth_, true, offset, m_params_.img_height * m_params_.img_width * sizeof(uint16_t), ret_img.data, 0, nullptr, nullptr);
    break;
  case OCLImageType::PRIOR_DEPTH:   
    offset = 0;
    clEnqueueReadBuffer(stream, m_d_prior_depth_, true, offset, m_params_.img_height * m_params_.img_width * sizeof(float), ret_img.data, 0, nullptr, nullptr);
    break;
  case OCLImageType::PRIOR_DISP:   
    offset = 0;
    clEnqueueReadBuffer(stream, m_d_prior_disp_, true, offset, m_params_.img_height * m_params_.img_width * sizeof(float), ret_img.data, 0, nullptr, nullptr);
    break;
  default:
    break;
  }
  return 0;
}
}
#endif