@DEPTH_FIXED_SCALE@

#define uint16_t unsigned short
#define uint32_t uint

kernel void depth2disp_kernel(global float* d_disp,
    global const uint32_t* d_depth,
    int width,
    int height,
    float baseline,
    float focal_length,
    float doffs,
    float sub_pixel_scale)
{
    const int x = get_global_id(0);
    const int y = get_global_id(1);

    if (x >= width || y >= height)
    {
        return;
    }

    float d = (float)d_depth[y * width + x] / DEPTH_FIXED_SCALE;
    if (d > 0)
    {
        // Convert depth to disparity
        // Formula: disp = (baseline * focal_length) / depth - doffs
        float disp = (baseline * focal_length) / d - doffs;
        d_disp[y * width + x] = disp;
    }
    else
    {
        // Invalid disparity, set disp to zero
        d_disp[y * width + x] = 0;
    }
    
}