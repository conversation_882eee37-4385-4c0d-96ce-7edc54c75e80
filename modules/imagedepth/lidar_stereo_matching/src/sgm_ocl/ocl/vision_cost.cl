#define uint8_t uchar
#define int16_t short
#define uint16_t unsigned short

@MAX_DISP@
@KERNEL_R_H_SAD@
@KERNEL_R_W_SAD@
@BLOCK_DIM_X@
@BLOCK_DIM_Y@

// 局部内存中Block的实际尺寸，包含了邻域
#define LOCAL_BLOCK_WIDTH  (BLOCK_DIM_X + 2 * KERNEL_R_W_SAD)
#define LOCAL_BLOCK_HEIGHT (BLOCK_DIM_Y + 2 * KERNEL_R_H_SAD)



#define LOCAL_RIGHT_BLOCK_WIDTH  (BLOCK_DIM_X + MAX_DISP + 2 * KERNEL_R_W_SAD)
#define LOCAL_RIGHT_BLOCK_HEIGHT (BLOCK_DIM_Y + 2 * KERNEL_R_H_SAD)

__kernel void vision_cost_kernel(    
    __global uint16_t* output_data,             // 输出Cost数据
    __global const uint8_t* input_data_left,    // 输入图像数据
    __global const uint8_t* input_data_right,   // 输入图像数据
    __global const int16_t* input_data_left_feature,  // 输入图像数据
    __global const int16_t* input_data_right_feature,  // 输入图像数据
    int width,                      
    int height,                        
    int min_disp,
    int max_disp,
    int r_h_sad,                              
    int r_w_sad,                       
    uint16_t max_gray_cost,                   
    uint16_t max_grad_cost,                   
    uint16_t weight_gray,                     
    uint16_t weight_grad,                     
    uint16_t scale_vis)                 
{
    // 声明局部内存，用于存储当前工作组的图像块
    __local uint8_t local_block_left[LOCAL_BLOCK_HEIGHT][LOCAL_BLOCK_WIDTH];
    __local uint8_t local_block_right[LOCAL_RIGHT_BLOCK_HEIGHT][LOCAL_RIGHT_BLOCK_WIDTH];
    __local int16_t local_block_left_feature[LOCAL_BLOCK_HEIGHT][LOCAL_BLOCK_WIDTH];
    __local int16_t local_block_right_feature[LOCAL_RIGHT_BLOCK_HEIGHT][LOCAL_RIGHT_BLOCK_WIDTH];

    // 获取当前工作项的全局 ID (对应输出图像的像素坐标和视差)
    int u = get_global_id(0);
    int v = get_global_id(1);
    int d = get_global_id(2);

    // 获取当前工作项在工作组内的局部 ID (对应BLOCK内的坐标)
    int local_x = get_local_id(0);
    int local_y = get_local_id(1);

    // 获取当前工作项所属工作组的 ID
    int group_x = get_group_id(0);
    int group_y = get_group_id(1);

    // 计算当前工作组在全局内存中处理的Block的起始像素坐标
    int block_start_x = group_x * BLOCK_DIM_X;
    int block_start_y = group_y * BLOCK_DIM_Y;

    // 边界检查：确保u, v, d在有效范围内
    if (u >= width || v >= height || d >= max_disp) {
        return;
    }

    int local_group_size_x = get_local_size(0); // BLOCK_DIM_X
    int local_group_size_y = get_local_size(1); // BLOCK_DIM_Y
    // 计算当前工作组需要加载的全局区域的起始 X 坐标
    // 左图的全局起始 X 坐标
    int global_load_start_x_left = block_start_x - KERNEL_R_W_SAD;
    int global_load_start_y_left = block_start_y - KERNEL_R_H_SAD;
    // --- 数据加载阶段 ---
    // --- 左图局部内存加载 ---
    // 遍历 LOCAL_LEFT_BLOCK_HEIGHT x LOCAL_LEFT_BLOCK_WIDTH 区域
    for (int ly = local_y; ly < LOCAL_BLOCK_HEIGHT; ly += local_group_size_y) {
        for (int lx = local_x; lx < LOCAL_BLOCK_WIDTH; lx += local_group_size_x) {
            // 计算全局坐标
            int global_x = global_load_start_x_left + lx;
            int global_y = global_load_start_y_left + ly;

            // 边界钳制并加载
            int clamped_global_x = clamp(global_x, 0, width - 1);
            int clamped_global_y = clamp(global_y, 0, height - 1);

            local_block_left[ly][lx] = input_data_left[clamped_global_y * width + clamped_global_x];
            local_block_left_feature[ly][lx] = input_data_left_feature[clamped_global_y * width + clamped_global_x];
        }
    }

    // 右图的全局起始 X 坐标
    // 右图加载区域的起始 X 坐标需要根据 BLOCK_DIM_X 和 MAX_DISP 来计算
    // 假设最左侧的访问点是 block_start_x - max_disp - KERNEL_R_W_SAD
    int global_load_start_x_right = block_start_x - (max_disp + min_disp) - KERNEL_R_W_SAD; // min_disp也要考虑进去
    int global_load_start_y_right = block_start_y - KERNEL_R_H_SAD; // Y 轴与左图相同

    // --- 右图局部内存加载 ---
    // 遍历 LOCAL_RIGHT_BLOCK_HEIGHT x LOCAL_RIGHT_BLOCK_WIDTH 区域
    for (int ly = local_y; ly < LOCAL_RIGHT_BLOCK_HEIGHT; ly += local_group_size_y) {
        for (int lx = local_x; lx < LOCAL_RIGHT_BLOCK_WIDTH; lx += local_group_size_x) {
            // 计算全局坐标
            int global_x = global_load_start_x_right + lx;
            int global_y = global_load_start_y_right + ly;

            // 边界钳制并加载
            int clamped_global_x = clamp(global_x, 0, width - 1);
            int clamped_global_y = clamp(global_y, 0, height - 1);

            local_block_right[ly][lx] = input_data_right[clamped_global_y * width + clamped_global_x];
            local_block_right_feature[ly][lx] = input_data_right_feature[clamped_global_y * width + clamped_global_x];
        }
    }

    // --- 同步 ---
    // 确保所有工作项都已将数据加载到局部内存中
    barrier(CLK_LOCAL_MEM_FENCE);

    // --- 计算阶段 ---
    // 只有当当前全局 ID 对应有效的输出像素时才进行计算
    // 这里的 (u, v) 已经扣除了 SAD 窗口半径的边缘
    if (u >= r_w_sad && u < width - r_w_sad && v >= r_h_sad && v < height - r_h_sad) {
        int16_t current_pixel_cost = 0; // 初始化当前 (u,v,d) 的总代价

        // 计算右图像中心点在全局的水平坐标（用于确定右图在局部内存中的偏移）
        int u_r_center_global = u - d - min_disp; // 这是当前视差 d 下右图SAD窗口中心点的全局坐标

        // 遍历SAD窗口
        for (int dv = -r_h_sad; dv <= r_h_sad; dv++) {
            for (int du = -r_w_sad; du <= r_w_sad; du++) {
                // 计算左图SAD窗口内像素在局部Block中的坐标
                int local_access_v_left = local_y + r_h_sad + dv;
                int local_access_u_left = local_x + r_w_sad + du;

                // 从局部内存读取左图像数据
                int16_t gray_l = local_block_left[local_access_v_left][local_access_u_left];
                int16_t grad_l = local_block_left_feature[local_access_v_left][local_access_u_left];

                // 计算右图SAD窗口内像素的全局坐标
                int u_r_sample_global = u_r_center_global + du;
                int v_r_sample_global = v + dv;

                // 计算右图SAD窗口内像素在局部Block中的坐标
                // 这是最关键的一步：将全局坐标映射到局部内存坐标
                // local_access_u_right = u_r_sample_global - global_load_start_x_right;
                int local_access_u_right = u_r_sample_global - (block_start_x - (max_disp + min_disp) - KERNEL_R_W_SAD);
                int local_access_v_right = local_y + r_h_sad + dv; // 垂直方向的相对位置与左图相似

                // 从局部内存读取右图像数据
                // 再次进行边界检查以防万一，尽管理论上加载的区域应该包含了所有访问。
                // 如果 local_access_u_right 或 local_access_v_right 超出 LOCAL_RIGHT_BLOCK_WIDTH/HEIGHT，
                // 则表示计算逻辑有误或局部内存区域不足。
                // 正常情况下，这里不应该再出现“超出图像边界”的问题，因为数据已经加载到局部内存。
                // 检查局部内存索引是否在范围内
                if (local_access_u_right >= 0 && local_access_u_right < LOCAL_RIGHT_BLOCK_WIDTH &&
                    local_access_v_right >= 0 && local_access_v_right < LOCAL_RIGHT_BLOCK_HEIGHT)
                {
                    int16_t gray_r = local_block_right[local_access_v_right][local_access_u_right];
                    int16_t grad_r = local_block_right_feature[local_access_v_right][local_access_u_right];

                    // 计算灰度代价
                    uint16_t gray_cost = abs((gray_l - gray_r));
                    gray_cost = min(gray_cost, max_gray_cost);

                    // 计算梯度代价
                    uint16_t grad_cost = abs((grad_l - grad_r));
                    grad_cost = min(grad_cost, max_grad_cost);

                    // 累加加权代价
                    current_pixel_cost += weight_gray * gray_cost + weight_grad * grad_cost;
                } else {
                    // 如果由于某种原因局部内存索引超出范围，给出惩罚（这表明计算逻辑或局部内存大小可能不正确）
                    current_pixel_cost += max_gray_cost + max_grad_cost;
                }
            }
        }

        // 归一化代价
        if (scale_vis > 0) {
            current_pixel_cost /= scale_vis;
        }
        // 将计算出的代价写入全局内存
        output_data[v * width * max_disp + u * max_disp + d] = current_pixel_cost;
    } else {
        // 边缘像素，将其输出值设为 0
        output_data[v * width * max_disp + u * max_disp + d] = 0.0f;
    }
}