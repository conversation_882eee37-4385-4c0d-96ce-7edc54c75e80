@DEPTH_FIXED_SCALE@
#define uint32_t uint

int crossProduct(int2 a, int2 b, int2 c) 
{
  return (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
}

kernel void triangle2depth_kernel(
    global uint32_t* d_depth,
    global int* normals,
    global int* normals_count,
    global const float* uv_p3d,
    global const int* triangles,
    global const int* idx_mat,
    int width,
    int height,
    int point_num,
    int triangle_num, 
    float thresh_cos,
    global const float* K)
{
    const int idx = get_global_id(0);
    if (idx >= triangle_num)
        return;

    // 获取三角形的三个顶点
    int2 p2d_1 = (int2)(triangles[idx * 6 + 0], triangles[idx * 6 + 1]);
    int2 p2d_2 = (int2)(triangles[idx * 6 + 2], triangles[idx * 6 + 3]);
    int2 p2d_3 = (int2)(triangles[idx * 6 + 4], triangles[idx * 6 + 5]);
    // 检查顶点是否在图像范围内
    if( p2d_1.x < 0 || p2d_1.x >= width || p2d_1.y < 0 || p2d_1.y >= height ||
        p2d_2.x < 0 || p2d_2.x >= width || p2d_2.y < 0 || p2d_2.y >= height ||
        p2d_3.x < 0 || p2d_3.x >= width || p2d_3.y < 0 || p2d_3.y >= height)
    {
        return; // 如果三角形的顶点超出图像范围，直接返回
    }
    // 计算三角形的三个顶点在uv_p3d中的索引  
    int map_idx1 = p2d_1.x + p2d_1.y * width;
    int map_idx2 = p2d_2.x + p2d_2.y * width;
    int map_idx3 = p2d_3.x + p2d_3.y * width;

    // 获取idx_mat中对应的索引
    int idx1 = idx_mat[map_idx1];
    int idx2 = idx_mat[map_idx2];
    int idx3 = idx_mat[map_idx3];
    
    if (idx1 < 0 || idx1 >= point_num || idx2 < 0 || idx2 >= point_num || idx3 < 0 || idx3 >= point_num)
    {
        return; // 如果索引超出范围，直接返回
    }
    // 获取对应的三维点
    float3 p3d_1 = (float3)(uv_p3d[idx1 * 5 + 2], uv_p3d[idx1 * 5 + 3], uv_p3d[idx1 * 5 + 4]);
    float3 p3d_2 = (float3)(uv_p3d[idx2 * 5 + 2], uv_p3d[idx2 * 5 + 3], uv_p3d[idx2 * 5 + 4]);
    float3 p3d_3 = (float3)(uv_p3d[idx3 * 5 + 2], uv_p3d[idx3 * 5 + 3], uv_p3d[idx3 * 5 + 4]);

    // 三角形的法向量
    float3 n_3d = cross(p3d_2 - p3d_1, p3d_3 - p3d_1);
    if(0==n_3d.x && 0==n_3d.y && 0==n_3d.z)
    {
      return;
    }   
    n_3d = normalize(n_3d);
    if(n_3d.z>0)
    {
      n_3d = n_3d * -1;
    }
    float3 p3d_1_normalized = normalize(p3d_1);
    float3 p3d_2_normalized = normalize(p3d_2);
    float3 p3d_3_normalized = normalize(p3d_3);
    // 三角形的法向量n和点p1, p2, p3任意的法向量的夹角小于阈值
    if (fabs(dot(n_3d, p3d_1_normalized)) < thresh_cos ||
        fabs(dot(n_3d, p3d_2_normalized)) < thresh_cos ||
        fabs(dot(n_3d, p3d_3_normalized)) < thresh_cos)
    { 
        return;
    }

    // 二维三角形的边长，选取最长的边作为权重
    int l12 = (p2d_1-p2d_2).x*(p2d_1-p2d_2).x + (p2d_1-p2d_2).y*(p2d_1-p2d_2).y;;
    int l13 = (p2d_1-p2d_3).x*(p2d_1-p2d_3).x + (p2d_1-p2d_3).y*(p2d_1-p2d_3).y;;
    int l23 = (p2d_2-p2d_3).x*(p2d_2-p2d_3).x + (p2d_2-p2d_3).y*(p2d_2-p2d_3).y;;
    int l = max(l12, max(l13, l23));
    int w = l+1;
    float3 n_w = n_3d / (float)(w);
    int3 n_w_fixed = (int3)(n_w.x * DEPTH_FIXED_SCALE, n_w.y * DEPTH_FIXED_SCALE, n_w.z * DEPTH_FIXED_SCALE);
    // 统计法向量的加权和
    atomic_add(&normals[idx1 * 3 + 0], n_w_fixed.x);
    atomic_add(&normals[idx1 * 3 + 1], n_w_fixed.y);
    atomic_add(&normals[idx1 * 3 + 2], n_w_fixed.z);

    atomic_add(&normals[idx2 * 3 + 0], n_w_fixed.x);
    atomic_add(&normals[idx2 * 3 + 1], n_w_fixed.y);
    atomic_add(&normals[idx2 * 3 + 2], n_w_fixed.z);

    atomic_add(&normals[idx3 * 3 + 0], n_w_fixed.x);
    atomic_add(&normals[idx3 * 3 + 1], n_w_fixed.y);
    atomic_add(&normals[idx3 * 3 + 2], n_w_fixed.z);

    // 针对 normals_count 数组的原子操作 (通常在 OpenCL 1.0+ 就支持整数原子操作)
    atomic_inc(&normals_count[idx1]);
    atomic_inc(&normals_count[idx2]);
    atomic_inc(&normals_count[idx3]);   
    
  float4 abcd = (float4)(n_3d.x * K[0], n_3d.y * K[1], n_3d.z + n_3d.x * K[2] + n_3d.y * K[3], dot(n_3d, p3d_1));
  if(abcd.w<0)
  {
    abcd = abcd * -1.0f; // 确保深度值为正
  }

  // 遍历三角形包围盒内的像素
  // 计算每个像素的深度值
  // 使用三角形的方程 abcd.x * u + abcd.y * v + abcd.z = 0
  // 计算深度值为 abcd.w / (abcd.x * u + abcd.y * v + abcd.z)
  // 计算三角形的包围盒
  // 注意：这里的包围盒是基于整数像素坐标的
  // 计算包围盒的最小和最大坐标
  // 遍历包围盒内的像素
  // 如果像素在三角形内，则计算深度值并存储到 d_depth 中
  int minX = min(p2d_1.x, min(p2d_2.x, p2d_3.x));
  int maxX = max(p2d_1.x, max(p2d_2.x, p2d_3.x));
  int minY = min(p2d_1.y, min(p2d_2.y, p2d_3.y));
  int maxY = max(p2d_1.y, max(p2d_2.y, p2d_3.y));

  for(int v = minY; v <= maxY; ++v) 
  {
    for(int u = minX; u <= maxX; ++u) 
    {
      int2 p = (int2)(u, v);
      int d1 = crossProduct(p2d_1, p2d_2, p);
      int d2 = crossProduct(p2d_2, p2d_3, p);
      int d3 = crossProduct(p2d_3, p2d_1, p);
  
      bool inside = (d1 <= 0 && d2 <= 0 && d3 <= 0) ||  (d1 >= 0 && d2 >= 0 && d3 >= 0);
      
      if(inside) 
      {
        int linear_idx = v * width + u;
        float den = abcd.x * u + abcd.y * v + abcd.z;
        if(den > 0)
        {
          uint32_t calculated_depth = DEPTH_FIXED_SCALE * 0.001 * (abcd.w / den);
          atomic_min(&d_depth[linear_idx], calculated_depth);
        }
      }
    }
  } 
}