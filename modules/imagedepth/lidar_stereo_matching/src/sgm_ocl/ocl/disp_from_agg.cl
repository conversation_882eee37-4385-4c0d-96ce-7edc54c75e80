//include utilities
@WARP_SIZE@
@ACCUMULATION_PER_THREAD@
@MAX_DISPARITY@
@NUM_PATHS@
@COMPUTE_SUBPIXEL@
@WARPS_PER_BLOCK@
@BLOCK_SIZE@
@SUBPIXEL_SHIFT@

#define uint8_t uchar
#define int16_t short
#define uint32_t uint
#define uint16_t unsigned short

#define REDUCTION_PER_THREAD (MAX_DISPARITY / WARP_SIZE) // 16
#define ACCUMULATION_INTERVAL (ACCUMULATION_PER_THREAD / REDUCTION_PER_THREAD) // 16 / 16 = 1
#define UNROLL_DEPTH ((REDUCTION_PER_THREAD > ACCUMULATION_INTERVAL) ? REDUCTION_PER_THREAD : ACCUMULATION_INTERVAL) // 16
#define INVALID_DISP 0

inline uint32_t pack_uint8x4(uint32_t x, uint32_t y, uint32_t z, uint32_t w) 
{
    uchar4 uint8x4;
    uint8x4.x = (uint8_t)(x);
    uint8x4.y = (uint8_t)(y);
    uint8x4.z = (uint8_t)(z);
    uint8x4.w = (uint8_t)(w);
    return as_uint(uint8x4);
}

void store_uint8_vector_8u(global uint8_t* dest, const uint32_t* ptr)
{
    uint2 uint32x2;
    uint32x2.x = pack_uint8x4(ptr[0], ptr[1], ptr[2], ptr[3]);
    uint32x2.y = pack_uint8x4(ptr[4], ptr[5], ptr[6], ptr[7]);
    global uint2* dest_ptr = (global uint2*) dest;
    *dest_ptr = uint32x2;
}

void store_uint8_vector(global uint8_t* dest, const int N,
    const uint32_t* ptr)
{
    if (N == 16)
    {
        uchar16 vv;
        uchar* v = (uchar*)&vv;
#pragma unroll
        for (int i = 0; i < 16; ++i)
            v[i] = (uint8_t)ptr[i];
        *((global uchar16*)dest) = vv;
    }
    else if (N == 8)
    {
        uchar8 vv;
        uchar* v = (uchar*)&vv;
#pragma unroll
        for (int i = 0; i < 8; ++i)
            v[i] = (uint8_t)ptr[i];
        *((global uchar8*)dest) = vv;

    }
    else
    {
#pragma unroll
        for (int i = 0; i < N; ++i)
            dest[i] = (uint8_t)ptr[i];
    }
}


inline void load_uint8_vector(uint32_t* dest, const int num, const local uint8_t* ptr) 
{
#pragma unroll
    for (int  i = 0; i < num; ++i)
        dest[i] = (uint32_t)(ptr[i]);
    //barrier(CLK_LOCAL_MEM_FENCE);
}


inline void g_load_uint8_vector(uint32_t* dest, const int num, const global uint8_t* ptr)
{
    if (num == 16)
    {
        uchar16 vv = *((global uchar16*)ptr);
        uchar* v = (uchar*)&vv;
#pragma unroll
        for (int i = 0; i < 16; ++i)
            dest[i] = (uint32_t)v[i];   
    }
    else if (num == 8)
    {
        uchar8 vv = *((global uchar8*)ptr);
        uchar* v = (uchar*)&vv;
#pragma unroll
        for (int i = 0; i < 8; ++i)
            dest[i] = (uint32_t)v[i];
    }
    else
    {
#pragma unroll
        for (int i = 0; i < num; ++i)
            dest[i] = (uint32_t)(ptr[i]);
    }
}


inline void lload_uint8_vector(uint32_t* dest, const int num,  const uint8_t* ptr)
{
    for (int i = 0; i < num; ++i)
        dest[i] = (uint32_t)(ptr[i]);
}

inline void load_uint16_vector(uint32_t* dest, const int num, const local uint16_t* ptr)
{
    for (int i = 0; i < num; ++i)
        dest[i] = (uint32_t)(ptr[i]);

}


inline void lload_uint16_vector(uint32_t* dest, const int num, const uint16_t* ptr)
{
    for (int i = 0; i < num; ++i)
        dest[i] = (uint32_t)(ptr[i]);
}


inline void store_uint16_vector(local uint16_t* dest, const int N, const uint32_t* ptr)
{
    for (int i = 0; i < N; ++i)
        dest[i] = (uint16_t)ptr[i];
    barrier(CLK_LOCAL_MEM_FENCE);
}

inline uint32_t subgroup_min(const uint32_t lane_id,
    const uint32_t subgroup_size,
    local uint32_t* shfl_mem)
{
    int lid = get_local_id(0);
    for (int i = subgroup_size / 2; i > 0; i >>= 1)
    {
        if (lane_id < i)
        {
            shfl_mem[lid] = min(shfl_mem[lid], shfl_mem[lid + i]);
        }
        barrier(CLK_LOCAL_MEM_FENCE);
    }
    int sub_group_idx = get_local_id(0) / subgroup_size;
    return shfl_mem[sub_group_idx * subgroup_size];
}

inline uint32_t pack_cost_index(uint32_t cost, uint32_t index)
{
    union {
        uint32_t uint32;
        ushort2 uint16x2;
    } u;
    u.uint16x2.x = (uint16_t)(index);
    u.uint16x2.y = (uint16_t)(cost);
    return u.uint32;
}

inline uint32_t unpack_cost(uint32_t packed)
{
    return packed >> 16;
}

inline int unpack_index(uint32_t packed)
{
    return packed & 0xffffu;
}

inline uint32_t compute_disparity_normal(uint32_t disp, uint32_t cost, const local uint16_t* smem )
{
    return disp;
}

inline uint32_t compute_disparity_subpixel(uint32_t disp, uint32_t cost, const local uint16_t* smem)
{
    int subp = disp;
    subp <<= SUBPIXEL_SHIFT;
    if (disp > 0 && disp < MAX_DISPARITY - 1)
    {
        const int left = smem[disp - 1];
        const int right = smem[disp + 1];
        const int numer = left - right;
        const int denom = left - 2 * cost + right;
        subp += ((numer << SUBPIXEL_SHIFT) + denom) / (2 * denom);
    }
    return subp;
}

// 最终视差选择核函数
__kernel void compute_disparity_from_agg_kernel(
    __global uint16_t* left_dest,             // 输出：视差图
    __global uint16_t* right_dest,            // 输出：视差图
    __global const uint8_t* src,            // 输入：最终累加代价体
    __global const float *prior_disp,    
    int width,
    int height,
    int min_disp,
    int num_disp,
    int uniqueness_int,
    int sub_shift,
    uint16_t r_pd,
    uint16_t pd1,
    uint16_t pd2,
    float alpha,
    float beta)
{
    float uniqueness = (float)uniqueness_int / 100.0f;
    int pitch = width;
    const unsigned int cost_step = MAX_DISPARITY * width * height;
    const unsigned int warp_id = get_local_id(0) / WARP_SIZE; // 当前线程所属warp在工作组中的ID
    const unsigned int lane_id = get_local_id(0) % WARP_SIZE; // 当前线程在其warp内的ID

    const unsigned int y = get_group_id(0) * WARPS_PER_BLOCK + warp_id; // 当前线程所处理的行，每个工作组处理WARPS_PER_BLOCK = 8行
    src += y * MAX_DISPARITY * width;
    left_dest += y * pitch;
    right_dest += y * pitch;
    prior_disp += y * pitch;

    if (y >= height)
    {
        return;
    }

    local uint16_t smem_cost_sum[WARPS_PER_BLOCK][ACCUMULATION_INTERVAL][MAX_DISPARITY];
    local uint32_t shfl_buffer[BLOCK_SIZE];    
    
    local float local_prior_disp_segment[WARPS_PER_BLOCK][UNROLL_DEPTH];



    uint32_t right_best[REDUCTION_PER_THREAD];
    for (unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i)
    {
        right_best[i] = 0xffffffffu;
    }

    for (unsigned int x0 = 0; x0 < width; x0 += UNROLL_DEPTH) 
    {
        // --- 新增：将 prior_disp 从全局内存加载到局部内存 ---
        // 确保每个 prior_disp 值只被工作组中的一个线程加载一次。
        // 这里让 lane_id 0 的线程负责加载它所属 Warp 对应行的 `UNROLL_DEPTH` 段数据
        // 每个 Warp 负责一行，所以 `warp_id` 确定了行的偏移。
        // `x0` 确定了当前行的列偏移。
        if (lane_id < UNROLL_DEPTH) { //WARP_SIZE >= UNROLL_DEPTH
            const unsigned int global_x = x0 + lane_id;
            // 确保加载索引不越界
            if (global_x < width) {
                local_prior_disp_segment[warp_id][lane_id] = prior_disp[global_x];
            } else {
                local_prior_disp_segment[warp_id][lane_id] = 0.0f; // 或其他默认值
            }
        }

        barrier(CLK_LOCAL_MEM_FENCE); // 确保所有 prior_disp 加载完成，再进行计算
#pragma unroll
        for (unsigned int x1 = 0; x1 < UNROLL_DEPTH; ++x1)
        {
            if (x1 % ACCUMULATION_INTERVAL == 0)
            {
                const unsigned int k = lane_id * ACCUMULATION_PER_THREAD;
                const unsigned int k_hi = k / MAX_DISPARITY;
                const unsigned int k_lo = k % MAX_DISPARITY;
                const unsigned int x = x0 + x1 + k_hi;
                if (x < width)
                {
                    const unsigned int offset = x * MAX_DISPARITY + k_lo;
                    uint32_t weight_sum[ACCUMULATION_PER_THREAD];
                    // 浮点数转定点，为保留精度先乘以一个数
                    uint32_t d_ref = (uint32_t)(local_prior_disp_segment[warp_id][x1 + k_hi] * 256.f);
                    for (unsigned int i = 0; i < ACCUMULATION_PER_THREAD; ++i)
                    {                        
                        //weight_sum[i] = 0;
                        uint32_t d = k_lo + i;                        
                        uint32_t delta = abs(d_ref-d);
                        uint32_t delta_2 = delta * delta;
                        // 将乘的倍数去掉
                        uint32_t tmp_cost = (uint32_t)(pd1 * delta_2) >> 16;
                        uint32_t prior_cost = (uint32_t)((d_ref > 0) ? ((delta > r_pd) ? pd2 : tmp_cost) : 0);
                        weight_sum[i] = prior_cost * alpha;
                    }
                    for (unsigned int p = 0; p < NUM_PATHS; ++p)
                    {
                        uint32_t load_buffer[ACCUMULATION_PER_THREAD];
                        g_load_uint8_vector(load_buffer, ACCUMULATION_PER_THREAD, &src[p * cost_step + offset]);
                        for (unsigned int i = 0; i < ACCUMULATION_PER_THREAD; ++i)
                        {
                            //weight_sum[i] += (load_buffer[i]);
                            weight_sum[i] += (load_buffer[i] * beta);
                        }
                    }
                    store_uint16_vector(&smem_cost_sum[warp_id][k_hi][k_lo], ACCUMULATION_PER_THREAD, weight_sum);                   
                }
                barrier(CLK_LOCAL_MEM_FENCE | CLK_GLOBAL_MEM_FENCE);
            }
            const unsigned int x = x0 + x1;
            if (x < width)
            {
                // Load sum of costs
                const unsigned int smem_x = x1 % ACCUMULATION_INTERVAL;
                const unsigned int k0 = lane_id * REDUCTION_PER_THREAD;
                uint32_t local_cost_sum[REDUCTION_PER_THREAD];
                load_uint16_vector(local_cost_sum, REDUCTION_PER_THREAD, &smem_cost_sum[warp_id][smem_x][k0]); 
                
                // Pack sum of costs and dispairty
                uint32_t local_packed_cost[REDUCTION_PER_THREAD];
                for (unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i)
                {
                    local_packed_cost[i] = pack_cost_index(local_cost_sum[i], k0 + i);
                    
                }
                
                // Update left
                uint32_t best = 0xffffffffu;
                for (unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i)
                {
                    best = min(best, local_packed_cost[i]);
                }
                shfl_buffer[get_local_id(0)] = best;
                barrier(CLK_LOCAL_MEM_FENCE);
                best = subgroup_min(lane_id, WARP_SIZE, shfl_buffer);
                // + best = subgroup_min<WARP_SIZE>(best, 0xffffffffu);
                
                // Update right
#pragma unroll
                for (unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i) 
                {
                    const unsigned int k = lane_id * REDUCTION_PER_THREAD + i;
                    const int p = (int)(((x - k) & ~(MAX_DISPARITY - 1)) + k);
                    const unsigned int d = (unsigned int)(x - p);
                    //load data into shared memory
                    shfl_buffer[get_local_id(0)] = local_packed_cost[(REDUCTION_PER_THREAD - i + x1) % REDUCTION_PER_THREAD];
                    barrier(CLK_LOCAL_MEM_FENCE);
                    const uint32_t recv = shfl_buffer[warp_id * WARP_SIZE + d / REDUCTION_PER_THREAD];
                    barrier(CLK_LOCAL_MEM_FENCE);

                    right_best[i] = min(right_best[i], recv);
                    if (d == MAX_DISPARITY - 1) 
                    {
                        if (0 <= p)
                        {
                            right_dest[p] = compute_disparity_normal(unpack_index(right_best[i]), 0, 0);
                        }
                        right_best[i] = 0xffffffffu;
                    }
                }
                // Resume updating left to avoid execution dependency
                const uint32_t bestCost = unpack_cost(best);
                const int bestDisp = unpack_index(best);
                bool uniq = true;
                for (unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i) 
                {
                    const uint32_t x = local_packed_cost[i];
                    const bool uniq1 = unpack_cost(x) * uniqueness >= bestCost;
                    const bool uniq2 = abs(unpack_index(x) - bestDisp) <= 1;
                    uniq &= uniq1 || uniq2;
                }
                shfl_buffer[get_local_id(0)] = uniq ? 1u : 0u;
                barrier(CLK_LOCAL_MEM_FENCE);
                uniq = subgroup_min(lane_id, WARP_SIZE, shfl_buffer) == 1u ? true : false;
                //uniq = subgroup_and<WARP_SIZE>(uniq, 0xffffffffu);
                //uniq = true;
                if (lane_id == 0) 
                {
                    if (uniq)
                    {
                        if (COMPUTE_SUBPIXEL == 1)
                            left_dest[x] = compute_disparity_subpixel(bestDisp, bestCost, smem_cost_sum[warp_id][smem_x]);
                        else
                            left_dest[x] = compute_disparity_normal(bestDisp, bestCost, smem_cost_sum[warp_id][smem_x]);
                    }
                    else
                        left_dest[x] = INVALID_DISP;
                }
            }
        }
    }
    for (unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i) 
    {
        const unsigned int k = lane_id * REDUCTION_PER_THREAD + i;
        const int p = (int)(((width - k) & ~(MAX_DISPARITY - 1)) + k);
        if (0 <= p && p < width) 
        {
            right_dest[p] = compute_disparity_normal(unpack_index(right_best[i]), 0, 0);
        }
    }  
}