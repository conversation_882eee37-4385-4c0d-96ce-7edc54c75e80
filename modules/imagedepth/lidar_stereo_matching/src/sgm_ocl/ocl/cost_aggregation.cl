#define uint8_t uchar
#define int16_t short
#define uint32_t uint
#define uint16_t unsigned short

@MAX_DISP@

__kernel void cost_aggregation_kernel(    
    __global uint32_t* cost_agg_data,             // 输出Cost数据
    __global const uint16_t* cost_sum_data,       // 输入Cost数据
    __global const uint8_t* input_gray_data,      // 输入图像数据
    int width,                      
    int height,
    uint16_t P1,
    uint16_t P2, 
    int num_disp,
    int k_diff,
    int path_dir_x,                              // 路径方向的 x 分量 (-1, 0, 1)
    int path_dir_y)                              // 路径方向的 y 分量 (-1, 0, 1)
{
    // 每个工作项负责处理一个路径（即图像的一行或一列）。
    // 例如，path_dir_y != 0，并行处理列（global_id(0) 是 u）, path_dir_x != 0，并行处理行（global_id(0) 是 v）。
    int main_axis_idx = get_global_id(0);

    // 确定主轴的最大范围
    int main_axis_max = (path_dir_y != 0) ? width : height;

    if (main_axis_idx >= main_axis_max || num_disp > MAX_DISP) {
        return;
    }

    // 用于访问扁平化数组的步长
    int D = num_disp;
    int cost_volume_stride_w = num_disp;            // 宽度维度上的步长 (D)
    int cost_volume_stride_h = width * num_disp;    // 高度维度上的步长 (W*D)
    __private float L[MAX_DISP];                         // 注意：256 是一个硬编码的限制

    // 初始化DP状态 for the current column
    float last_min = 0.0f; // 对应 C++ 中 DynamicProgramming 类的 last_min_
    for (int i = 0; i < num_disp; ++i) {
        L[i] = 0.0f; // 对应 C++ 中 DynamicProgramming 类的 dp_
    }

    // 路径遍历的起始和结束点
    int v_start, v_end, v_step;
    int u_start, u_end, u_step;

    // 根据路径方向确定遍历方式
    if (path_dir_x == 0) { // 垂直方向 (path_dir_y != 0)
        u_start = main_axis_idx; u_end = u_start + path_dir_x; u_step = 0; // x 轴固定
        v_start = (path_dir_y > 0) ? 0 : height - 1;
        v_end = (path_dir_y > 0) ? height : -1;
        v_step = path_dir_y;
    } else if (path_dir_y == 0) { // 水平方向 (path_dir_x != 0)
        v_start = main_axis_idx; v_end = v_start + path_dir_y; v_step = 0; // y 轴固定
        u_start = (path_dir_x > 0) ? 0 : width - 1;
        u_end = (path_dir_x > 0) ? width : -1;
        u_step = path_dir_x;
    } else { // 对角线方向
        if (path_dir_y > 0) { // 向上或向下对角线
            v_start = (path_dir_x > 0) ? 0 : height - 1; // 左上到右下，从 (0,0) 开始；左下到右上，从 (0,H-1) 开始
            u_start = (path_dir_x > 0) ? main_axis_idx : width - 1 - main_axis_idx;
        } else { // 向下或向上对角线
            v_start = (path_dir_x > 0) ? height - 1 : 0; // 右上到左下，从 (0,H-1) 开始；右下到左上，从 (0,0) 开始
            u_start = (path_dir_x > 0) ? main_axis_idx : width - 1 - main_axis_idx;
        }
        v_end = (path_dir_y > 0) ? height : -1;
        u_end = (path_dir_x > 0) ? width : -1;
        v_step = path_dir_y;
        u_step = path_dir_x;
    }

    int current_u = u_start;
    int current_v = v_start;
    int last_gray_val = -1;

    // 遍历路径上的每一个像素
    while ( (path_dir_x == 0 || (path_dir_x > 0 ? (current_u < u_end) : (current_u > u_end))) &&
            (path_dir_y == 0 || (path_dir_y > 0 ? (current_v < v_end) : (current_v > v_end))) )
    {
        // 边界检查，确保像素在图像范围内
        if (current_u < 0 || current_u >= width || current_v < 0 || current_v >= height) {
            // 如果是边界路径，跳过这个像素，继续下一个
            current_u += u_step;
            current_v += v_step;
            continue;
        }

        // 获取当前像素的初始匹配代价 C(p,d)
        __global const uint16_t* Cp_ptr = cost_sum_data + current_v * cost_volume_stride_h + current_u * cost_volume_stride_w;

        // 获取当前像素的灰度值
        int current_gray_val = (int)input_gray_data[current_v * width + current_u];

        // 计算自适应 P2
        float p2_adjusted = P2;
        if (last_gray_val >= 0 && k_diff > 0) {
            int diff_val = abs(current_gray_val - last_gray_val) / k_diff;
            diff_val = max(1, diff_val);
            p2_adjusted = (float)P2 / (float)diff_val;
        }
        last_gray_val = current_gray_val;

        // Clamp p2_adjusted: if(p2 < p1) p2 = p1;
        p2_adjusted = max((float)P1, p2_adjusted);

        // --- SGM DP 更新逻辑 ---
        // Lr(p,d) = C(p,d) + min( Lr(p-r, d), Lr(p-r, d-1)+P1, Lr(p-r, d+1)+P1, min_k(Lr(p-r, k)+P2) ) - min_k(Lr(p-r, k))
        // 这里的 L 数组对应 Lr(p-r, d)
        // 需要计算 Lr(p,d)，然后将其累加到 aggregated_cost_output

        __private float new_L[MAX_DISP]; // 存储当前像素的新 L 值
        float min_prev_L = FLT_MAX; // 寻找 min_k(Lr(p-r, k))

        // 找到前一个像素的最小 L 值
        for (int k = 0; k < D; ++k) {
            min_prev_L = fmin(min_prev_L, L[k]);
        }

        for (int d = 0; d < D; ++d) {
            float term1 = L[d]; // Lr(p-r, d)
            float term2 = (d > 0) ? (L[d-1] + P1) : FLT_MAX; // Lr(p-r, d-1)+P1
            float term3 = (d < D - 1) ? (L[d+1] + P1) : FLT_MAX; // Lr(p-r, d+1)+P1
            float term4 = min_prev_L + p2_adjusted; // min_k(Lr(p-r, k))+P2

            // 找到 min( term1, term2, term3, term4 )
            float min_val = fmin(fmin(term1, term2), fmin(term3, term4));

            // 计算 Lr(p,d)
            new_L[d] = (float)Cp_ptr[d] + min_val - min_prev_L;
        }

        // 将新的 L 值复制到 L 数组，供下一个像素使用
        for (int d = 0; d < D; ++d) {
            L[d] = new_L[d];
        }

        // 将当前像素聚合后的代价写入输出缓冲区。
        // 假设 aggregated_cost_output 已经用 0 初始化过，这里直接累加
        __global uint32_t* agg_out_ptr = cost_agg_data + current_v * cost_volume_stride_h + current_u * cost_volume_stride_w;
        for (int d = 0; d < D; ++d) {
            //agg_out_ptr[d] += (uint32_t)L[d]; // 累加所有方向的结果
            atomic_add(&agg_out_ptr[d], (uint32_t)L[d]);
        }
        // 移动到路径的下一个像素
        current_u += u_step;
        current_v += v_step;
    } 
}