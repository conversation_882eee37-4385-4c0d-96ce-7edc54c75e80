kernel void filter_bad_prior_depth_kernel(
    global float* d_depth,
    global const float* uv_p3d,
    global const float* normals,
    global const int* idx_mat,
    int width,
    int height,
    int point_num,
    int r,
    global const float* K)
{
  const int u = get_global_id(0);
  const int v = get_global_id(1);
  if(u < 0 || u >= width || v < 0 || v >= height){
    return;
  }

  // 当前像素点对应的3d点的id存在
  int d_idx = v*width + u;
  const int idx = idx_mat[d_idx];
  // 索引越界
  if (idx >=0 && idx >= point_num )
    return;

  if (idx >= 0)
  {
    // 当前像素存在对应3d点，则直接获取改点的深度图值
    float3 p0 = (float3)(uv_p3d[idx * 5 + 2], uv_p3d[idx * 5 + 3], uv_p3d[idx * 5 + 4]);
    p0 = p0 * 0.001f; // 将三维点转换为m单位    
    d_depth[d_idx] = p0.z;
  }
  
  else {
    float z = d_depth[d_idx];
    float2 p2d = (float2)(u, v);
    float2 p2d_r = p2d;
    float min_d = FLT_MAX;
    int p_min_idx = idx;
    barrier(CLK_LOCAL_MEM_FENCE);
    // 查找邻域内最近的3d点
    for(int dv=-r; dv<=r; dv++) {
      for(int du=-r; du<=r; du++) {
        int v1 = v+dv;
        int u1 = u+du;
        if(v1<0 || v1>=height || u1<0 || u1>=width) continue;
        int idx1 = v1 * width + u1;
        int p_idx = idx_mat[idx1];
        if(p_idx < 0 || p_idx >= point_num) continue;
        float2 p2d_r_cur = (float2)(u1, v1);
        float2 p2pr = p2d_r_cur - p2d;
        float distance = sqrt(dot(p2pr, p2pr));
        if(distance < min_d){
          min_d = distance;
          p2d_r = p2d_r_cur;
          p_min_idx = p_idx;
        }
      }
    }

    //if(d_idx % 10 == 0) printf("d_idx %d, %d, %d, p_min_idx %d, %f, %f\n", d_idx, u, v, p_min_idx, p2d_r.x, p2d_r.y)
    if(min_d < FLT_MAX){        
      float3 n_3d = (float3)(normals[p_min_idx * 3 + 0], normals[p_min_idx * 3 + 1], normals[p_min_idx * 3 + 2]);
      float3 p0 = (float3)(uv_p3d[p_min_idx * 5 + 2], uv_p3d[p_min_idx * 5 + 3], uv_p3d[p_min_idx * 5 + 4]);
      p0 = p0 * 0.001f;           // 将三维点转换为m单位   
      if(2*dot(n_3d, n_3d) < 1.f) // 如果法向量的模长小于阈值，则认为是无效的
      {
        return;
      }
      float4 abcd = (float4)(n_3d.x * K[0], n_3d.y * K[1], n_3d.z + n_3d.x * K[2] + n_3d.y * K[3], -dot(n_3d, p0));
      float delta = fabs(z * (abcd.x*u+abcd.y*v+abcd.z)+abcd.w);
      //printf("%f \n", delta);
      if(delta > 0.1f)
      {
        d_depth[d_idx] = 0.f;
      }
    }
  }
}