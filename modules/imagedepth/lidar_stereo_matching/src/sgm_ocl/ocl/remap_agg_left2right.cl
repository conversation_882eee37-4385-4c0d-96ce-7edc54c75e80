#define uint8_t uchar
#define int16_t short
#define uint32_t uint

__kernel void remap_agg_left2right_kernel(
    __global uint32_t* right_remapped_cost_volume,       // 输出：重映射后的右图代价体
    __global const uint32_t* left_agg_cost_volume,       // 输入：已聚合的左图代价体
    int width,
    int height,
    int min_disp,  // param_.min_disp_
    int num_disp // param_.num_disp_    
    )
 {
    // 获取当前工作项的全局 ID (对应图像的像素坐标 u, v)
    int u = get_global_id(0);
    int v = get_global_id(1);

    // 边界检查：确保 u, v 在有效范围内
    if (u >= width || v >= height) {
        return;
    }

    // 计算代价体中每一"层"的步长 (width * num_disp)
    int cost_volume_plane_stride = width * num_disp; 

    // 获取当前像素 (u,v) 在左图代价体中的起始指针
    const uint32_t* Sp = left_agg_cost_volume + v * cost_volume_plane_stride + u * num_disp;

    // 遍历所有视差 d
    for (int d = 0; d < num_disp; ++d) {
        // 从左图代价体中获取当前 (u,v,d) 的代价
        uint32_t cost_val = Sp[d];

        // 根据视差关系计算在右图中的对应 x 坐标 (u_r)
        // C++代码中的 `u_r = u - param_.min_disp_ - d;`
        // 这里的 `d` 是 0 到 `num_disp-1` 的索引，代表的是相对于 `min_disp` 的偏移。
        // 因此，实际的视差值是 `min_disp + d`。
        // 那么，`u_r = u - (min_disp + d);`
        int u_r = u - (min_disp + d);

        // 检查计算出的 u_r 是否在右图的有效宽度范围内
        if (u_r >= 0 && u_r < width) {
            // 将左图的代价 (u,v,d) 写入到右图重映射代价体中对应的 (u_r,v,d) 位置。
            // 这意味着：如果左图像素 (u,v) 匹配右图像素 (u_r,v) 的代价是 cost_val，
            // 那么右图像素 (u_r,v) 匹配左图像素 (u,v) 的代价也近似为 cost_val。
            right_remapped_cost_volume[v * cost_volume_plane_stride + u_r * num_disp + d] = cost_val;
        }
        // 如果 u_r 超出范围，则该位置保持其初始值（param_.max_cost_），不进行写入。
    }
}