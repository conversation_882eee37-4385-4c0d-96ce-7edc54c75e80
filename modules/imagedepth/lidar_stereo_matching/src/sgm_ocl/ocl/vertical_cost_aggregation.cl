// --- 宏定义 ---
// MAX_DISP: 总视差数量 256
// DP_BLOCK_SIZE: 每个线程处理的视差数量，16
// SUBGROUP_SIZE: 硬件子组大小 16
// WARP_SIZE: 16
// BLOCK_SIZE: 128 (WARP_SIZE * 8)

// --- 相关的宏定义 ---
@MAX_DISP@
@WARP_SIZE@
@DP_BLOCK_SIZE@
@SUBGROUP_SIZE@
@SIZE@
@BLOCK_SIZE@

#define uint8_t uchar
#define int16_t short
#define uint16_t unsigned short
#define uint32_t uint

// #define SUBGROUP_SIZE (MAX_DISP / DP_BLOCK_SIZE) // 256 / 16
// #define BLOCK_SIZE (WARP_SIZE * 8) // 16 * 8

// 计算每个工作组处理的路径数量
#define PATHS_PER_BLOCK (BLOCK_SIZE / SUBGROUP_SIZE)
#define PATHS_PER_WARP (WARP_SIZE / SUBGROUP_SIZE) // 1  SUBGROUP_SIZE = 16
#define RIGHT_BUFFER_SIZE (MAX_DISP + PATHS_PER_BLOCK) // 256+16
#define RIGHT_BUFFER_ROWS (RIGHT_BUFFER_SIZE / DP_BLOCK_SIZE) // 17

inline void store_uint8_vector(global uint8_t* dest, const int N, const uint32_t* ptr)
{
    if (N == 16)
    {
        uchar16 vv;
        uchar* v = (uchar*)&vv;
#pragma unroll
        for (int i = 0; i < 16; ++i)
            v[i] = (uint8_t)ptr[i];
        *((global uchar16*)dest) = vv;
    }
    else if (N == 8)
    {
        uchar8 vv;
        uchar* v = (uchar*)&vv;
#pragma unroll
        for (int i = 0; i < 8; ++i)
            v[i] = (uint8_t)ptr[i];
        *((global uchar8*)dest) = vv;

    }
    else
    {
#pragma unroll
        for (int i = 0; i < N; ++i)
            dest[i] = (uint8_t)ptr[i];
    }
}

void store_vector(global uint32_t* dest, const int N, const uint32_t* ptr)
{
#pragma unroll
    for (int i = 0; i < N; ++i)
        //ptr[i];
        atomic_add(&dest[i], ptr[i]); // 使用原子操作以避免数据竞争
        //dest[i] += ptr[i];
}

// --- DynamicProgramming 结构体 ---
typedef struct {
    uint32_t last_min;            // 上一个像素的最小聚合代价
    uint32_t dp[DP_BLOCK_SIZE];   // 当前线程负责的视差块的聚合代价
} DynamicProgramming;

// --- Utility 函数 (需要添加到你的 common.cl 或其他头文件中) ---
// 生成子组洗牌掩码
inline uint32_t generate_mask() {
    return (uint32_t)((1u << SUBGROUP_SIZE) - 1u);
}

// -----------------------------------------------------------------------------

// init 函数
void init(DynamicProgramming* dp) {
    dp->last_min = 0;
    for (unsigned int i = 0; i < DP_BLOCK_SIZE; ++i) { dp->dp[i] = 0; }
}

// update 函数 (核心 SGM DP 逻辑，借鉴 aggregate_horizontal_path_kernel)
// local_costs: 当前线程负责的视差块的 C(p,d) 值
// p1, p2: SGM 平滑惩罚参数
// last_min_prev_pixel: 上一个像素的所有视差的 L 值中的最小值
// shfl_prev_dp_val: 从前一个线程（d-1方向）获取的 dp 块的最后一个值
// shfl_next_dp_val: 从后一个线程（d+1方向）获取的 dp 块的第一个值
// shfl_mask: 用于子组洗牌的掩码 (如果直接使用 __shfl_up_sync 和 __shfl_down_sync)
void update_dp(DynamicProgramming* dp,
               const uint32_t* local_costs,
               uint32_t p1,
               uint32_t p2,
               uint32_t last_min_prev_pixel,
               __local uint32_t* shfl_prev_dp_val, // 从前一个线程获取的 dp 块的最后一个值
               __local uint32_t* shfl_next_dp_val // 从后一个线程获取的 dp 块的第一个值
               ) 
{ // 硬件 lane_id (0 to SUBGROUP_SIZE-1)

    const uint32_t lane_id = get_local_id(0) % SUBGROUP_SIZE;
    const uint32_t dp0 = dp->dp[0];
    shfl_next_dp_val[get_local_id(0)] = 0;
    barrier(CLK_LOCAL_MEM_FENCE);

    uint32_t lazy_out = 0, local_min = 0;
    {
        const unsigned int k = 0;
        const int shfl_prev_idx = max(0, (int)get_local_id(0) - 1);
        shfl_prev_dp_val[get_local_id(0)] = dp->dp[DP_BLOCK_SIZE - 1];
        barrier(CLK_LOCAL_MEM_FENCE);
        const uint32_t prev = shfl_prev_dp_val[shfl_prev_idx];
        uint32_t out = min(dp->dp[k] - dp->last_min, p2);
        if (lane_id != 0) { out = min(out, prev - dp->last_min + p1); }
        out = min(out, dp->dp[k + 1] - dp->last_min + p1);
        lazy_out = local_min = out + local_costs[k];
    }
    for (unsigned int k = 1; k + 1 < DP_BLOCK_SIZE; ++k)
    {
        uint32_t out = min(dp->dp[k] - dp->last_min, p2);
        out = min(out, dp->dp[k - 1] - dp->last_min + p1);
        out = min(out, dp->dp[k + 1] - dp->last_min + p1);
        dp->dp[k - 1] = lazy_out;
        lazy_out = out + local_costs[k];
        local_min = min(local_min, lazy_out);
    }
    {
        shfl_prev_dp_val[get_local_id(0)] = dp0;
        barrier(CLK_LOCAL_MEM_FENCE);
        const unsigned int k = DP_BLOCK_SIZE - 1;
        const int shfl_next_idx = min(BLOCK_SIZE - 1, (int)get_local_id(0) + 1);
        const uint32_t next = shfl_prev_dp_val[shfl_next_idx];
        uint32_t out = min(dp->dp[k] - dp->last_min, p2);
        out = min(out, dp->dp[k - 1] - dp->last_min + p1);
        if (lane_id + 1 != SUBGROUP_SIZE)
        {
            out = min(out, next - dp->last_min + p1);
        }
        dp->dp[k - 1] = lazy_out;
        dp->dp[k] = out + local_costs[k];
        local_min = min(local_min, dp->dp[k]);
    }
    int lid = get_local_id(0);
    shfl_next_dp_val[lid] = local_min;
    barrier(CLK_LOCAL_MEM_FENCE);
    //calculating subgroup minimum
    for (int i = SUBGROUP_SIZE / 2; i > 0; i >>= 1)
    {
        if (lane_id < i)
        {
            shfl_next_dp_val[lid] = min(shfl_next_dp_val[lid], shfl_next_dp_val[lid + i]);
        }
        barrier(CLK_LOCAL_MEM_FENCE);
    }
    int sub_group_idx = get_local_id(0) / SUBGROUP_SIZE;
    dp->last_min = shfl_next_dp_val[sub_group_idx * SUBGROUP_SIZE];
}

// --- Kernel 函数定义 ---
__kernel void vertical_cost_aggregation_kernel(
    __global uint8_t* cost_agg_data,                  // 输出：当前路径方向的累加代价体 (H*W*D)
    __global const uint32_t* input_data_left_feature,  // 输入特征（Census）数据
    __global const uint32_t* input_data_right_feature, // 输入特征（Census）数据
    __global const uint32_t* input_data_left_gx_feature,  // 输入梯度图的特征（Census）数据
    __global const uint32_t* input_data_right_gx_feature, // 输入梯度图的特征（Census）数据
    int width,
    int height,
    int min_disp, // 实际 min_disp
    uint16_t p1,
    uint16_t p2, 
    int path_dir_x,                              // 路径方向的 x 分量 (-1, 0, 1)
    int path_dir_y,
    uint16_t weight_gray,
    uint16_t weight_grad)                              // 路径方向的 y 分量 (-1, 0, 1)
{    
    if (width == 0 || height == 0) {
        return;
    }

    __local uint32_t right_buffer[2 * DP_BLOCK_SIZE][RIGHT_BUFFER_ROWS + 1];
    __local uint32_t right_gx_buffer[2 * DP_BLOCK_SIZE][RIGHT_BUFFER_ROWS + 1];
    
    //buffer for shuffle 
    __local uint32_t shfl_buffer[BLOCK_SIZE]; // BLOCK_SIZE = 128
    __local uint32_t local_min_shared[BLOCK_SIZE];

    DynamicProgramming dp;
    init(&dp);
    const unsigned int warp_id = get_local_id(0) / WARP_SIZE;
    const unsigned int group_id = get_local_id(0) % WARP_SIZE / SUBGROUP_SIZE;
    const unsigned int lane_id = get_local_id(0) % SUBGROUP_SIZE;
    const unsigned int shfl_mask =  generate_mask() << (group_id * SUBGROUP_SIZE); // SUBGROUP SIZE

    const unsigned int x = get_group_id(0) * PATHS_PER_BLOCK + warp_id * PATHS_PER_WARP + group_id;
    const unsigned int right_x0 = get_group_id(0) * PATHS_PER_BLOCK;
    const unsigned int dp_offset = lane_id * DP_BLOCK_SIZE;

    const unsigned int right0_addr = (right_x0 + PATHS_PER_BLOCK - 1) - x + dp_offset;
    const unsigned int right0_addr_lo = right0_addr % DP_BLOCK_SIZE;
    const unsigned int right0_addr_hi = right0_addr / DP_BLOCK_SIZE;

    for (unsigned int iter = 0; iter < height; ++iter)
    {
        const unsigned int y = (path_dir_y > 0 ? iter : height - 1 - iter);
        // Load left to register
        uint32_t left_value;
        uint32_t left_gx_value;
        if (x < width)
        {
            left_value = input_data_left_feature[x + y * width];
            left_gx_value = input_data_left_gx_feature[x + y * width];
        }
        // Load right to smem
        for (unsigned int i0 = 0; i0 < RIGHT_BUFFER_SIZE; i0 += BLOCK_SIZE)
        {
            const unsigned int i = i0 + get_local_id(0);
            if (i < RIGHT_BUFFER_SIZE)
            {
                const int right_x = (int)(right_x0 + PATHS_PER_BLOCK - 1 - i - min_disp);
                uint32_t right_value = 0;
                uint32_t right_gx_value = 0;
                if (0 <= right_x && right_x < (int)(width)) 
                {
                    right_value = input_data_right_feature[right_x + y * width];
                    right_gx_value = input_data_right_gx_feature[right_x + y * width];
                }
                const unsigned int lo = i % DP_BLOCK_SIZE;
                const unsigned int hi = i / DP_BLOCK_SIZE;
                right_buffer[lo][hi] = right_value;
                right_gx_buffer[lo][hi] = right_gx_value;
                if (hi > 0)
                {
                    right_buffer[lo + DP_BLOCK_SIZE][hi - 1] = right_value;
                    right_gx_buffer[lo + DP_BLOCK_SIZE][hi - 1] = right_gx_value;
                }
            }
        }
        barrier(CLK_LOCAL_MEM_FENCE);
        // Compute
        if (x < width)
        {
            uint32_t right_values[DP_BLOCK_SIZE]; // DP_BLOCK_SIZE = 16
            uint32_t right_gx_values[DP_BLOCK_SIZE];
            for (unsigned int j = 0; j < DP_BLOCK_SIZE; ++j)
            {
                right_values[j] = right_buffer[right0_addr_lo + j][right0_addr_hi];
                right_gx_values[j] = right_gx_buffer[right0_addr_lo + j][right0_addr_hi];
            }
            uint32_t local_costs[DP_BLOCK_SIZE];
            for (unsigned int j = 0; j < DP_BLOCK_SIZE; ++j)
            {
                uint32_t local_cost_gray = popcount(left_value ^ right_values[j]);
                uint32_t local_cost_grad = popcount(left_gx_value ^ right_gx_values[j]);
                local_costs[j] = weight_gray * local_cost_gray + weight_grad * local_cost_grad; //popcount(left_value ^ right_values[j]);
            }
            update_dp(&dp, local_costs, p1, p2, shfl_mask, shfl_buffer, local_min_shared);
            store_uint8_vector(&cost_agg_data[dp_offset + x * MAX_DISP + y * MAX_DISP * width], DP_BLOCK_SIZE, dp.dp);
        }
        //barrier(CLK_LOCAL_MEM_FENCE);
    }
}