@DEPTH_FIXED_SCALE@

#define uint32_t uint

kernel void filter_bad_prior_depth_kernel(
    global uint32_t* d_depth,
    global const float* uv_p3d,
    global const float* normals,
    global const int* idx_mat,
    int width,
    int height,
    int point_num,
    int r,
    global const float* K)
{
  const int idx = get_global_id(0);
  if (idx >= point_num)
      return;
  // 获取对应的三维点
  int u = (int)uv_p3d[idx * 5 + 0];
  int v = (int)uv_p3d[idx * 5 + 1];  
  if (u < 0 || u >= width || v < 0 || v >= height)
  {
    return;
  }
  float3 p0 = (float3)(uv_p3d[idx * 5 + 2], uv_p3d[idx * 5 + 3], uv_p3d[idx * 5 + 4]);
  p0 = p0 * 0.001f; // 将三维点转换为m单位

  int d_idx = v*width + u;
  d_depth[d_idx] = DEPTH_FIXED_SCALE * (p0.z);

  // 获取对应的法向量
  float3 n_3d = (float3)(normals[idx * 3 + 0], normals[idx * 3 + 1], normals[idx * 3 + 2]);
  if(2*dot(n_3d, n_3d) < 1.f) // 如果法向量的模长小于阈值，则认为是无效的
  {
    return;
  }
  // 计算法向量的方向
  // TODO: 以下循环存在依赖，邻域的z在不同线程中被同时更新和使用，这会使多线程的执行结果有一定随机性。
  float4 abcd = (float4)(n_3d.x * K[0], n_3d.y * K[1], n_3d.z + n_3d.x * K[2] + n_3d.y * K[3], -dot(n_3d, p0));
  for(int dv=-r; dv<=r; dv++)
  {
    for(int du=-r; du<=r; du++)
    {
      int v1 = v+dv;
      int u1 = u+du;
      if(v1<0 || v1>=height || u1<0 || u1>=width) continue;
      // 检查索引是否在idx_mat中有效，有Lidar 3D点的位置不过滤
      int idx1 = v1 * width + u1;
      if(idx_mat[idx1] >= 0) continue;
      float z = d_depth[idx1] / DEPTH_FIXED_SCALE;
      if(z>0)
      {
        float delta = fabs(z * (abcd.x*u1+abcd.y*v1+abcd.z)+abcd.w);
        if(delta > 0.1f)
        {
          d_depth[idx1] = 0;
        }
      }
    }
  }
}