#define uint16_t unsigned short
#define INVALID_DISP 0

kernel void correct_range_kernel(global uint16_t* d_disp,
    int width,
    int height,
    int pitch,
    int min_disp_scaled,
    int invalid_disp_scaled)
{
    const int x = get_global_id(0);
    const int y = get_global_id(1);

    if (x >= width || y >= height)
    {
        return;
    }

    uint16_t d = d_disp[y * pitch + x];
    if (d == INVALID_DISP)
    {
        d_disp[y * pitch + x] = d;
    }
    else
    {
        d += min_disp_scaled;
        d_disp[y * pitch + x] = d;
    }
    
}