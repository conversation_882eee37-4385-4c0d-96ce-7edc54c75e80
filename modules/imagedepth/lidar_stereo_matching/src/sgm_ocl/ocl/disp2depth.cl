@DEPTH_FIXED_SCALE@

#define uint16_t unsigned short
#define uint32_t uint

kernel void disp2depth_kernel(
    global uint16_t* d_depth,
    global const uint16_t* d_disp,
    global const uint32_t* d_prior_depth,
    int width,
    int height,
    float baseline,
    float focal_length,
    float doffs,
    float sub_pixel_scale,
    int enable_lidar, 
    float semi_rel_error,
    float thres_cross_check)
{
    const int x = get_global_id(0);
    const int y = get_global_id(1);

    if (x >= width || y >= height)
    {
        return;
    }

    uint16_t disp_val = d_disp[y * width + x]; //(float)d_disp[y * width + x] * sub_pixel_scale;
    uint32_t z_semi_fixed = d_prior_depth[y * width + x];
    float z_semi = z_semi_fixed / DEPTH_FIXED_SCALE; 

    if(enable_lidar == 1 && z_semi_fixed < UINT_MAX){
        d_depth[y * width + x] = (uint16_t)z_semi;
    }
    if (disp_val > 0)
    {        
        // Convert disparity to float        
        float d = (float)disp_val * sub_pixel_scale; // Scale disparity to account for subpixel precision
        float bf = baseline * focal_length;
        // Convert disparity to depth
        float z_vis = bf / (d + doffs);        

        if(enable_lidar == 1){
            if(z_semi > 0){
                float delta_z = fabs(z_semi - z_vis);
                if(delta_z > semi_rel_error*z_semi &&  bf * z_semi - z_vis > thres_cross_check * z_semi * z_vis)
                {
                    z_vis = 0.;
                }
            }
        }

        d_depth[y * width + x] = (uint16_t)z_vis;
    }
    
}