#define uint8_t uchar
#define int16_t short
#define uint16_t unsigned short

// 定义局部工作组的维度
// 主机端设置的 local_size[0]
#define BLOCK_DIM_X_1D 1024 // 假设一维局部工作组的长度

#define BLOCK_DIM_X 32
#define BLOCK_DIM_Y 32
#define KERNEL_R_H_SAD 4
#define KERNEL_R_W_SAD 4

// 局部内存中Block的实际尺寸，包含了邻域
// 由于现在是处理三维数据，局部内存可能需要更大的范围来容纳一个切片或多切片数据
// 这里我们仍然沿用之前的逻辑，假设一个工作组处理 BLOCK_DIM_X_1D x 1 的区域
// 但为了兼容原来的2D局部内存结构，我们保留2D的局部内存定义，并手动计算其1D索引
// LOCAL_BLOCK_WIDTH 和 LOCAL_BLOCK_HEIGHT 仍然是针对原始2D图像块的定义
#define LOCAL_BLOCK_WIDTH  (BLOCK_DIM_X + 2 * KERNEL_R_W_SAD) // BLOCK_DIM_X 仍用32
#define LOCAL_BLOCK_HEIGHT (BLOCK_DIM_Y + 2 * KERNEL_R_H_SAD) // BLOCK_DIM_Y 仍用32

// 假设我们仍然想在局部内存中缓存一个 BLOCK_DIM_X x BLOCK_DIM_Y 的区域
// 且每个工作项仍然负责加载一个像素。
// LOCAL_MEM_SIZE 定义了需要加载到局部内存的元素的总数
#define LOCAL_MEM_LOAD_WIDTH (BLOCK_DIM_X + 2 * KERNEL_R_W_SAD)
#define LOCAL_MEM_LOAD_HEIGHT (BLOCK_DIM_Y + 2 * KERNEL_R_H_SAD)
#define LOCAL_MEM_SIZE (LOCAL_MEM_LOAD_WIDTH * LOCAL_MEM_LOAD_HEIGHT)


__kernel void vision_cost_kernel(    
    __global uint16_t* output_data,             // 输出Cost数据
    __global const uint8_t* input_data_left,    // 输入图像数据
    __global const uint8_t* input_data_right,   // 输入图像数据
    __global const int16_t* input_data_left_feature,  // 输入图像数据
    __global const int16_t* input_data_right_feature,  // 输入图像数据
    int width,                      
    int height,                        
    int min_disp,
    int max_disp,
    int r_h_sad,                              
    int r_w_sad,                       
    uint16_t max_gray_cost,                   
    uint16_t max_grad_cost,                   
    uint16_t weight_gray,                     
    uint16_t weight_grad,                     
    uint16_t scale_vis)                 
{
    // 声明局部内存，用于存储当前工作组的图像块
    // 将2D局部内存扁平化为1D数组，方便1D局部ID访问
    __local uint8_t local_block_left[LOCAL_MEM_SIZE];
    __local uint8_t local_block_right[LOCAL_MEM_SIZE];
    __local int16_t local_block_left_feature[LOCAL_MEM_SIZE];
    __local int16_t local_block_right_feature[LOCAL_MEM_SIZE];

    // 获取当前工作项的全局 ID (对应输出Cost数据的1D索引)
    int global_id_1d = get_global_id(0);

    // 获取当前工作项在工作组内的局部 ID
    int local_id_1d = get_local_id(0);

    // 获取当前工作组的 ID
    int group_id_1d = get_group_id(0);

    // 计算当前工作组的全局尺寸 (由主机端设置的 global_size[0] / local_size[0] 决定)
    int group_size_1d = get_local_size(0); // 即 BLOCK_DIM_X_1D

    // 根据全局1D ID 计算 (u, v, d) 坐标
    // output_data 布局为 [v][u][d]
    int u_total_pixels = width * max_disp;
    int d = global_id_1d % max_disp;
    int uv_idx = global_id_1d / max_disp;
    int u = uv_idx % width;
    int v = uv_idx / width;

    // 边界检查：确保u, v, d在有效范围内
    // 虽然global_id已经限制了范围，但为了代码健壮性，这里保留
    if (u >= width || v >= height || d >= max_disp) {
        return;
    }

    // --- 数据加载阶段 ---
    // 每个工作组负责加载一个完整的 BLOCK_DIM_X x BLOCK_DIM_Y 区域到局部内存
    // 尽管是1D工作组，但我们仍然需要将2D区域加载到局部内存
    // 假设每个工作项负责加载 `LOCAL_MEM_SIZE / group_size_1d` 个像素
    // 或者更简单，让每个工作项按照其 local_id_1d 偏移量来加载数据
    
    // 计算当前工作组在全局内存中处理的Block的起始像素坐标
    // 这里的逻辑需要根据主机端如何将三维问题映射到一维全局ID来调整。
    // 如果一个工作组仍然负责处理一个 BLOCK_DIM_X x BLOCK_DIM_Y 的图像块的所有视差
    // 那么 group_x 和 group_y 仍然需要从 group_id_1d 中推导出来。
    // 这里我们假设全局ID的映射是这样的：global_id = d + u*max_disp + v*width*max_disp
    // 因此，一个工作组处理的区域是围绕某个 (u,v) 块和所有 d 值。
    // 这意味着一个工作组可能需要加载一个 (BLOCK_DIM_X + 2*R_W_SAD) x (BLOCK_DIM_Y + 2*R_H_SAD)
    // 的块，并且所有工作项都参与加载这个块。

    // 为了简化，我们假设 local_size[0] = BLOCK_DIM_X * BLOCK_DIM_Y
    // 这样每个工作项对应局部内存中的一个加载点
    // 实际应用中，local_size[0] 应该根据硬件特性（如warp/wavefront大小）进行优化
    // 这里的示例假设 local_size[0] 等于 LOCAL_MEM_SIZE，即每个工作项加载一个局部内存元素

    // 对于1D工作组，一种常见的加载策略是：每个工作项加载多个数据点，
    // 或者工作组内的所有工作项协同加载整个区域。
    // 为了与原代码的加载逻辑保持一致，我们将局部ID映射回2D加载坐标
    // 然后让每个工作项加载一个或多个像素。

    // 计算当前工作组负责的2D图像块的左上角全局坐标
    // 这需要根据主机端如何将3D全局ID映射到1D。
    // 假设主机端是按照 (v, u, d) 顺序将3D空间展平到1D的，
    // 并且每个工作组仍然负责处理一个 BLOCK_DIM_X x BLOCK_DIM_Y 的二维图像区域的所有视差层。
    // 那么我们需要从 group_id_1d 反推出 group_x 和 group_y。

    // global_size_x_per_group = width / BLOCK_DIM_X (向上取整)
    // global_size_y_per_group = height / BLOCK_DIM_Y (向上取整)
    // num_groups_2d = global_size_x_per_group * global_size_y_per_group * max_disp
    // group_id_2d_uv = group_id_1d / max_disp
    // group_y = group_id_2d_uv / global_size_x_per_group
    // group_x = group_id_2d_uv % global_size_x_per_group

    // 由于原始Kernel是以 BLOCK_DIM_X 和 BLOCK_DIM_Y 为基础进行局部内存加载的，
    // 我们需要调整这里的加载逻辑。最直接的方式是让每个工作项负责加载 `LOCAL_MEM_SIZE / get_local_size(0)` 个数据。
    // 或者，让所有 local_id_1d 参与加载整个 `LOCAL_MEM_SIZE` 区域。
    // 这里我们选择后者，让每个 local_id_1d 负责加载其对应的局部内存位置。

    // 每个工作项（local_id_1d）负责加载其在局部内存中的对应位置。
    // local_id_1d 映射到 (load_local_y, load_local_x)
    int load_local_y = local_id_1d / LOCAL_MEM_LOAD_WIDTH;
    int load_local_x = local_id_1d % LOCAL_MEM_LOAD_WIDTH;

    // 但这里有个问题：一个工作组的local_id_1d通常远小于 LOCAL_MEM_SIZE。
    // 因此，需要循环加载。
    // 循环加载的次数取决于 LOCAL_MEM_SIZE 和 local_size_1d
    int num_elements_to_load = LOCAL_MEM_SIZE;
    int stride = get_local_size(0); // 步长为局部工作组的大小

    // 为了确定 block_start_x 和 block_start_y
    // 我们需要知道当前工作组是负责哪个 (u, v) 区域的。
    // 假设主机端调度是这样的：一个工作组负责计算一个或多个 (u,v) 点的所有视差。
    // 最常见的映射是：全局ID的u,v,d是线性的。
    // 比如：global_id = u + v * width + d * width * height
    // 或者：global_id = d + u * max_disp + v * width * max_disp (当前代码的假设)
    // 那么，group_x 和 group_y 可以通过其所包含的 (u,v) 中心点来推断。
    // 如果每个工作组仍然处理一个 BLOCK_DIM_X x BLOCK_DIM_Y 的区域，那么：
    int block_start_x = (u / BLOCK_DIM_X) * BLOCK_DIM_X;
    int block_start_y = (v / BLOCK_DIM_Y) * BLOCK_DIM_Y;


    for (int i = local_id_1d; i < num_elements_to_load; i += stride) {
        int current_load_local_y = i / LOCAL_MEM_LOAD_WIDTH;
        int current_load_local_x = i % LOCAL_MEM_LOAD_WIDTH;

        // 计算当前工作项在全局内存中负责加载的像素坐标
        int load_global_x = block_start_x + current_load_local_x - KERNEL_R_W_SAD;
        int load_global_y = block_start_y + current_load_local_y - KERNEL_R_H_SAD;
        
        // 处理全局内存的边界情况 (模拟 CLAMP_TO_EDGE)
        int clamped_global_x = clamp(load_global_x, 0, width - 1);
        int clamped_global_y = clamp(load_global_y, 0, height - 1);

        // 从全局内存读取像素值，并存储到局部内存
        local_block_left[i] = input_data_left[clamped_global_y * width + clamped_global_x];
        local_block_right[i] = input_data_right[clamped_global_y * width + clamped_global_x];
        local_block_left_feature[i] = input_data_left_feature[clamped_global_y * width + clamped_global_x];
        local_block_right_feature[i] = input_data_right_feature[clamped_global_y * width + clamped_global_x];
    }

    // --- 同步 ---
    // 确保所有工作项都已将数据加载到局部内存中
    barrier(CLK_LOCAL_MEM_FENCE);

    // --- 计算阶段 ---
    // 只有当当前全局 ID 对应有效的输出像素时才进行计算
    // 这里的 (u, v) 已经扣除了 SAD 窗口半径的边缘
    if (u >= r_w_sad && u < width - r_w_sad && v >= r_h_sad && v < height - r_h_sad) {
        int16_t current_pixel_cost = 0; // 初始化当前 (u,v,d) 的总代价

        // 计算右图像中心点在全局的水平坐标
        int u_r_center = u - d - min_disp;

        // 遍历SAD窗口
        // KERNEL_R_H_SAD 和 KERNEL_R_W_SAD 定义了SAD窗口的半径，所以遍历范围是 2*radius+1
        // 注意：原代码中的循环范围是 `dv <= 0` 和 `du <= 0`，这意味着它只计算了中心像素。
        // 这与 SAD (Sum of Absolute Differences) 的通常定义不符。
        // SAD 通常会在一个窗口内求和。
        // 如果 `KERNEL_R_H_SAD` 和 `KERNEL_R_W_SAD` 是窗口半径，那么循环应该为：
        // `for (int dv = -r_h_sad; dv <= r_h_sad; ++dv)`
        // `for (int du = -r_w_sad; du <= r_w_sad; ++du)`
        // 鉴于原代码的循环范围，这里保留其逻辑，只计算中心点的代价，但请注意这可能不是预期的SAD。
        // 如果需要完整的SAD，请调整循环范围。
        for (int dv = -r_h_sad; dv <= r_h_sad; ++dv) { // 遍历 SAD 窗口的垂直方向
            for (int du = -r_w_sad; du <= r_w_sad; ++du) { // 遍历 SAD 窗口的水平方向
                // 计算SAD窗口内像素在局部Block中的坐标
                // 注意：这里的 local_x, local_y 是根据全局 u, v 推导出来的
                // 如果是1D Kernel，local_x 和 local_y 不再是 get_local_id(0/1)
                // 而是当前 (u,v) 相对于其所属 BLOCK_DIM_X x BLOCK_DIM_Y 块的偏移
                int current_block_local_x = u % BLOCK_DIM_X;
                int current_block_local_y = v % BLOCK_DIM_Y;

                int local_access_v = current_block_local_y + r_h_sad + dv;
                int local_access_u = current_block_local_x + r_w_sad + du;

                // 从局部内存读取左图像数据
                // 将2D局部坐标映射回1D索引
                int local_mem_idx = local_access_v * LOCAL_MEM_LOAD_WIDTH + local_access_u;
                uint8_t gray_l = local_block_left[local_mem_idx];
                int16_t grad_l = local_block_left_feature[local_mem_idx];

                // 为了正确性，右图依然从全局内存读取，并进行边界钳制
                int v_new = clamp(v + dv, 0, height - 1);
                int u_r_new = clamp(u_r_center + du, 0, width - 1); // 右图的SAD窗口中心像素
                
                // 确保 u_r_new 在有效视差范围内（u - disp）
                if (u_r_new >=0 && u_r_new < width) {
                    uint8_t gray_r = input_data_right[v_new * width + u_r_new];
                    int16_t grad_r = input_data_right_feature[v_new * width + u_r_new];

                    // 计算灰度代价
                    uint16_t gray_cost = (uint16_t)abs(gray_l - gray_r);
                    gray_cost = min(gray_cost, max_gray_cost);

                    // 计算梯度代价
                    uint16_t grad_cost = (uint16_t)abs(grad_l - grad_r);
                    grad_cost = min(grad_cost, max_grad_cost);

                    // 累加加权代价
                    current_pixel_cost += weight_gray * gray_cost + weight_grad * grad_cost;
                } else {
                    // 如果右图SAD窗口区域超出图像边界，可以将其代价设置为最大或特定值
                    // 或者不累加到 cost 中
                    current_pixel_cost += max_gray_cost + max_grad_cost; // 例如，给一个较高的惩罚
                }
            }
        }

        // 归一化代价
        if (scale_vis > 0) {
            current_pixel_cost /= scale_vis;
        }
        // 将计算出的代价写入全局内存
        output_data[global_id_1d] = current_pixel_cost;
    } else {
        // 边缘像素不进行计算，将其输出值设为 0
        output_data[global_id_1d] = 0;
    }
}