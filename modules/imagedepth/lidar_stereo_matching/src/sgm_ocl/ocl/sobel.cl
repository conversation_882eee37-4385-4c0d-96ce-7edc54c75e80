#define uint8_t uchar
#define int16_t short
// 定义局部工作组的维度
// 主机端设置的 local_work_size[0] 和 local_work_size[1]
@BLOCK_DIM_X@
@BLOCK_DIM_Y@

// 卷积核需要的邻域大小
#define WINDOW_SIZE 1 // (-WINDOW_SIZE, 0, WINDOW_SIZE + 1)

// 局部内存中Block的实际尺寸，包含了邻域
#define LOCAL_BLOCK_WIDTH  (BLOCK_DIM_X + 2 * WINDOW_SIZE)
#define LOCAL_BLOCK_HEIGHT (BLOCK_DIM_Y + 2 * WINDOW_SIZE)

__kernel void sobel_kernel(    
    __global uint8_t* output_data_left,       // 输出Gx数据
    __global uint8_t* output_data_right,       // 输出Gx数据
    __global const uint8_t* input_data_left,  // 输入图像数据
    __global const uint8_t* input_data_right,  // 输入图像数据
    int width,                         // 图像宽度
    int height                          // 图像高度
    )                 
{
    // 声明局部内存，用于存储当前工作组的图像块
    // 存储的是float，便于后续计算
    __local float local_block_left[LOCAL_BLOCK_HEIGHT][LOCAL_BLOCK_WIDTH];
    __local float local_block_right[LOCAL_BLOCK_HEIGHT][LOCAL_BLOCK_WIDTH];

    int input_stride = width;
    int output_stride = width;
    // 获取当前工作项的全局 ID (对应输出图像的像素坐标)
    int global_x = get_global_id(0);
    int global_y = get_global_id(1);

    // 获取当前工作项在工作组内的局部 ID (对应BLOCK内的坐标)
    int local_x = get_local_id(0);
    int local_y = get_local_id(1);

    // 获取当前工作项所属工作组的 ID
    int group_x = get_group_id(0);
    int group_y = get_group_id(1);

    // 计算当前工作组在全局内存中处理的block的起始像素坐标
    int block_start_x = group_x * BLOCK_DIM_X;
    int block_start_y = group_y * BLOCK_DIM_Y;

    // --- 数据加载阶段 ---
    // 每个工作项负责从全局内存加载一部分像素到局部内存
    // 需要加载的区域是 (BLOCK_DIM_X + 2*WINDOW_SIZE) x (BLOCK_DIM_Y + 2*WINDOW_SIZE)
    // 通过循环确保工作组内的所有工作项合作完成Block的填充
    for (int y_offset = 0; y_offset < LOCAL_BLOCK_HEIGHT; y_offset += BLOCK_DIM_Y) {
        for (int x_offset = 0; x_offset < LOCAL_BLOCK_WIDTH; x_offset += BLOCK_DIM_X) {
            // 计算当前工作项在局部内存中负责加载的像素坐标
            int load_local_x = local_x + x_offset;
            int load_local_y = local_y + y_offset;

            // 计算当前工作项在全局内存中负责加载的像素坐标
            int load_global_x = block_start_x + load_local_x - WINDOW_SIZE;
            int load_global_y = block_start_y + load_local_y - WINDOW_SIZE;

            if (load_local_x < LOCAL_BLOCK_WIDTH && load_local_y < LOCAL_BLOCK_HEIGHT) {
                // 处理全局内存的边界情况 (模拟 CLAMP_TO_EDGE)
                // 确保读取的全局坐标在图像范围内
                int clamped_global_x = clamp(load_global_x, 0, width - 1);
                int clamped_global_y = clamp(load_global_y, 0, height - 1);

                // 从全局内存读取像素值，并转换为 float
                float pixel_val_left = (float)input_data_left[clamped_global_y * input_stride + clamped_global_x];
                local_block_left[load_local_y][load_local_x] = pixel_val_left;
                float pixel_val_right = (float)input_data_right[clamped_global_y * input_stride + clamped_global_x];
                local_block_right[load_local_y][load_local_x] = pixel_val_right;
            }
        }
    }

    // --- 同步 ---
    // 确保所有工作项都已将数据加载到局部内存中
    barrier(CLK_LOCAL_MEM_FENCE);

    // --- 计算阶段 ---
    // 只有当当前全局 ID 对应有效的输出像素时才进行计算
    if (global_x >= WINDOW_SIZE && global_x < width - WINDOW_SIZE && global_y >= WINDOW_SIZE && global_y < height - WINDOW_SIZE) {
        // 从局部内存中读取 3x3 邻域像素值
        // 注意：这里的 local_x 和 local_y 是相对于局部Block的坐标，
        // 实际访问局部内存时需要加上 WINDOW_SIZE (即 1)
        float p00 = local_block_left[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE - 1];
        float p01 = local_block_left[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE];
        float p02 = local_block_left[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE + 1];

        float p10 = local_block_left[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE - 1];
        float p12 = local_block_left[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE + 1];

        float p20 = local_block_left[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE - 1];
        float p21 = local_block_left[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE];
        float p22 = local_block_left[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE + 1];

        float left_Gx = (p02 + 2 * p12 + p22) - (p00 + 2 * p10 + p20);
        uint8_t final_left_gx = (uint8_t)clamp(round(left_Gx), (float)SHRT_MIN, (float)SHRT_MAX);

        p00 = local_block_right[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE - 1];
        p01 = local_block_right[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE];
        p02 = local_block_right[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE + 1];

        p10 = local_block_right[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE - 1];
        p12 = local_block_right[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE + 1];

        p20 = local_block_right[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE - 1];
        p21 = local_block_right[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE];
        p22 = local_block_right[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE + 1];

        float right_Gx = (p02 + 2 * p12 + p22) - (p00 + 2 * p10 + p20);
        uint8_t final_right_gx = (uint8_t)clamp(round(right_Gx), (float)SHRT_MIN, (float)SHRT_MAX);

        // 计算输出数据在 `output_data` 数组中的索引
        int output_idx = global_y * (output_stride / sizeof(uint8_t)) + global_x;


        // 写入结果到输出缓冲区
        output_data_left[output_idx] = final_left_gx;
        output_data_right[output_idx] = final_right_gx;

    } else {
        // 边缘像素不进行 Sobel 计算，将其输出值设为 0
        // 这确保了输出图像和输入图像具有相同的大小，
        // 尽管边缘部分没有经过完整的 Sobel 运算。
       int output_idx = global_y * (output_stride / sizeof(uint8_t)) + global_x;
       output_data_right[output_idx] = 0;
       output_data_right[output_idx] = 0;
    }
}