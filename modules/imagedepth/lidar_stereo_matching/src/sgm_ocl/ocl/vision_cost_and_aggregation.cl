#define uint8_t uchar
#define int16_t short
#define uint16_t unsigned short
#define uint32_t uint

// --- 相关的宏定义 ---
@MAX_DISP@
@KERNEL_R_H_SAD@
@KERNEL_R_W_SAD@

__kernel void sgm_cost_and_aggregation_kernel(    
    __global uint32_t* cost_agg_data,             // 输出：累加后的最终代价体 (H*W*D)
    __global const uint8_t* input_data_left,    // 输入图像数据
    __global const uint8_t* input_data_right,   // 输入图像数据
    __global const int16_t* input_data_left_feature,  // 输入图像数据
    __global const int16_t* input_data_right_feature,  // 输入图像数据
    int width,                      
    int height,                        
    int min_disp,
    int max_disp, // 实际上是 num_disparities
    uint16_t P1,
    uint16_t P2, 
    int k_diff,                      
    uint16_t max_gray_cost,                   
    uint16_t max_grad_cost,                   
    uint16_t weight_gray,                     
    uint16_t weight_grad,                     
    uint16_t scale_vis,
    int path_dir_x,                              // 路径方向的 x 分量 (-1, 0, 1)
    int path_dir_y)                              // 路径方向的 y 分量 (-1, 0, 1)
{
    // 每个工作项负责处理一条完整的 SGM 聚合路径。
    // get_global_id(0) 可以是列索引 u (for vertical paths) 或行索引 v (for horizontal paths)
    // 对于对角线路径，可能需要更复杂的映射。
    // 这里我们假设 global_id(0) 映射到路径的主轴索引。

    int main_axis_idx = get_global_id(0); // 垂直路径时是 u，水平路径时是 v

    // 确定主轴的最大范围
    int main_axis_max = (path_dir_y != 0) ? width : height;

    if (main_axis_idx >= main_axis_max || max_disp > MAX_DISP) {
        return; // 超出图像范围或视差数超出硬编码限制
    }

    int D = max_disp; // 视差数量

    // --- SGM DP 状态数组（私有内存）---
    __private float L[MAX_DISP]; // 用于存储当前像素的聚合代价 L(p,d)

    // --- 初始化 SGM DP 状态 ---
    for (int i = 0; i < D; ++i) {
        L[i] = 0.0f; // 路径聚合的 L 数组初始化为 0
    }
    int last_gray_val = -1; // 用于自适应 P2 计算

    // --- 路径遍历的起始和结束点 ---
    // 这个逻辑负责确定当前工作项要处理的路径
    int v_start, v_end, v_step;
    int u_start, u_end, u_step;

 // 根据路径方向确定遍历方式
    if (path_dir_x == 0) { // 垂直方向 (path_dir_y != 0)
        u_start = main_axis_idx; u_end = u_start + path_dir_x; u_step = 0; // x 轴固定
        v_start = (path_dir_y > 0) ? 0 : height - 1;
        v_end = (path_dir_y > 0) ? height : -1;
        v_step = path_dir_y;
    } else if (path_dir_y == 0) { // 水平方向 (path_dir_x != 0)
        v_start = main_axis_idx; v_end = v_start + path_dir_y; v_step = 0; // y 轴固定
        u_start = (path_dir_x > 0) ? 0 : width - 1;
        u_end = (path_dir_x > 0) ? width : -1;
        u_step = path_dir_x;
    } else { // 对角线方向
        if (path_dir_y > 0) { // 向上或向下对角线
            v_start = (path_dir_x > 0) ? 0 : height - 1; // 左上到右下，从 (0,0) 开始；左下到右上，从 (0,H-1) 开始
            u_start = (path_dir_x > 0) ? main_axis_idx : width - 1 - main_axis_idx;
        } else { // 向下或向上对角线
            v_start = (path_dir_x > 0) ? height - 1 : 0; // 右上到左下，从 (0,H-1) 开始；右下到左上，从 (0,0) 开始
            u_start = (path_dir_x > 0) ? main_axis_idx : width - 1 - main_axis_idx;
        }
        v_end = (path_dir_y > 0) ? height : -1;
        u_end = (path_dir_x > 0) ? width : -1;
        v_step = path_dir_y;
        u_step = path_dir_x;
    }

    int current_u = u_start;
    int current_v = v_start;

    // --- 路径遍历主循环 ---
    while ((path_dir_x == 0 || (path_dir_x > 0 ? (current_u < u_end) : (current_u > u_end))) &&
            (path_dir_y == 0 || (path_dir_y > 0 ? (current_v < v_end) : (current_v > v_end))) )
    {
      // 边界检查，确保像素在图像范围内
        if (current_u < 0 || current_u >= width || current_v < 0 || current_v >= height) {
            // 如果是边界路径，跳过这个像素，继续下一个
            current_u += u_step;
            current_v += v_step;
            continue;
        }
        // --- 1. 即时计算当前像素 (current_u, current_v) 的初始匹配代价 C(p,d) ---
        // (这部分是原 vision_cost_kernel 的核心逻辑，但没有局部内存瓦片化)
        // 注意：这里的循环发生在每个工作项内部，为当前路径上的每个像素计算 D 个视差的代价。
        // 为了避免在每个像素处重复加载整个 SAD 窗口，我们将 SAD 窗口的加载也整合进来。

        __private uint32_t current_Cp_values[MAX_DISP]; // 存储当前像素的所有视差的初始代价

        // 对于当前路径上的像素 (current_u, current_v)
        // 遍历所有可能的视差 d
        for (int d_val = 0; d_val < D; ++d_val) {
            uint32_t single_pixel_cost = 0; // 当前 (u,v,d) 的总代价

            // 计算右图像中心点在全局的水平坐标 for current_u and d_val
            int u_r_center = current_u - d_val - min_disp; // min_disp 是视差偏移

            // 遍历 SAD 窗口 (KERNEL_R_H_SAD x KERNEL_R_W_SAD)
            for (int dv = -KERNEL_R_H_SAD; dv <= KERNEL_R_H_SAD; dv++) {
                for (int du = -KERNEL_R_H_SAD; du <= KERNEL_R_H_SAD; du++) {
                    // 计算 SAD 窗口内像素的全局坐标
                    int global_v_sad = clamp(current_v + dv, 0, height - 1);
                    int global_u_left_sad = clamp(current_u + du, 0, width - 1);
                    int global_u_right_sad = clamp(u_r_center + du, 0, width - 1);

                    // 从全局内存读取灰度图像数据
                    uint8_t gray_l = input_data_left[global_v_sad * width + global_u_left_sad];
                    uint8_t gray_r = input_data_right[global_v_sad * width + global_u_right_sad];

                    // 从全局内存读取特征（梯度）图像数据
                    int16_t grad_l = input_data_left_feature[global_v_sad * width + global_u_left_sad];
                    int16_t grad_r = input_data_right_feature[global_v_sad * width + global_u_right_sad];

                    // 计算灰度代价
                    float gray_cost = fabs((float)(gray_l - gray_r));
                    gray_cost = fmin(gray_cost, (float)max_gray_cost);

                    // 计算梯度代价
                    float grad_cost = fabs((float)(grad_l - grad_r));
                    grad_cost = fmin(grad_cost, (float)max_grad_cost);

                    // 累加加权代价
                    single_pixel_cost += (uint32_t)(gray_cost * weight_gray + grad_cost * weight_grad);
                    
                    //atomic_add(&single_pixel_cost, pixel_cost);                    
                }
            }

            // 归一化代价
            if (scale_vis > 0) {
                single_pixel_cost /= scale_vis;
            }
            current_Cp_values[d_val] = single_pixel_cost; // 存储当前像素的初始代价
        }
        // --- 同步 ---
        // 确保所有工作项都已将数据加载到局部内存中
        barrier(CLK_LOCAL_MEM_FENCE);

        // --- 2. SGM DP 聚合逻辑 (使用 current_Cp_values 作为 C(p,d)) ---
        int current_gray_val = (int)input_data_left[current_v * width + current_u];

        // 计算自适应 P2
        float p2_adjusted = (float)P2;
        if (last_gray_val >= 0 && k_diff > 0) {
            int diff_val = abs(current_gray_val - last_gray_val) / k_diff;
            diff_val = max(1, diff_val);
            p2_adjusted = (float)P2 / (float)diff_val;
        }
        last_gray_val = current_gray_val;
        p2_adjusted = fmax((float)P1, p2_adjusted);

        float min_prev_L = FLT_MAX; // 寻找 min_k(Lr(p-r, k))

        // 找到前一个像素的最小 L 值
        for (int k = 0; k < D; ++k) {
            min_prev_L = fmin(min_prev_L, L[k]);
        }

        // 计算新 L 值
        __private float new_L[MAX_DISP];
        for (int d_val = 0; d_val < D; ++d_val) {
            float term1 = L[d_val]; // Lr(p-r, d)
            float term2 = (d_val > 0) ? (L[d_val-1] + P1) : FLT_MAX; // Lr(p-r, d-1)+P1
            float term3 = (d_val < D - 1) ? (L[d_val+1] + P1) : FLT_MAX; // Lr(p-r, d+1)+P1
            float term4 = min_prev_L + p2_adjusted; // min_k(Lr(p-r, k))+P2

            float min_val = fmin(fmin(term1, term2), fmin(term3, term4));
            new_L[d_val] = (float)current_Cp_values[d_val] + min_val - min_prev_L;
        }

        // 更新 L 数组，供下一个像素使用
        for (int d_val = 0; d_val < D; ++d_val) {
            L[d_val] = new_L[d_val];
        }

        // --- 3. 将当前像素聚合后的代价累加到最终输出缓冲区 ---
        int output_idx_base = current_v * width * D + current_u * D;
        __global uint32_t* agg_out_ptr = cost_agg_data + output_idx_base;
        
        for (int d_val = 0; d_val < D; ++d_val) {
            // 由于多个路径会写入同一个 final_cost_volume_accum，这里需要原子操作
            // 或者，如之前所讨论，主机端按方向顺序调用核函数，以避免并发写入
            // 假设主机端是顺序调用的，可以直接累加
            atomic_add(&agg_out_ptr[d_val], (uint32_t)L[d_val]);
        }

        // 移动到路径的下一个像素
        current_u += u_step;
        current_v += v_step;
    }
}