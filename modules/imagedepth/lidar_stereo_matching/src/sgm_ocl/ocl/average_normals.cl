kernel void average_normals_kernel(
    global float* normals,
    global int* normals_fixed,
    global int* normals_count,
    int point_num)
{
  const int idx = get_global_id(0);
  if (idx >= point_num)
      return;

  //printf("[average_normals_kernel] Normal vector at index %d: (%f, %f, %f), Normal_cout: %d\n", idx, normals[idx * 3 + 0], normals[idx * 3 + 1], normals[idx * 3 + 2], normals_count[idx]);
  if (normals_count[idx] >= 2)
  {
    // 归一化法向量
    float3 n = (float3)(normals_fixed[idx * 3 + 0], normals_fixed[idx * 3 + 1], normals_fixed[idx * 3 + 2]);
    float norm = sqrt(dot(n, n));
    if (norm > 0)
    {
      normals[idx * 3 + 0] = n.x / norm;
      normals[idx * 3 + 1] = n.y / norm;
      normals[idx * 3 + 2] = n.z / norm;
    }
    //printf("[average_normals_kernel] Normal vector at index %d: (%f, %f, %f), Normal_cout: %d\n", idx, normals[idx * 3 + 0], normals[idx * 3 + 1], normals[idx * 3 + 2], normals_count[idx]);
  } else {
    // 如果法向量计数小于2，则将法向量设置为零
    normals[idx * 3 + 0] = 0.0f;
    normals[idx * 3 + 1] = 0.0f;
    normals[idx * 3 + 2] = 0.0f;
    //printf("[average_normals_kernel] Invalid normal vector at index %d: (%f, %f, %f)\n", idx, normals[idx * 3 + 0], normals[idx * 3 + 1], normals[idx * 3 + 2]);
  }
}