
@SUBPIXEL_SHIFT@
#define uint8_t uchar
#define uint16_t unsigned short
#define INVALID_DISP 0

kernel void check_consistency_kernel(global uint16_t* d_resultDisp,
    global const uint16_t* d_leftDisp,
    global const uint16_t* d_rightDisp,
    global const uint8_t* d_left,
    int width,
    int height,
    int src_pitch,
    int dst_pitch,
    int subpixel, 
    int LR_max_diff,
    int min_disp) 
{

    const int j = get_global_id(0);
    const int i = get_global_id(1);
    if (i >= height || j >= width)
        return;

    // left-right consistency check, only on leftDisp, but could be done for rightDisp too
    int disp_scale = 1 << SUBPIXEL_SHIFT;
    int disp_scale_mod = disp_scale - 1;
    int disp_scale2 = disp_scale >> 1;
    int thres_lr_scale = LR_max_diff << SUBPIXEL_SHIFT;

    //uint8_t mask = d_left[i * src_pitch + j];
    uint16_t disp_l = d_leftDisp[i * dst_pitch + j];

    if(disp_l>0)
    {
      int d_tmp = disp_l;
      if(SUBPIXEL_SHIFT > 0)
      {
        int quotient = d_tmp >> SUBPIXEL_SHIFT;
        int remainder = d_tmp & disp_scale_mod;
        if(remainder >= disp_scale2)
        {
          quotient++;
        }
        d_tmp = quotient;
      }
      int u_r = j - (min_disp + d_tmp);
      if(u_r >=0 && u_r<width)
      {
        uint16_t disp_r = d_rightDisp[i * dst_pitch + u_r];
        if(0==disp_r || abs(d_tmp-disp_r) > LR_max_diff)
        {
          disp_l = 0;
        }
      }
      else
      {
        disp_l = 0;
      }
    }
    d_resultDisp[i * dst_pitch + j] = disp_l;
}