#include "sgm_cuda/device_data.h"

#include <cuda_runtime.h>

#include "sgm_cuda/host_utility.h"
namespace robosense {
namespace listereo
{

static size_t dataElemSize(DeviceDataType type, int custom_size = 0)
{
	if (type == DATA_8U)
		return 1;
	if (type == DATA_16U)
		return 2;
	if (type == DATA_32U )
		return sizeof(unsigned int);
	if (type == DATA_32S)
		return sizeof(int);
	if (type == DATA_32F)
		return sizeof(float);
	if (type == DATA_CUSTOM)
		return custom_size;
	return 0;
}

DeviceData::DeviceData() : data(nullptr), length(0), type(DATA_8U)
{
}

DeviceData::DeviceData(int length, DeviceDataType type)
{
	create(length, type);
}

DeviceData::DeviceData(void* data, int length, DeviceDataType type)
{
	create(data, length, type);
}

void DeviceData::create(int _length, DeviceDataType _type, int custom_size) 
{
	data = allocator_.allocate(dataElemSize(_type, custom_size) * _length);
	length = _length;
	type = _type;
}

void DeviceData::create(void* _data, int _length, DeviceDataType _type, int custom_size)
{
	allocator_.assign(_data, dataElemSize(_type, custom_size) * _length);
	data = _data;
	length = _length;
	type = _type;
}

void DeviceData::upload(const void* _data, int custom_size)
{
	CUDA_CHECK(cudaMemcpy(data, _data, dataElemSize(type, custom_size) * length, cudaMemcpyHostToDevice));
}

void DeviceData::download(void* _data, int custom_size, int copy_size) const
{
	if(copy_size == 0)
		CUDA_CHECK(cudaMemcpy(_data, data, dataElemSize(type, custom_size) * length, cudaMemcpyDeviceToHost));
	else {
		CUDA_CHECK(cudaMemcpy(_data, data, dataElemSize(type, custom_size) * copy_size, cudaMemcpyDeviceToHost));
	}
}

void DeviceData::fill_zero(int custom_size)
{
	CUDA_CHECK(cudaMemset(data, 0, dataElemSize(type, custom_size) * length));
}

} // namespace listereo
} // namespace robosense
