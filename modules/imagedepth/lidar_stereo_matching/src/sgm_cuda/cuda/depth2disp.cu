#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"

#define DEPTH_FIXED_SCALE 65536.0f 

namespace robosense {
namespace listereo
{
__global__ void depth2disp_kernel(
    float* d_disp,
    const float* d_depth,
    int width,
    int height,
    float baseline,
    float focal_length,
    float doffs,
    float sub_pixel_scale)
{
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height)
        return;

    int idx = y * width + x;
    float d = d_depth[idx]; // m
    if (d > 0)
    {
        // Convert depth to disparity
        float disp = (baseline * focal_length) / d - doffs;
        d_disp[idx] = disp;
    }
    else
    {
        d_disp[idx] = 0.0f;
    }
}

namespace cuda_kernels_api
{
    int depth2disp(
        const DeviceImage& depth, 
        DeviceImage& disp, 
        float baseline, 
        float focal_length,
        float doffs,
        float sub_pixel_scale)
    {
        cudaError_t err = cudaSuccess;

        int width = depth.cols;
        int height = depth.rows;

        dim3 block(32, 32);
        dim3 grid((width + block.x - 1) / block.x, (height + block.y - 1) / block.y);

        listereo::depth2disp_kernel<<<grid, block>>>(
            disp.ptr<float>(),
            depth.ptr<float>(),
            width,
            height,
            baseline,
            focal_length,
            doffs,
            sub_pixel_scale
        );

        err = cudaGetLastError();
        if (err != cudaSuccess) {
            printf("Failed to launch depth2disp_kernel (error code %s)!\n", cudaGetErrorString(err));
            return -1;
        }

        return 0;
    }
}

} // namespace listereo
} // namespace robosense