#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#include <math.h>
#include <stdint.h>
#include <device_atomic_functions.h>

#define DEPTH_FIXED_SCALE 65536.0f // 根据实际情况设置

namespace robosense {
namespace listereo
{
__device__ int crossProduct(int2 a, int2 b, int2 c) 
{
    return (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
}

__device__ inline float3 operator-(const float3 &a, const float3 &b) {
    return make_float3(a.x - b.x, a.y - b.y, a.z - b.z);
}

__device__ inline float3 operator*(const float3 &a, float b) {
    return make_float3(a.x * b, a.y * b, a.z * b);
}

__device__ inline float3 cross(const float3 &a, const float3 &b) {
    return make_float3(
        a.y * b.z - a.z * b.y,
        a.z * b.x - a.x * b.z,
        a.x * b.y - a.y * b.x
    );
}

__device__ inline float dot(const float3 &a, const float3 &b) {
    return a.x * b.x + a.y * b.y + a.z * b.z;
}

__device__ inline float3 normalize(const float3 &v) {
    float len = sqrtf(v.x * v.x + v.y * v.y + v.z * v.z);
    if (len > 1e-20f)
        return make_float3(v.x / len, v.y / len, v.z / len);
    else
        return make_float3(0.0f, 0.0f, 0.0f);
}

__global__ void triangle2depth_kernel(
    float* d_depth,
    int* normals,
    int* normals_count,
    const float* uv_p3d,
    const int* triangles,
    const int* idx_mat,
    int width,
    int height,
    int point_num,
    int triangle_num, 
    float thresh_cos,
    const float* K)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x; 
    if (idx >= triangle_num){
        return;
    }

    // 获取三角形的三个顶点
    int2 p2d_1 = make_int2(triangles[idx * 6 + 0], triangles[idx * 6 + 1]);
    int2 p2d_2 = make_int2(triangles[idx * 6 + 2], triangles[idx * 6 + 3]);
    int2 p2d_3 = make_int2(triangles[idx * 6 + 4], triangles[idx * 6 + 5]);
    // 检查顶点是否在图像范围内
    if( p2d_1.x < 0 || p2d_1.x >= width || p2d_1.y < 0 || p2d_1.y >= height ||
        p2d_2.x < 0 || p2d_2.x >= width || p2d_2.y < 0 || p2d_2.y >= height ||
        p2d_3.x < 0 || p2d_3.x >= width || p2d_3.y < 0 || p2d_3.y >= height)
    {
        return;
    }
    // 计算三角形的三个顶点在uv_p3d中的索引  
    int map_idx1 = p2d_1.x + p2d_1.y * width;
    int map_idx2 = p2d_2.x + p2d_2.y * width;
    int map_idx3 = p2d_3.x + p2d_3.y * width;

    // 获取idx_mat中对应的索引
    int idx1 = idx_mat[map_idx1];
    int idx2 = idx_mat[map_idx2];
    int idx3 = idx_mat[map_idx3];
    
    if (idx1 < 0 || idx1 >= point_num || idx2 < 0 || idx2 >= point_num || idx3 < 0 || idx3 >= point_num)
    {
        return;
    }
    // uv_p3d
    if (idx1 * 5 + 4 >= point_num * 5 ||
        idx2 * 5 + 4 >= point_num * 5 ||
        idx3 * 5 + 4 >= point_num * 5)
        return;
    // 获取对应的三维点
    float3 p3d_1 = make_float3(uv_p3d[idx1 * 5 + 2], uv_p3d[idx1 * 5 + 3], uv_p3d[idx1 * 5 + 4]);
    float3 p3d_2 = make_float3(uv_p3d[idx2 * 5 + 2], uv_p3d[idx2 * 5 + 3], uv_p3d[idx2 * 5 + 4]);
    float3 p3d_3 = make_float3(uv_p3d[idx3 * 5 + 2], uv_p3d[idx3 * 5 + 3], uv_p3d[idx3 * 5 + 4]);

    // 三角形的法向量
    float3 n_3d = cross(p3d_2 - p3d_1, p3d_3 - p3d_1);
    if(n_3d.x == 0 && n_3d.y == 0 && n_3d.z == 0)
    {
        return;
    }   
    n_3d = normalize(n_3d);
    if(n_3d.z > 0)
    {
        n_3d = n_3d * -1.0f;
    }
    float3 p3d_1_normalized = normalize(p3d_1);
    float3 p3d_2_normalized = normalize(p3d_2);
    float3 p3d_3_normalized = normalize(p3d_3);
    if (fabsf(dot(n_3d, p3d_1_normalized)) < thresh_cos ||
        fabsf(dot(n_3d, p3d_2_normalized)) < thresh_cos ||
        fabsf(dot(n_3d, p3d_3_normalized)) < thresh_cos)
    { 
        return;
    }

    // 二维三角形的边长，选取最长的边作为权重
    int dx12 = p2d_1.x - p2d_2.x, dy12 = p2d_1.y - p2d_2.y;
    int dx13 = p2d_1.x - p2d_3.x, dy13 = p2d_1.y - p2d_3.y;
    int dx23 = p2d_2.x - p2d_3.x, dy23 = p2d_2.y - p2d_3.y;
    int l12 = dx12*dx12 + dy12*dy12;
    int l13 = dx13*dx13 + dy13*dy13;
    int l23 = dx23*dx23 + dy23*dy23;
    int l = max(l12, max(l13, l23));
    int w = l + 1;
    float3 n_w = n_3d * (1.0f / w);
    int3 n_w_fixed = make_int3(n_w.x * DEPTH_FIXED_SCALE, n_w.y * DEPTH_FIXED_SCALE, n_w.z * DEPTH_FIXED_SCALE);

    // 统计法向量的加权和
    atomicAdd(&normals[idx1 * 3 + 0], n_w_fixed.x);
    atomicAdd(&normals[idx1 * 3 + 1], n_w_fixed.y);
    atomicAdd(&normals[idx1 * 3 + 2], n_w_fixed.z);

    atomicAdd(&normals[idx2 * 3 + 0], n_w_fixed.x);
    atomicAdd(&normals[idx2 * 3 + 1], n_w_fixed.y);
    atomicAdd(&normals[idx2 * 3 + 2], n_w_fixed.z);

    atomicAdd(&normals[idx3 * 3 + 0], n_w_fixed.x);
    atomicAdd(&normals[idx3 * 3 + 1], n_w_fixed.y);
    atomicAdd(&normals[idx3 * 3 + 2], n_w_fixed.z);

    atomicAdd(&normals_count[idx1], 1);
    atomicAdd(&normals_count[idx2], 1);
    atomicAdd(&normals_count[idx3], 1);

    float4 abcd = make_float4(n_3d.x * K[0], n_3d.y * K[1], n_3d.z + n_3d.x * K[2] + n_3d.y * K[3], dot(n_3d, p3d_1));
    if(abcd.w < 0)
    {
        abcd.x *= -1.0f;
        abcd.y *= -1.0f;
        abcd.z *= -1.0f;
        abcd.w *= -1.0f;
    }

    int minX = min(p2d_1.x, min(p2d_2.x, p2d_3.x));
    int maxX = max(p2d_1.x, max(p2d_2.x, p2d_3.x));
    int minY = min(p2d_1.y, min(p2d_2.y, p2d_3.y));
    int maxY = max(p2d_1.y, max(p2d_2.y, p2d_3.y));

    for(int v = minY; v <= maxY; ++v) 
    {
        for(int u = minX; u <= maxX; ++u) 
        {
            int2 p = make_int2(u, v);
            int d1 = crossProduct(p2d_1, p2d_2, p);
            int d2 = crossProduct(p2d_2, p2d_3, p);
            int d3 = crossProduct(p2d_3, p2d_1, p);

            bool inside = (d1 <= 0 && d2 <= 0 && d3 <= 0) ||  (d1 >= 0 && d2 >= 0 && d3 >= 0);
            
            if(inside) 
            {
                int linear_idx = v * width + u;
                float den = abcd.x * u + abcd.y * v + abcd.z;
                if(den > 0)
                {
                    float calculated_depth = ((abcd.w / den));
                    d_depth[linear_idx] = d_depth[linear_idx] == 0 ? calculated_depth : min(d_depth[linear_idx], calculated_depth); //m
                } 
            }
        }
    }
}

namespace cuda_kernels_api
{
    int triangle2depth(
        const DeviceData& uv_p3d, 
        const DeviceData& triangles, 
        const DeviceImage& idx_mat, 
        DeviceImage& depth, 
        DeviceData& normals,
        DeviceData& normals_count,
        const int point_num,
        const int triangle_num,
        const float thres_cos,
        const DeviceData& K)
    {

        cudaError_t err = cudaSuccess;

        // 1. 设置 block 和 grid 大小
        int threadsPerBlock = 256;
        const int gdim = (divUp(triangle_num, threadsPerBlock));
        const int bdim = threadsPerBlock;
        // 2. 调用 kernel
        listereo::triangle2depth_kernel<<<gdim, bdim>>>(
            depth.ptr<float>(),
            normals.ptr<int>(),
            normals_count.ptr<int>(),
            uv_p3d.ptr<float>(),
            triangles.ptr<int>(),
            idx_mat.ptr<int>(),
            idx_mat.cols,
            idx_mat.rows,
            point_num,
            triangle_num,
            thres_cos,
            K.ptr<float>()
        );

        // 3. 检查 kernel 是否出错
        err = cudaGetLastError();
        if (err != cudaSuccess) {
            printf("Failed to launch triangle2depth_kernel (error code %s)!\n", cudaGetErrorString(err));
            return -1;
        }

        return err;
    }
}

} // namespace listereo
} // namespace robosense