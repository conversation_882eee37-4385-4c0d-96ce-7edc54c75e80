#include <cuda_runtime.h>
#include <device_functions.h>
#include <climits>
#include "sgm_cuda/cuda_kernels_api.h"
#include "sgm_cuda/host_utility.h"

#define uint8_t unsigned char
#define uint16_t unsigned short

namespace robosense {
namespace listereo
{
    // clamp condition
__device__ inline int clampBC(const int x, const int y, const int width, const int height)
{
    const int idx = min(max(x, 0), width - 1);
    const int idy = min(max(y, 0), height - 1);
    return idx + idy * width;
}

__global__ void median3x3(
    uint16_t* __restrict__ output,
    const uint16_t* __restrict__ input,
    const int width,
    const int height)
{
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int idy = blockIdx.y * blockDim.y + threadIdx.y;

    if (idx >= width || idy >= height) {
        return;
    }

    const int id = idx + idy * width;

    // Use a small array on the stack for the 3x3 window
    uint16_t window[9];

    // Load the 3x3 window from global memory
    window[0] = input[clampBC(idx - 1, idy - 1, width, height)];
    window[1] = input[clampBC(idx, idy - 1, width, height)];
    window[2] = input[clampBC(idx + 1, idy - 1, width, height)];

    window[3] = input[clampBC(idx - 1, idy, width, height)];
    window[4] = input[clampBC(idx, idy, width, height)];
    window[5] = input[clampBC(idx + 1, idy, width, height)];

    window[6] = input[clampBC(idx - 1, idy + 1, width, height)];
    window[7] = input[clampBC(idx, idy + 1, width, height)];
    window[8] = input[clampBC(idx + 1, idy + 1, width, height)];

    // Perform partial bitonic sort to find the median
    // The min/max logic is preserved as it's a standard device-side operation
    uint16_t flMin = min(window[0], window[1]);
    uint16_t flMax = max(window[0], window[1]);
    window[0] = flMin;
    window[1] = flMax;

    flMin = min(window[3], window[2]);
    flMax = max(window[3], window[2]);
    window[3] = flMin;
    window[2] = flMax;

    flMin = min(window[2], window[0]);
    flMax = max(window[2], window[0]);
    window[2] = flMin;
    window[0] = flMax;

    flMin = min(window[3], window[1]);
    flMax = max(window[3], window[1]);
    window[3] = flMin;
    window[1] = flMax;

    flMin = min(window[1], window[0]);
    flMax = max(window[1], window[0]);
    window[1] = flMin;
    window[0] = flMax;

    flMin = min(window[3], window[2]);
    flMax = max(window[3], window[2]);
    window[3] = flMin;
    window[2] = flMax;

    flMin = min(window[5], window[4]);
    flMax = max(window[5], window[4]);
    window[5] = flMin;
    window[4] = flMax;

    flMin = min(window[7], window[8]);
    flMax = max(window[7], window[8]);
    window[7] = flMin;
    window[8] = flMax;

    flMin = min(window[6], window[8]);
    flMax = max(window[6], window[8]);
    window[6] = flMin;
    window[8] = flMax;

    flMin = min(window[6], window[7]);
    flMax = max(window[6], window[7]);
    window[6] = flMin;
    window[7] = flMax;

    flMin = min(window[4], window[8]);
    flMax = max(window[4], window[8]);
    window[4] = flMin;
    window[8] = flMax;

    flMin = min(window[4], window[6]);
    flMax = max(window[4], window[6]);
    window[4] = flMin;
    window[6] = flMax;

    flMin = min(window[5], window[7]);
    flMax = max(window[5], window[7]);
    window[5] = flMin;
    window[7] = flMax;

    flMin = min(window[4], window[5]);
    flMax = max(window[4], window[5]);
    window[4] = flMin;
    window[5] = flMax;

    flMin = min(window[6], window[7]);
    flMax = max(window[6], window[7]);
    window[6] = flMin;
    window[7] = flMax;

    flMin = min(window[0], window[8]);
    flMax = max(window[0], window[8]);
    window[0] = flMin;
    window[8] = flMax;

    window[4] = max(window[0], window[4]);
    window[5] = max(window[1], window[5]);

    window[6] = max(window[2], window[6]);
    window[7] = max(window[3], window[7]);

    window[4] = min(window[4], window[6]);
    window[5] = min(window[5], window[7]);

    output[id] = min(window[4], window[5]);
}
namespace cuda_kernels_api
{

    int median_filter(const DeviceImage& src, DeviceImage& dst)
    {
        const int w = src.cols;
        const int h = src.rows;

        const dim3 block(16, 16);
	    const dim3 grid(divUp(w, block.x), divUp(h, block.y));

        median3x3<<<grid, block>>>(dst.ptr<uint16_t>(), src.ptr<uint16_t>(), w, h);
        cudaError_t err = cudaGetLastError();
        CUDA_CHECK(err);
        return err;
    }
} // namespace cuda_kernels_api
} //listereo
} // robosense