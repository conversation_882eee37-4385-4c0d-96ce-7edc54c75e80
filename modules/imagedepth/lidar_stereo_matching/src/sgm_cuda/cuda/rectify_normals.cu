#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include <math.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
namespace robosense {
namespace listereo
{

__device__ inline float3 make_float3_from_int3(int x, int y, int z) {
    return make_float3((float)x, (float)y, (float)z);
}

__global__ void rectify_normals_kernel(
    float* normals,
    const int* normals_fixed,
    const int* normals_count,
    int point_num)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= point_num)
        return;

    if (normals_count[idx] >= 2)
    {
        float3 n = make_float3_from_int3(
            normals_fixed[idx * 3 + 0],
            normals_fixed[idx * 3 + 1],
            normals_fixed[idx * 3 + 2]
        );
        float norm = sqrtf(n.x * n.x + n.y * n.y + n.z * n.z);
        if (norm > 0)
        {
            normals[idx * 3 + 0] = n.x / norm;
            normals[idx * 3 + 1] = n.y / norm;
            normals[idx * 3 + 2] = n.z / norm;
        }
    }
    else
    {
        normals[idx * 3 + 0] = 0.0f;
        normals[idx * 3 + 1] = 0.0f;
        normals[idx * 3 + 2] = 0.0f;
    }
}

namespace cuda_kernels_api
{
    int rectify_normals(
        const DeviceData& src_normals,
        const DeviceData& src_normals_count,
        DeviceData& dst_normals_fixed,
        const int point_num)
    {
        cudaError_t err = cudaSuccess;

        int threadsPerBlock = 256;
        int blocksPerGrid = (point_num + threadsPerBlock - 1) / threadsPerBlock;

        listereo::rectify_normals_kernel<<<blocksPerGrid, threadsPerBlock>>>(
            dst_normals_fixed.ptr<float>(),
            src_normals.ptr<int>(),
            src_normals_count.ptr<int>(),
            point_num
        );

        err = cudaGetLastError();
        if (err != cudaSuccess) {
            printf("Failed to launch rectify_normals_kernel (error code %s)!\n", cudaGetErrorString(err));
            return -1;
        }

        return 0;
    }
}

} // namespace listereo
} // namespace robosense