
#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#define uint8_t unsigned char

namespace robosense {
namespace listereo
{

__global__ void image_undistort_kernel(
    uint8_t* dest_left, 
    uint8_t* dest_right,     
    const uint8_t* src_left, 
    const uint8_t* src_right, 
    int width, 
    int height,
    const float* K_left,
    const float* K_right,
    const float* dist_left,
    const float* dist_right)
{
	// 获取当前线程的像素坐标 (去畸变图像中的坐标)
    const int u = blockIdx.x * blockDim.x + threadIdx.x;
    const int v = blockIdx.y * blockDim.y + threadIdx.y;

    // 检查边界
    if (u >= width || v >= height) {
        return;
    }   

    const int dest_linear_idx = v * width + u;

    // --- 左图去畸变 ---
    
    // 从内参矩阵 K 获取焦距和主点坐标
    const float fx_left = K_left[0];
    const float fy_left = K_left[1];
    const float cx_left = K_left[2];
    const float cy_left = K_left[3];
    
    // 从畸变系数 dist 获取参数
    const float k1_left = dist_left[0];
    const float k2_left = dist_left[1];
    const float p1_left = dist_left[2];
    const float p2_left = dist_left[3];
    const float k3_left = dist_left[4];
    const float k4_left = dist_left[5];
    const float k5_left = dist_left[6];
    const float k6_left = dist_left[7];
    
    // 像素坐标转归一化坐标
    float x_prime_left = (u - cx_left) / fx_left;
    float y_prime_left = (v - cy_left) / fy_left;

    // 计算径向和切向畸变
    float r2_left = x_prime_left * x_prime_left + y_prime_left * y_prime_left;
    float r4_left = r2_left * r2_left;
    float r6_left = r4_left * r2_left;

    // 径向畸变公式
    float radial_dist_left_numerator = 1.0f + k1_left * r2_left + k2_left * r4_left + k3_left * r6_left;
    float radial_dist_left_denominator = 1.0f + k4_left * r2_left + k5_left * r4_left + k6_left * r6_left;
    float radial_dist_left = radial_dist_left_numerator / radial_dist_left_denominator;
    
    float tan_dist_x_left = 2.0f * p1_left * x_prime_left * y_prime_left + p2_left * (r2_left + 2.0f * x_prime_left * x_prime_left);
    float tan_dist_y_left = p1_left * (r2_left + 2.0f * y_prime_left * y_prime_left) + 2.0f * p2_left * x_prime_left * y_prime_left;

    // 计算原始图像中的归一化坐标
    float x_orig_left = x_prime_left * radial_dist_left + tan_dist_x_left;
    float y_orig_left = y_prime_left * radial_dist_left + tan_dist_y_left;

    // 归一化坐标转回像素坐标
    float u_orig_left = x_orig_left * fx_left + cx_left;
    float v_orig_left = y_orig_left * fy_left + cy_left;
    
    // 双线性插值
    if (u_orig_left >= 0 && u_orig_left < width - 1 && v_orig_left >= 0 && v_orig_left < height - 1) {
        int u1 = (int)u_orig_left;
        int v1 = (int)v_orig_left;
        float du = u_orig_left - u1;
        float dv = v_orig_left - v1;
        
        const uint8_t p11 = src_left[v1 * width + u1];
        const uint8_t p12 = src_left[v1 * width + u1 + 1];
        const uint8_t p21 = src_left[(v1 + 1) * width + u1];
        const uint8_t p22 = src_left[(v1 + 1) * width + u1 + 1];

        dest_left[dest_linear_idx] = static_cast<uint8_t>(
            p11 * (1.0f - du) * (1.0f - dv) +
            p12 * du * (1.0f - dv) +
            p21 * (1.0f - du) * dv +
            p22 * du * dv
        );
    } else {
        dest_left[dest_linear_idx] = 0; // 超出边界，设为黑色
    }

    // --- 右图去畸变 ---
    
    // 从内参矩阵 K 获取焦距和主点坐标
    const float fx_right = K_right[0];
    const float fy_right = K_right[1];
    const float cx_right = K_right[2];
    const float cy_right = K_right[3];
    
    // 从畸变系数 dist 获取参数
    const float k1_right = dist_right[0];
    const float k2_right = dist_right[1];
    const float p1_right = dist_right[2];
    const float p2_right = dist_right[3];
    const float k3_right = dist_right[4];
    const float k4_right = dist_right[5];
    const float k5_right = dist_right[6];
    const float k6_right = dist_right[7];
    
    // 像素坐标转归一化坐标
    float x_prime_right = (u - cx_right) / fx_right;
    float y_prime_right = (v - cy_right) / fy_right;

    // 计算径向和切向畸变
    float r2_right = x_prime_right * x_prime_right + y_prime_right * y_prime_right;
    float r4_right = r2_right * r2_right;
    float r6_right = r4_right * r2_right;

    // 径向畸变公式
    float radial_dist_right_numerator = 1.0f + k1_right * r2_right + k2_right * r4_right + k3_right * r6_right;
    float radial_dist_right_denominator = 1.0f + k4_right * r2_right + k5_right * r4_right + k6_right * r6_right;
    float radial_dist_right = radial_dist_right_numerator / radial_dist_right_denominator;
    
    float tan_dist_x_right = 2.0f * p1_right * x_prime_right * y_prime_right + p2_right * (r2_right + 2.0f * x_prime_right * x_prime_right);
    float tan_dist_y_right = p1_right * (r2_right + 2.0f * y_prime_right * y_prime_right) + 2.0f * p2_right * x_prime_right * y_prime_right;

    // 计算原始图像中的归一化坐标
    float x_orig_right = x_prime_right * radial_dist_right + tan_dist_x_right;
    float y_orig_right = y_prime_right * radial_dist_right + tan_dist_y_right;

    // 归一化坐标转回像素坐标
    float u_orig_right = x_orig_right * fx_right + cx_right;
    float v_orig_right = y_orig_right * fy_right + cy_right;
    
    // 双线性插值
    if (u_orig_right >= 0 && u_orig_right < width - 1 && v_orig_right >= 0 && v_orig_right < height - 1) {
        int u1 = (int)u_orig_right;
        int v1 = (int)v_orig_right;
        float du = u_orig_right - u1;
        float dv = v_orig_right - v1;
        
        const uint8_t p11 = src_right[v1 * width + u1];
        const uint8_t p12 = src_right[v1 * width + u1 + 1];
        const uint8_t p21 = src_right[(v1 + 1) * width + u1];
        const uint8_t p22 = src_right[(v1 + 1) * width + u1 + 1];

        dest_right[dest_linear_idx] = static_cast<uint8_t>(
            p11 * (1.0f - du) * (1.0f - dv) +
            p12 * du * (1.0f - dv) +
            p21 * (1.0f - du) * dv +
            p22 * du * dv
        );
    } else {
        dest_right[dest_linear_idx] = 0; // 超出边界，设为黑色
    }
}

namespace cuda_kernels_api
{
    int image_undistort(
        const DeviceImage& src_left, 
        const DeviceImage& src_right,
        DeviceImage& dst_left, 
        DeviceImage& dst_right,
        const DeviceData& K_left, 
        const DeviceData& K_right,
        const DeviceData& dist_left,
        const DeviceData& dist_right)
    {
        const int w = src_left.cols;
        const int h = src_left.rows;

        const int w_per_block = 16;
        const int h_per_block = 16;
        const dim3 bdim(w_per_block, h_per_block);
        const dim3 gdim(divUp(w, w_per_block), divUp(h, h_per_block));

        image_undistort_kernel<<<gdim, bdim>>>(
            dst_left.ptr<uint8_t>(), 
            dst_right.ptr<uint8_t>(), 
            src_left.ptr<uint8_t>(), 
            src_right.ptr<uint8_t>(), 
            w, 
            h, 
            K_left.ptr<float>(), 
            K_right.ptr<float>(), 
            dist_left.ptr<float>(), 
            dist_right.ptr<float>());

        CUDA_CHECK(cudaGetLastError());
        return cudaGetLastError();
    }
} // namespace cuda_kernels_api

} // namespace listereo
} // namespace robosense
