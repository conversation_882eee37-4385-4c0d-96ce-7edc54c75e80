
#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#define uint8_t unsigned char

namespace robosense {
namespace listereo
{

__global__ void color2gray_kernel(
    uint8_t* dest_left, 
    uint8_t* dest_right,     
    const uint8_t* src_left, 
    const uint8_t* src_right, 
    int width, 
    int height)
{
	// 获取当前线程的像素坐标
    const int u = blockIdx.x * blockDim.x + threadIdx.x;
    const int v = blockIdx.y * blockDim.y + threadIdx.y;

    // 检查边界
    if (u >= width || v >= height) {
        return;
    }

    // 计算线性索引
    const int linear_idx = v * width + u;
    const int src_idx = linear_idx * 3; // 彩色图有3个通道

    // 转换左图
    const uint8_t r_left = src_left[src_idx];
    const uint8_t g_left = src_left[src_idx + 1];
    const uint8_t b_left = src_left[src_idx + 2];

    // 常见的加权平均公式
    dest_left[linear_idx] = static_cast<uint8_t>(0.299f * r_left + 0.587f * g_left + 0.114f * b_left);

    // 转换右图
    const uint8_t r_right = src_right[src_idx];
    const uint8_t g_right = src_right[src_idx + 1];
    const uint8_t b_right = src_right[src_idx + 2];

    // 常见的加权平均公式
    dest_right[linear_idx] = static_cast<uint8_t>(0.299f * r_right + 0.587f * g_right + 0.114f * b_right);

}

namespace cuda_kernels_api
{
    int color2gray(const DeviceImage& src_left, const DeviceImage& src_right, DeviceImage& dst_left, DeviceImage& dst_right)
    {
        const int w = src_left.cols;
        const int h = src_left.rows;

        const int w_per_block = 16;
        const int h_per_block = 16;
        const dim3 bdim(w_per_block, h_per_block);
        const dim3 gdim(divUp(w, w_per_block), divUp(h, h_per_block));

        color2gray_kernel<<<gdim, bdim>>>(dst_left.ptr<uint8_t>(), dst_right.ptr<uint8_t>(), src_left.ptr<uint8_t>(), src_right.ptr<uint8_t>(), w, h);

        CUDA_CHECK(cudaGetLastError());
        return cudaGetLastError();
    }
} // namespace cuda_kernels_api

} // namespace listereo
} // namespace robosense
