#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include <math.h>
#include <stdint.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#define DEPTH_FIXED_SCALE 65536.0f
namespace robosense {
namespace listereo
{
__device__ inline float3 make_float3_from_float(const float* arr) {
    return make_float3(arr[0], arr[1], arr[2]);
}

__device__ inline float dot3(const float3 &a, const float3 &b) {
    return a.x * b.x + a.y * b.y + a.z * b.z;
}

__global__ void filter_bad_prior_depth_kernel(
    float* d_depth,
    const float* uv_p3d,
    const float* normals,
    const int* idx_mat,
    int width,
    int height,
    int point_num,
    int r,
    const float* K)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= point_num)
        return;

    int u = (int)uv_p3d[idx * 5 + 0];
    int v = (int)uv_p3d[idx * 5 + 1];
    if (u < 0 || u >= width || v < 0 || v >= height)
        return;

    float3 p0 = make_float3(
        uv_p3d[idx * 5 + 2],
        uv_p3d[idx * 5 + 3],
        uv_p3d[idx * 5 + 4]
    );
    p0 = make_float3(p0.x, p0.y, p0.z);

    int d_idx = v * width + u;
    d_depth[d_idx] = p0.z;

    float3 n_3d = make_float3(
        normals[idx * 3 + 0],
        normals[idx * 3 + 1],
        normals[idx * 3 + 2]
    );
    if (2 * dot3(n_3d, n_3d) < 1.f)
        return;

    float abcd_x = n_3d.x * K[0];
    float abcd_y = n_3d.y * K[1];
    float abcd_z = n_3d.z + n_3d.x * K[2] + n_3d.y * K[3];
    float abcd_w = -dot3(n_3d, p0);

    for (int dv = -r; dv <= r; dv++) {
        for (int du = -r; du <= r; du++) {
            int v1 = v + dv;
            int u1 = u + du;
            if (v1 < 0 || v1 >= height || u1 < 0 || u1 >= width) continue;
            int idx1 = v1 * width + u1;
            if (idx_mat[idx1] >= 0) continue;
            float z = d_depth[idx1];
            if (z > 0) {
                float delta = fabsf(z * (abcd_x * u1 + abcd_y * v1 + abcd_z) + abcd_w);
                if (delta > 0.1f) {
                    d_depth[idx1] = 0;
                }
            }
        }
    }
}

namespace cuda_kernels_api
{
    int filter_bad_prior_depth(
        DeviceImage& depth,
        const DeviceData& uv_p3d,
        const DeviceData& normals_fixed,        
        const DeviceImage& idx_mat,
        const int point_num,
        const int r,
        const DeviceData& K)
    {
        cudaError_t err = cudaSuccess;

        int threadsPerBlock = 256;
        int blocksPerGrid = (point_num + threadsPerBlock - 1) / threadsPerBlock;

        listereo::filter_bad_prior_depth_kernel<<<blocksPerGrid, threadsPerBlock>>>(
            depth.ptr<float>(),
            uv_p3d.ptr<float>(),
            normals_fixed.ptr<float>(),
            idx_mat.ptr<int>(),
            idx_mat.cols,
            idx_mat.rows,
            point_num,
            r,
            K.ptr<float>()
        );

        err = cudaGetLastError();
        if (err != cudaSuccess) {
            printf("Failed to launch filter_bad_prior_depth_kernel (error code %s)!\n", cudaGetErrorString(err));
            return -1;
        }

        return 0;
    }
}

} // namespace listereo
} // namespace robosense