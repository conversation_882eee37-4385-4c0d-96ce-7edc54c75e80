
#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#define uint8_t unsigned char

namespace robosense {
namespace listereo
{

static const unsigned int BLOCK_DIM_X = 16;
static const unsigned int BLOCK_DIM_Y = 16;

// 卷积核需要的邻域大小
static const unsigned int WINDOW_SIZE = 1;

// 局部内存中Block的实际尺寸，包含了邻域
static const unsigned int LOCAL_BLOCK_WIDTH = (BLOCK_DIM_X + 2 * WINDOW_SIZE);
static const unsigned int LOCAL_BLOCK_HEIGHT = (BLOCK_DIM_Y + 2 * WINDOW_SIZE);

__global__ void sobel_gx_kernel(
    uint8_t* dest_left, 
    uint8_t* dest_right,     
    const uint8_t* src_left, 
    const uint8_t* src_right, 
    int width, 
    int height)
{
	// 声明共享内存（__shared__），用于存储当前线程块的图像块
    __shared__ float local_block_left[LOCAL_BLOCK_HEIGHT][LOCAL_BLOCK_WIDTH];
    __shared__ float local_block_right[LOCAL_BLOCK_HEIGHT][LOCAL_BLOCK_WIDTH];

    int input_stride = width;
    int output_stride = width;

    // 获取当前线程的全局ID
    int global_x = blockIdx.x * blockDim.x + threadIdx.x;
    int global_y = blockIdx.y * blockDim.y + threadIdx.y;

    // 获取当前线程在线程块内的局部ID
    int local_x = threadIdx.x;
    int local_y = threadIdx.y;

    // 计算当前线程块在全局内存中处理的block的起始像素坐标
    int block_start_x = blockIdx.x * blockDim.x;
    int block_start_y = blockIdx.y * blockDim.y;

    // --- 数据加载阶段 ---
    // 每个线程负责从全局内存加载一部分像素到共享内存
    // 需要加载的区域是 (BLOCK_DIM_X + 2*WINDOW_SIZE) x (BLOCK_DIM_Y + 2*WINDOW_SIZE)
    // 通过循环确保线程块内的所有线程合作完成Block的填充
    for (int y_offset = 0; y_offset < LOCAL_BLOCK_HEIGHT; y_offset += blockDim.y) {
        for (int x_offset = 0; x_offset < LOCAL_BLOCK_WIDTH; x_offset += blockDim.x) {
            // 计算当前线程在局部内存中负责加载的像素坐标
            int load_local_x = local_x + x_offset;
            int load_local_y = local_y + y_offset;

            // 计算当前线程在全局内存中负责加载的像素坐标
            int load_global_x = block_start_x + load_local_x - WINDOW_SIZE;
            int load_global_y = block_start_y + load_local_y - WINDOW_SIZE;

            if (load_local_x < LOCAL_BLOCK_WIDTH && load_local_y < LOCAL_BLOCK_HEIGHT) {
                // 处理全局内存的边界情况 (模拟 CLAMP_TO_EDGE)
                int clamped_global_x = max(0, min(load_global_x, width - 1));
                int clamped_global_y = max(0, min(load_global_y, height - 1));

                float pixel_val_left = (float)src_left[clamped_global_y * input_stride + clamped_global_x];
                local_block_left[load_local_y][load_local_x] = pixel_val_left;

                float pixel_val_right = (float)src_right[clamped_global_y * input_stride + clamped_global_x];
                local_block_right[load_local_y][load_local_x] = pixel_val_right;
            }
        }
    }

    // --- 同步 ---
    // 确保所有线程都已将数据加载到共享内存中
    __syncthreads();

    // --- 计算阶段 ---
    // 只有当当前全局ID对应有效的输出像素时才进行计算
    if (global_x >= WINDOW_SIZE && global_x < width - WINDOW_SIZE &&
        global_y >= WINDOW_SIZE && global_y < height - WINDOW_SIZE)
    {
        // 从共享内存中读取 3x3 邻域像素值
        float p00_left = local_block_left[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE - 1];
        //float p01_left = local_block_left[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE];
        float p02_left = local_block_left[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE + 1];

        float p10_left = local_block_left[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE - 1];
        float p12_left = local_block_left[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE + 1];

        float p20_left = local_block_left[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE - 1];
        //float p21_left = local_block_left[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE];
        float p22_left = local_block_left[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE + 1];

        float left_Gx = (p02_left + 2 * p12_left + p22_left) - (p00_left + 2 * p10_left + p20_left);
        // CUDA中可以使用 `clamp` 和 `roundf` 函数
        uint8_t final_left_gx = (uint8_t)min(max(roundf(left_Gx), (float)SHRT_MIN), (float)SHRT_MAX);

        float p00_right = local_block_right[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE - 1];
        //float p01_right = local_block_right[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE];
        float p02_right = local_block_right[local_y + WINDOW_SIZE - 1][local_x + WINDOW_SIZE + 1];

        float p10_right = local_block_right[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE - 1];
        float p12_right = local_block_right[local_y + WINDOW_SIZE][local_x + WINDOW_SIZE + 1];

        float p20_right = local_block_right[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE - 1];
        //float p21_right = local_block_right[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE];
        float p22_right = local_block_right[local_y + WINDOW_SIZE + 1][local_x + WINDOW_SIZE + 1];

        float right_Gx = (p02_right + 2 * p12_right + p22_right) - (p00_right + 2 * p10_right + p20_right);
        uint8_t final_right_gx =  (uint8_t)min(max(roundf(right_Gx), (float)SHRT_MIN), (float)SHRT_MAX);

        // 计算输出数据在 `output_data` 数组中的索引
        int output_idx = global_y * output_stride + global_x;

        // 写入结果到输出缓冲区
        dest_left[output_idx] = final_left_gx;
        dest_right[output_idx] = final_right_gx;
    }
}

namespace cuda_kernels_api
{
    int sobel_gx_feature(const DeviceImage& src_left, const DeviceImage& src_right, DeviceImage& dst_left, DeviceImage& dst_right)
    {
        const int w = src_left.cols;
        const int h = src_left.rows;

        const int w_per_block = BLOCK_DIM_X;
        const int h_per_block = BLOCK_DIM_Y;
        const dim3 bdim(BLOCK_DIM_X, BLOCK_DIM_Y);
        const dim3 gdim(divUp(w, w_per_block), divUp(h, h_per_block));

        sobel_gx_kernel<<<gdim, bdim>>>(dst_left.ptr<uint8_t>(), dst_right.ptr<uint8_t>(), src_left.ptr<uint8_t>(), src_right.ptr<uint8_t>(), w, h);

        CUDA_CHECK(cudaGetLastError());
        return cudaGetLastError();
    }
} // namespace cuda_kernels_api

} // namespace listereo
} // namespace robosense
