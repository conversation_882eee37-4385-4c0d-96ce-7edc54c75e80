#include <cuda_runtime.h>
#include <device_functions.h>
#include <climits>
#include "sgm_cuda/cuda_kernels_api.h"
#include "sgm_cuda/host_utility.h"

#define uint8_t unsigned char
#define uint16_t unsigned short
namespace robosense {
namespace listereo
{

__global__ void check_consistency_kernel(
    uint16_t* d_leftDisp,
    const uint16_t* d_rightDisp,
    const uint8_t* d_left,
    int width,
    int height,
    int min_disp,
    int LR_max_diff,
    int sub_pixel_shift) 
{
    const int j = blockIdx.x * blockDim.x + threadIdx.x;
    const int i = blockIdx.y * blockDim.y + threadIdx.y;

    if (i >= height || j >= width) {
        return;
    }

    int disp_scale = 1 << sub_pixel_shift;
    int disp_scale_mod = disp_scale - 1;
    int disp_scale2 = disp_scale >> 1;
    int thres_lr_scale = LR_max_diff << sub_pixel_shift;

    uint16_t disp_l = d_leftDisp[i * width + j];

    if (disp_l > 0) {
        int d_tmp = disp_l;
        if (sub_pixel_shift > 0) {
            int quotient = d_tmp >> sub_pixel_shift;
            int remainder = d_tmp & disp_scale_mod;
            if (remainder >= disp_scale2) {
                quotient++;
            }
            d_tmp = quotient;
        }
        
        int u_r = j - (min_disp + d_tmp);
        
        if (u_r >= 0 && u_r < width) {
            uint16_t disp_r = d_rightDisp[i * width + u_r];
            
            if (disp_r == 0 || abs(d_tmp - disp_r) > LR_max_diff) {
                disp_l = 0;
            }
        } else {
            disp_l = 0;
        }
    }
    d_leftDisp[i * width + j] = disp_l;
}


namespace cuda_kernels_api
{
    int check_consistency(
        DeviceImage& dispL, 
        const DeviceImage& dispR, 
        const DeviceImage& srcL, 
        int min_disp, 
        int LR_max_diff, 
        int sub_pixel_shift)
    {
        const int w = srcL.cols;
        const int h = srcL.rows;

        const dim3 block(16, 16);
	    const dim3 grid(divUp(w, block.x), divUp(h, block.y));

        check_consistency_kernel<<<grid, block>>>(dispL.ptr<uint16_t>(), dispR.ptr<uint16_t>(), srcL.ptr<uint8_t>(), w, h, min_disp, LR_max_diff, sub_pixel_shift);
        cudaError_t err = cudaGetLastError();
        CUDA_CHECK(err);
        return err;
    }
} // namespace cuda_kernels_api

} //listereo
} // robosense