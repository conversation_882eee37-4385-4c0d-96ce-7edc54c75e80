
#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#define uint8_t unsigned char

namespace robosense {
namespace listereo
{

// 辅助函数：三通道双线性插值
__device__ void bilinear_interpolate_rgb(
    const uint8_t* src,
    uint8_t* dst_val, // 指向输出像素的指针 (长度为3)
    float x, float y,
    int src_width, int src_height) {

    int x1 = floorf(x);
    int y1 = floorf(y);
    int x2 = x1 + 1;
    int y2 = y1 + 1;

    // 检查边界
    if (x1 < 0 || y1 < 0 || x2 >= src_width || y2 >= src_height) {
        dst_val[0] = dst_val[1] = dst_val[2] = 0;
        return;
    }

    float dx = x - x1;
    float dy = y - y1;
    
    // 获取四个邻近像素的起始地址
    const uint8_t* val1_ptr = &src[(y1 * src_width + x1) * 3];
    const uint8_t* val2_ptr = &src[(y1 * src_width + x2) * 3];
    const uint8_t* val3_ptr = &src[(y2 * src_width + x1) * 3];
    const uint8_t* val4_ptr = &src[(y2 * src_width + x2) * 3];

    // 对每个通道独立进行双线性插值计算
    for (int c = 0; c < 3; ++c) {
        float result = val1_ptr[c] * (1 - dx) * (1 - dy) +
                       val2_ptr[c] * dx * (1 - dy) +
                       val3_ptr[c] * (1 - dx) * dy +
                       val4_ptr[c] * dx * dy;
        dst_val[c] = static_cast<uint8_t>(result);
    }
}

// CUDA 核函数：remap (三通道)
__global__ void remap_rgb_kernel(    
    uint8_t* dst,
    const uint8_t* src,
    const float* map_x,
    const float* map_y,
    int src_width, int src_height,
    int dst_width, int dst_height) {

    // 获取像素坐标
    int j = blockIdx.x * blockDim.x + threadIdx.x;
    int i = blockIdx.y * blockDim.y + threadIdx.y;
    int dst_index = i * dst_width + j;

    if (i < dst_height && j < dst_width) {
        // 从映射表中读取源坐标
        float src_x = map_x[dst_index];
        float src_y = map_y[dst_index];

        // 检查源坐标是否在图像范围内
        if (src_x >= 0 && src_x < src_width && src_y >= 0 && src_y < src_height) {
            // 进行双线性插值
            uint8_t* dst_val_ptr = &dst[dst_index * 3];
            // const uint8_t* src_val_ptr = &src[dst_index * 3];
            // dst_val_ptr[0] = src_val_ptr[0];
            // dst_val_ptr[1] = src_val_ptr[1];
            // dst_val_ptr[2] = src_val_ptr[2];
            bilinear_interpolate_rgb(src, dst_val_ptr, src_x, src_y, src_width, src_height);
        } else {
           // 源坐标无效，将目标像素置为黑色
           uint8_t* dst_val_ptr = &dst[(dst_index) * 3];
           dst_val_ptr[0] = dst_val_ptr[1] = dst_val_ptr[2] = 0;
        }
    }
}

namespace cuda_kernels_api
{
    int remap(DeviceImage& dst, const DeviceImage& src, const DeviceImage& map_x, const DeviceImage& map_y)
    {
        // 调用核函数
        dim3 block_size(16, 16);
        dim3 grid_size(
            (src.cols + block_size.x - 1) / block_size.x,
            (src.rows + block_size.y - 1) / block_size.y);

        remap_rgb_kernel<<<grid_size, block_size>>>(
            dst.ptr<uint8_t>(),
            src.ptr<uint8_t>(),
            map_x.ptr<float>(),
            map_y.ptr<float>(),
            src.cols, src.rows,
            dst.cols, dst.rows);
        CUDA_CHECK(cudaGetLastError());
        return cudaGetLastError();
    }
} // namespace cuda_kernels_api

} // namespace listereo
} // namespace robosense
