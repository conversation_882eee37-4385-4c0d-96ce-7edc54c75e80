/*
Copyright 2016 Fixstars Corporation

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http ://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "sgm_cuda/device_utility.h"
#include "sgm_cuda/host_utility.h"
namespace robosense {
namespace listereo
{
namespace
{

static constexpr unsigned int WARPS_PER_BLOCK = 8u;
static constexpr unsigned int BLOCK_SIZE = WARPS_PER_BLOCK * WARP_SIZE;
//static constexpr unsigned int MAX_DISPARITY= 256;

__device__ inline uint32_t pack_cost_index(uint32_t cost, uint32_t index)
{
	union {
		uint32_t uint32;
		ushort2 uint16x2;
	} u;
	u.uint16x2.x = static_cast<uint16_t>(index);
	u.uint16x2.y = static_cast<uint16_t>(cost);
	return u.uint32;
}

__device__ uint32_t unpack_cost(uint32_t packed)
{
	return packed >> 16;
}

__device__ int unpack_index(uint32_t packed)
{
	return packed & 0xffffu;
}

using ComputeDisparity = uint32_t(*)(uint32_t, uint32_t, uint16_t*);

__device__ inline uint32_t compute_disparity_normal(uint32_t disp, uint32_t cost = 0, uint16_t* smem = nullptr)
{
	return disp;
}

__device__ inline uint32_t compute_disparity_subpixel(uint32_t disp, uint32_t cost, uint16_t* smem, int sub_pixel_shift, int max_disp)
{
	int subp = disp;
	subp <<= sub_pixel_shift;
	if (disp > 0 && disp < max_disp - 1) {
		const int left = smem[disp - 1];
		const int right = smem[disp + 1];
		const int numer = left - right;
		const int denom = left - 2 * cost + right;
		subp += ((numer << sub_pixel_shift) + denom) / (2 * denom);
	}
	return subp;
}

template<unsigned int MAX_DISPARITY>
__global__ void disp_from_agg_kernel(
	uint16_t *left_dest,
	uint16_t *right_dest,
	const uint8_t *src,
	const float *prior_disp,
	int width,
	int height,
	int pitch,
	float uniqueness,
	int min_disp,
	int max_disp,
	int path_num,
	int sub_pixel_shift,
	uint16_t r_pd,
    uint16_t pd1,
    uint16_t pd2,
    float alpha,
    float beta)
{
	static const unsigned int ACCUMULATION_PER_THREAD = 16u;
	static const unsigned int REDUCTION_PER_THREAD = MAX_DISPARITY / WARP_SIZE;
	static const unsigned int ACCUMULATION_INTERVAL = ACCUMULATION_PER_THREAD / REDUCTION_PER_THREAD;
	static const unsigned int UNROLL_DEPTH = 
		(REDUCTION_PER_THREAD > ACCUMULATION_INTERVAL)
			? REDUCTION_PER_THREAD
			: ACCUMULATION_INTERVAL;

	const size_t cost_step = static_cast<size_t>(max_disp) * width * height;
	const unsigned int warp_id = threadIdx.x / WARP_SIZE;
	const unsigned int lane_id = threadIdx.x % WARP_SIZE;

	const unsigned int y = blockIdx.x * WARPS_PER_BLOCK + warp_id;
	src += y * max_disp * width;
	left_dest  += y * pitch;
	right_dest += y * pitch;
	prior_disp += y * pitch;

	if(y >= height){
		return;
	}

	__shared__ uint16_t smem_cost_sum[WARPS_PER_BLOCK][ACCUMULATION_INTERVAL][MAX_DISPARITY];
	__shared__ float local_prior_disp_segment[WARPS_PER_BLOCK][UNROLL_DEPTH];

	uint32_t right_best[REDUCTION_PER_THREAD];
	for(unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i){
		right_best[i] = 0xffffffffu;
	}

	for(unsigned int x0 = 0; x0 < width; x0 += UNROLL_DEPTH){
		if (lane_id < UNROLL_DEPTH) { //WARP_SIZE >= UNROLL_DEPTH
            const unsigned int global_x = x0 + lane_id;
            // 确保加载索引不越界
            if (global_x < width) {
                local_prior_disp_segment[warp_id][lane_id] = prior_disp[global_x];
            } else {
                local_prior_disp_segment[warp_id][lane_id] = 0.0f; // 或其他默认值
            }
        }
#pragma unroll
		for(unsigned int x1 = 0; x1 < UNROLL_DEPTH; ++x1){
			if(x1 % ACCUMULATION_INTERVAL == 0){
				const unsigned int k = lane_id * ACCUMULATION_PER_THREAD;
				const unsigned int k_hi = k / max_disp;
				const unsigned int k_lo = k % max_disp;
				const unsigned int x = x0 + x1 + k_hi;
				if(x < width){
					const unsigned int offset = x * max_disp + k_lo;
					uint32_t weight_sum[ACCUMULATION_PER_THREAD];
					float d_ref = local_prior_disp_segment[warp_id][x1 + k_hi];
					for(unsigned int i = 0; i < ACCUMULATION_PER_THREAD; ++i){
						float d = k_lo + i;                        
                        float delta = abs(d_ref-d);
                        float delta_2 = delta * delta;
                        float tmp_cost = pd1 * delta_2;
						float is_d_ref_valid = float(d_ref > 0);
						float is_delta_large = float(delta > r_pd);
						float prior_cost = is_d_ref_valid * (is_delta_large * pd2 * alpha + (1 - is_delta_large) * tmp_cost * alpha);
                        // float prior_cost = ((d_ref > 0) ? ((delta > r_pd) ? pd2 : tmp_cost) : 0);
                        weight_sum[i] = (uint32_t)prior_cost;
					}
					for(unsigned int p = 0; p < path_num; ++p){
						uint32_t load_buffer[ACCUMULATION_PER_THREAD];
						load_uint8_vector<ACCUMULATION_PER_THREAD>(
							load_buffer, &src[p * cost_step + offset]);
						for(unsigned int i = 0; i < ACCUMULATION_PER_THREAD; ++i){
							weight_sum[i] += (load_buffer[i] * beta);
						}
					}
					store_uint16_vector<ACCUMULATION_PER_THREAD>(
						&smem_cost_sum[warp_id][k_hi][k_lo], weight_sum);
				}
#if CUDA_VERSION >= 9000
				__syncwarp();
#else
				__threadfence_block();
#endif
			}
			const unsigned int x = x0 + x1;
			if(x < width){
				// Load sum of costs
				const unsigned int smem_x = x1 % ACCUMULATION_INTERVAL;
				const unsigned int k0 = lane_id * REDUCTION_PER_THREAD;
				uint32_t local_cost_sum[REDUCTION_PER_THREAD];
				load_uint16_vector<REDUCTION_PER_THREAD>(
					local_cost_sum, &smem_cost_sum[warp_id][smem_x][k0]);
				// Pack sum of costs and dispairty
				uint32_t local_packed_cost[REDUCTION_PER_THREAD];
				for(unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i){
					local_packed_cost[i] = pack_cost_index(local_cost_sum[i], k0 + i);
				}
				// Update left
				uint32_t best = 0xffffffffu;
				for(unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i){
					best = min(best, local_packed_cost[i]);
				}
				best = subgroup_min<WARP_SIZE>(best, 0xffffffffu);
				// Update right
#pragma unroll
				for(unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i){
					const unsigned int k = lane_id * REDUCTION_PER_THREAD + i;
					const int p = static_cast<int>(((x - k) & ~(max_disp - 1)) + k);
					const unsigned int d = static_cast<unsigned int>(x - p);
#if CUDA_VERSION >= 9000
					const uint32_t recv = __shfl_sync(0xffffffffu,
						local_packed_cost[(REDUCTION_PER_THREAD - i + x1) % REDUCTION_PER_THREAD],
						d / REDUCTION_PER_THREAD,
						WARP_SIZE);
#else
					const uint32_t recv = __shfl(
						local_packed_cost[(REDUCTION_PER_THREAD - i + x1) % REDUCTION_PER_THREAD],
						d / REDUCTION_PER_THREAD,
						WARP_SIZE);
#endif
					right_best[i] = min(right_best[i], recv);
					if(d == max_disp - 1){
						if(0 <= p){
							right_dest[p] = compute_disparity_normal(unpack_index(right_best[i]));
						}
						right_best[i] = 0xffffffffu;
					}
				}
				// Resume updating left to avoid execution dependency
				const uint32_t bestCost = unpack_cost(best);
				const int bestDisp = unpack_index(best);
				bool uniq = true;
				for(unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i){
					const uint32_t x = local_packed_cost[i];
					const bool uniq1 = unpack_cost(x) * uniqueness >= bestCost;
					const bool uniq2 = abs(unpack_index(x) - bestDisp) <= 1;
					uniq &= uniq1 || uniq2;
				}
				uniq = subgroup_and<WARP_SIZE>(uniq, 0xffffffffu);
				if(lane_id == 0){
					left_dest[x] = uniq ? compute_disparity_subpixel(bestDisp, bestCost, smem_cost_sum[warp_id][smem_x], sub_pixel_shift, max_disp) : INVALID_DISP;
				}
			}
		}
	}
	for(unsigned int i = 0; i < REDUCTION_PER_THREAD; ++i){
		const unsigned int k = lane_id * REDUCTION_PER_THREAD + i;
		const int p = static_cast<int>(((width - k) & ~(max_disp - 1)) + k);
		if(0 <= p && p < width){
			right_dest[p] = compute_disparity_normal(unpack_index(right_best[i]));
		}
	}
}

} // namespace

namespace cuda_kernels_api
{

int disp_from_agg(
	const DeviceImage& src, 
	const DeviceImage& src_prior_disp, 
	DeviceImage& dstL, 
	DeviceImage& dstR,
	float uniqueness,
	int min_disp, 	
	int max_disp,
	int path_num,
	uint16_t sub_pixel_shift,
	uint16_t r_pd,
	uint16_t pd1,
	uint16_t pd2,
	float alpha,
	float beta)
{
	const int width = dstL.cols;
	const int height = dstL.rows;
	const int pitch = dstL.step;

	const int gdim = divUp(height, WARPS_PER_BLOCK);
	const int bdim = BLOCK_SIZE;

	const uint8_t* cost = src.ptr<uint8_t>();
	const float* disp_prior = src_prior_disp.ptr<float>();
	uint16_t* dispL = dstL.ptr<uint16_t>();
	uint16_t* dispR = dstR.ptr<uint16_t>();

	//std::cout << "disp_from_agg 0." <<std::endl;
	if(max_disp == 256){
		disp_from_agg_kernel<256><<<gdim, bdim>>>(
			dispL, 
			dispR, 
			cost,
			disp_prior,
			width, 
			height, 
			pitch, 
			uniqueness,
			min_disp,
			max_disp, 
			path_num, 
			sub_pixel_shift,
			r_pd,
			pd1,
			pd2,
			alpha,
			beta);
	}
	else if(max_disp == 128){
		disp_from_agg_kernel<128><<<gdim, bdim>>>(
			dispL, 
			dispR, 
			cost,
			disp_prior,
			width, 
			height, 
			pitch, 
			uniqueness,
			min_disp,
			max_disp, 
			path_num, 
			sub_pixel_shift,
			r_pd,
			pd1,
			pd2,
			alpha,
			beta);
	}
	else{
		return cudaErrorInvalidValue;
	}
	
	//std::cout << "disp_from_agg 1." <<std::endl;
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

} // namespace cuda_kernels_api
} // namespace listereo
}// namespace robosense
