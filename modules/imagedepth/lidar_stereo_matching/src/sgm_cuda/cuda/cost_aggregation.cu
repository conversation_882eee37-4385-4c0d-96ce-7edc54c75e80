/*
Copyright 2016 Fixstars Corporation

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http ://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "sgm_cuda/device_utility.h"
#include "sgm_cuda/host_utility.h"

#define uint8_t unsigned char
#define uint32_t unsigned int
#define uint16_t unsigned short

#if CUDA_VERSION >= 9000
#define SHFL_UP(mask, var, delta, w) __shfl_up_sync((mask), (var), (delta), (w))
#define SHFL_DOWN(mask, var, delta, w) __shfl_down_sync((mask), (var), (delta), (w))
#else
#define SHFL_UP(mask, var, delta, width) __shfl_up((var), (delta), (width))
#define SHFL_DOWN(mask, var, delta, width) __shfl_down((var), (delta), (width))
#endif

// #define MAX_DISPARITY 256
namespace robosense {
namespace listereo
{

namespace cost_aggregation
{

template <typename T> __device__ inline int popcnt(T x) { return 0; }
template <> __device__ inline int popcnt(uint32_t x) { return __popc(x); }
template <> __device__ inline int popcnt(uint64_t x) { return __popcll(x); }

template <unsigned int DP_BLOCK_SIZE, unsigned int SUBGROUP_SIZE>
struct DynamicProgramming
{
	static_assert(DP_BLOCK_SIZE >= 2, "DP_BLOCK_SIZE must be greater than or equal to 2");
	static_assert((SUBGROUP_SIZE & (SUBGROUP_SIZE - 1)) == 0, "SUBGROUP_SIZE must be a power of 2");

	uint32_t last_min;
	uint32_t dp[DP_BLOCK_SIZE];

	__device__ DynamicProgramming() : last_min(0)
	{
		for (unsigned int i = 0; i < DP_BLOCK_SIZE; ++i) { dp[i] = 0; }
	}

	__device__ void update(uint32_t *local_costs, uint32_t p1, uint32_t p2, uint32_t mask)
	{
		const unsigned int lane_id = threadIdx.x % SUBGROUP_SIZE;

		const auto dp0 = dp[0];
		uint32_t lazy_out = 0, local_min = 0;
		{
			const unsigned int k = 0;
			const uint32_t prev = SHFL_UP(mask, dp[DP_BLOCK_SIZE - 1], 1, WARP_SIZE);
			uint32_t out = min(dp[k] - last_min, p2);
			if (lane_id != 0) { out = min(out, prev - last_min + p1); }
			out = min(out, dp[k + 1] - last_min + p1);
			lazy_out = local_min = out + local_costs[k];
		}
		for (unsigned int k = 1; k + 1 < DP_BLOCK_SIZE; ++k) {
			uint32_t out = min(dp[k] - last_min, p2);
			out = min(out, dp[k - 1] - last_min + p1);
			out = min(out, dp[k + 1] - last_min + p1);
			dp[k - 1] = lazy_out;
			lazy_out = out + local_costs[k];
			local_min = min(local_min, lazy_out);
		}
		{
			const unsigned int k = DP_BLOCK_SIZE - 1;
			const uint32_t next = SHFL_DOWN(mask, dp0, 1, WARP_SIZE);
			uint32_t out = min(dp[k] - last_min, p2);
			out = min(out, dp[k - 1] - last_min + p1);
			if (lane_id + 1 != SUBGROUP_SIZE) {
				out = min(out, next - last_min + p1);
			}
			dp[k - 1] = lazy_out;
			dp[k] = out + local_costs[k];
			local_min = min(local_min, dp[k]);
		}
		last_min = subgroup_min<SUBGROUP_SIZE>(local_min, mask);
	}
};

template <unsigned int SIZE>
__device__ unsigned int generate_mask()
{
	static_assert(SIZE <= 32, "SIZE must be less than or equal to 32");
	return static_cast<unsigned int>((1ull << SIZE) - 1u);
}

__device__ inline uint32_t load_census_with_check(const uint32_t* ptr, int x, int w)
{
	return x >= 0 && x < w ? __ldg(ptr + x) : 0;
}

namespace vertical
{

static constexpr unsigned int DP_BLOCK_SIZE = 16u;
static constexpr unsigned int BLOCK_SIZE = WARP_SIZE * 8u;

template <int MAX_DISPARITY>
__global__ void aggregate_vertical_path_kernel(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	const int direction)
{
	static const unsigned int SUBGROUP_SIZE = MAX_DISPARITY /DP_BLOCK_SIZE;
	static const unsigned int PATHS_PER_WARP = WARP_SIZE / SUBGROUP_SIZE;
	static const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	static const unsigned int RIGHT_BUFFER_SIZE = MAX_DISPARITY + PATHS_PER_BLOCK;
	static const unsigned int RIGHT_BUFFER_ROWS = RIGHT_BUFFER_SIZE / DP_BLOCK_SIZE;

	//static_assert(direction == 1 || direction == -1, "");
	if (width == 0 || height == 0) {
		return;
	}

	__shared__ uint32_t right_buffer[2 * DP_BLOCK_SIZE][RIGHT_BUFFER_ROWS + 1];
	DynamicProgramming<DP_BLOCK_SIZE, SUBGROUP_SIZE> dp;

	const unsigned int warp_id = threadIdx.x / WARP_SIZE;
	const unsigned int group_id = threadIdx.x % WARP_SIZE / SUBGROUP_SIZE;
	const unsigned int lane_id = threadIdx.x % SUBGROUP_SIZE;
	const unsigned int shfl_mask = generate_mask<SUBGROUP_SIZE>() << (group_id * SUBGROUP_SIZE);

	const unsigned int x =
		blockIdx.x * PATHS_PER_BLOCK +
		warp_id * PATHS_PER_WARP +
		group_id;
	const unsigned int right_x0 = blockIdx.x * PATHS_PER_BLOCK;
	const unsigned int dp_offset = lane_id * DP_BLOCK_SIZE;

	const unsigned int right0_addr =
		(right_x0 + PATHS_PER_BLOCK - 1) - x + dp_offset;
	const unsigned int right0_addr_lo = right0_addr % DP_BLOCK_SIZE;
	const unsigned int right0_addr_hi = right0_addr / DP_BLOCK_SIZE;

	for (unsigned int iter = 0; iter < height; ++iter) {
		const unsigned int y = (direction > 0 ? iter : height - 1 - iter);
		// Load left to register
		uint32_t left_value;
		if (x < width) {
			left_value = left[x + y * width];
		}
		// Load right to smem
		for (unsigned int i0 = 0; i0 < RIGHT_BUFFER_SIZE; i0 += BLOCK_SIZE) {
			const unsigned int i = i0 + threadIdx.x;
			if (i < RIGHT_BUFFER_SIZE) {
				const int right_x = static_cast<int>(right_x0 + PATHS_PER_BLOCK - 1 - i - min_disp);
				const uint32_t right_value = load_census_with_check(&right[y * width], right_x, width);
				const unsigned int lo = i % DP_BLOCK_SIZE;
				const unsigned int hi = i / DP_BLOCK_SIZE;
				right_buffer[lo][hi] = right_value;
				if (hi > 0) {
					right_buffer[lo + DP_BLOCK_SIZE][hi - 1] = right_value;
				}
			}
		}
		__syncthreads();
		// Compute
		if (x < width) {
			uint32_t right_values[DP_BLOCK_SIZE];
			for (unsigned int j = 0; j < DP_BLOCK_SIZE; ++j) {
				right_values[j] = right_buffer[right0_addr_lo + j][right0_addr_hi];
			}
			uint32_t local_costs[DP_BLOCK_SIZE];
			for (unsigned int j = 0; j < DP_BLOCK_SIZE; ++j) {
				local_costs[j] = popcnt(left_value ^ right_values[j]);
			}
			dp.update(local_costs, p1, p2, shfl_mask);
			store_uint8_vector<DP_BLOCK_SIZE>(
				&dest[dp_offset + x * max_disp + y * max_disp * width],
				dp.dp);
		}
		__syncthreads();
	}
}

int aggregate_up2down(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	const int gdim = (width + PATHS_PER_BLOCK - 1) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_vertical_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1);
	}
	else if(max_disp == 128){
		aggregate_vertical_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

int aggregate_down2up(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	const int gdim = (width + PATHS_PER_BLOCK - 1) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_vertical_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1);
	}
	else if(max_disp == 128){
		aggregate_vertical_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

} // namespace vertical

namespace horizontal
{

static constexpr unsigned int DP_BLOCK_SIZE = 8u;
static constexpr unsigned int DP_BLOCKS_PER_THREAD = 1u;

static constexpr unsigned int WARPS_PER_BLOCK = 4u;
static constexpr unsigned int BLOCK_SIZE = WARP_SIZE * WARPS_PER_BLOCK;

template <int MAX_DISPARITY>
__global__ void aggregate_horizontal_path_kernel(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	const int direction)
{
	static const unsigned int SUBGROUP_SIZE = MAX_DISPARITY /DP_BLOCK_SIZE;
	static const unsigned int SUBGROUPS_PER_WARP = WARP_SIZE / SUBGROUP_SIZE;
	static const unsigned int PATHS_PER_WARP =
		WARP_SIZE * DP_BLOCKS_PER_THREAD / SUBGROUP_SIZE;
	static const unsigned int PATHS_PER_BLOCK =
		BLOCK_SIZE * DP_BLOCKS_PER_THREAD / SUBGROUP_SIZE;

	//static_assert(direction == 1 || direction == -1, "");
	if (width == 0 || height == 0) {
		return;
	}

	uint32_t right_buffer[DP_BLOCKS_PER_THREAD][DP_BLOCK_SIZE];
	DynamicProgramming<DP_BLOCK_SIZE, SUBGROUP_SIZE> dp[DP_BLOCKS_PER_THREAD];

	const unsigned int warp_id = threadIdx.x / WARP_SIZE;
	const unsigned int group_id = threadIdx.x % WARP_SIZE / SUBGROUP_SIZE;
	const unsigned int lane_id = threadIdx.x % SUBGROUP_SIZE;
	const unsigned int shfl_mask = generate_mask<SUBGROUP_SIZE>() << (group_id * SUBGROUP_SIZE);

	const unsigned int y0 =
		PATHS_PER_BLOCK * blockIdx.x +
		PATHS_PER_WARP * warp_id +
		group_id;
	const unsigned int feature_step = SUBGROUPS_PER_WARP * width;
	const unsigned int dest_step = SUBGROUPS_PER_WARP * max_disp * width;
	const unsigned int dp_offset = lane_id * DP_BLOCK_SIZE;
	left += y0 * width;
	right += y0 * width;
	dest += y0 * max_disp * width;

	if (y0 >= height) {
		return;
	}

	// initialize census buffer
	{
		const int x0 = (direction > 0 ? -1 : width) - (min_disp + static_cast<int>(dp_offset));
		for (int dy = 0; dy < DP_BLOCKS_PER_THREAD; ++dy)
			for (int dx = 0; dx < DP_BLOCK_SIZE; ++dx)
				right_buffer[dy][dx] = load_census_with_check(&right[dy * feature_step], x0 - dx, width);
	}

	int x0 = (direction > 0) ? 0 : static_cast<int>((width - 1) & ~(DP_BLOCK_SIZE - 1));
	for (unsigned int iter = 0; iter < width; iter += DP_BLOCK_SIZE) {
		for (unsigned int i = 0; i < DP_BLOCK_SIZE; ++i) {
			const unsigned int x = x0 + (direction > 0 ? i : (DP_BLOCK_SIZE - 1 - i));
			if (x >= width) {
				continue;
			}
			for (unsigned int j = 0; j < DP_BLOCKS_PER_THREAD; ++j) {
				const unsigned int y = y0 + j * SUBGROUPS_PER_WARP;
				if (y >= height) {
					continue;
				}
				const uint32_t left_value = __ldg(&left[j * feature_step + x]);
				if (direction > 0) {
					const uint32_t t = right_buffer[j][DP_BLOCK_SIZE - 1];
					for (unsigned int k = DP_BLOCK_SIZE - 1; k > 0; --k) {
						right_buffer[j][k] = right_buffer[j][k - 1];
					}
					right_buffer[j][0] = SHFL_UP(shfl_mask, t, 1, SUBGROUP_SIZE);
					if (lane_id == 0) {
						right_buffer[j][0] = load_census_with_check(&right[j * feature_step], x - min_disp, width);
					}
				}
				else {
					const uint32_t t = right_buffer[j][0];
					for (unsigned int k = 1; k < DP_BLOCK_SIZE; ++k) {
						right_buffer[j][k - 1] = right_buffer[j][k];
					}
					right_buffer[j][DP_BLOCK_SIZE - 1] = SHFL_DOWN(shfl_mask, t, 1, SUBGROUP_SIZE);
					if (lane_id + 1 == SUBGROUP_SIZE) {
						right_buffer[j][DP_BLOCK_SIZE - 1] = load_census_with_check(&right[j * feature_step], x - (min_disp + dp_offset + DP_BLOCK_SIZE - 1), width);
					}
				}
				uint32_t local_costs[DP_BLOCK_SIZE];
				for (unsigned int k = 0; k < DP_BLOCK_SIZE; ++k) {
					local_costs[k] = popcnt(left_value ^ right_buffer[j][k]);
				}
				dp[j].update(local_costs, p1, p2, shfl_mask);
				store_uint8_vector<DP_BLOCK_SIZE>(
					&dest[j * dest_step + x * max_disp + dp_offset],
					dp[j].dp);
			}
		}
		x0 += static_cast<int>(DP_BLOCK_SIZE) * direction;
	}
}


int aggregate_left2right(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE * DP_BLOCKS_PER_THREAD / SUBGROUP_SIZE;

	const int gdim = (height + PATHS_PER_BLOCK - 1) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_horizontal_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1);
	}
	else if(max_disp == 128){
		aggregate_horizontal_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

int aggregate_right2left(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE * DP_BLOCKS_PER_THREAD / SUBGROUP_SIZE;

	const int gdim = (height + PATHS_PER_BLOCK - 1) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_horizontal_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1);
	}
	else if(max_disp == 128){
		aggregate_horizontal_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

} // namespace horizontal

namespace oblique
{

static constexpr unsigned int DP_BLOCK_SIZE = 16u;
static constexpr unsigned int BLOCK_SIZE = WARP_SIZE * 8u;

template <int MAX_DISPARITY>
__global__ void aggregate_oblique_path_kernel(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	const int X_direction,
	const int Y_direction)
{
	static const unsigned int SUBGROUP_SIZE = MAX_DISPARITY /DP_BLOCK_SIZE;
	static const unsigned int PATHS_PER_WARP = WARP_SIZE / SUBGROUP_SIZE;
	static const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	static const unsigned int RIGHT_BUFFER_SIZE = MAX_DISPARITY + PATHS_PER_BLOCK;
	static const unsigned int RIGHT_BUFFER_ROWS = RIGHT_BUFFER_SIZE / DP_BLOCK_SIZE;

	//static_assert(X_direction == 1 || X_direction == -1, "");
	//static_assert(Y_direction == 1 || Y_direction == -1, "");
	if (width == 0 || height == 0) {
		return;
	}

	__shared__ uint32_t right_buffer[2 * DP_BLOCK_SIZE][RIGHT_BUFFER_ROWS];
	DynamicProgramming<DP_BLOCK_SIZE, SUBGROUP_SIZE> dp;

	const unsigned int warp_id = threadIdx.x / WARP_SIZE;
	const unsigned int group_id = threadIdx.x % WARP_SIZE / SUBGROUP_SIZE;
	const unsigned int lane_id = threadIdx.x % SUBGROUP_SIZE;
	const unsigned int shfl_mask =
		generate_mask<SUBGROUP_SIZE>() << (group_id * SUBGROUP_SIZE);

	const int x0 =
		blockIdx.x * PATHS_PER_BLOCK +
		warp_id * PATHS_PER_WARP +
		group_id +
		(X_direction > 0 ? -static_cast<int>(height - 1) : 0);
	const int right_x00 =
		blockIdx.x * PATHS_PER_BLOCK +
		(X_direction > 0 ? -static_cast<int>(height - 1) : 0);
	const unsigned int dp_offset = lane_id * DP_BLOCK_SIZE;

	const unsigned int right0_addr =
		static_cast<unsigned int>(right_x00 + PATHS_PER_BLOCK - 1 - x0) + dp_offset;
	const unsigned int right0_addr_lo = right0_addr % DP_BLOCK_SIZE;
	const unsigned int right0_addr_hi = right0_addr / DP_BLOCK_SIZE;

	for (unsigned int iter = 0; iter < height; ++iter) {
		const int y = static_cast<int>(Y_direction > 0 ? iter : height - 1 - iter);
		const int x = x0 + static_cast<int>(iter) * X_direction;
		const int right_x0 = right_x00 + static_cast<int>(iter) * X_direction;
		// Load right to smem
		for (unsigned int i0 = 0; i0 < RIGHT_BUFFER_SIZE; i0 += BLOCK_SIZE) {
			const unsigned int i = i0 + threadIdx.x;
			if (i < RIGHT_BUFFER_SIZE) {
				const int right_x = static_cast<int>(right_x0 + PATHS_PER_BLOCK - 1 - i - min_disp);
				const uint32_t right_value = load_census_with_check(&right[y * width], right_x, width);
				const unsigned int lo = i % DP_BLOCK_SIZE;
				const unsigned int hi = i / DP_BLOCK_SIZE;
				right_buffer[lo][hi] = right_value;
				if (hi > 0) {
					right_buffer[lo + DP_BLOCK_SIZE][hi - 1] = right_value;
				}
			}
		}
		__syncthreads();
		// Compute
		if (0 <= x && x < static_cast<int>(width)) {
			const uint32_t left_value = __ldg(&left[x + y * width]);
			uint32_t right_values[DP_BLOCK_SIZE];
			for (unsigned int j = 0; j < DP_BLOCK_SIZE; ++j) {
				right_values[j] = right_buffer[right0_addr_lo + j][right0_addr_hi];
			}
			uint32_t local_costs[DP_BLOCK_SIZE];
			for (unsigned int j = 0; j < DP_BLOCK_SIZE; ++j) {
				local_costs[j] = popcnt(left_value ^ right_values[j]);
			}
			dp.update(local_costs, p1, p2, shfl_mask);
			store_uint8_vector<DP_BLOCK_SIZE>(
				&dest[dp_offset + x * max_disp + y * max_disp * width],
				dp.dp);
		}
		__syncthreads();
	}
}


int aggregate_upleft2downright(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	const int gdim = (width + height + PATHS_PER_BLOCK - 2) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_oblique_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1, 1);
	}
	else if(max_disp == 128){
		aggregate_oblique_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1, 1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

int aggregate_upright2downleft(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	const int gdim = (width + height + PATHS_PER_BLOCK - 2) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_oblique_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1, 1);
	}
	else if(max_disp == 128){
		aggregate_oblique_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1, 1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

int aggregate_downright2upleft(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	const int gdim = (width + height + PATHS_PER_BLOCK - 2) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_oblique_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1, -1);
	}
	else if(max_disp == 128){
		aggregate_oblique_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, -1, -1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

int aggregate_downleft2upright(
	uint8_t *dest,
	const uint32_t *left,
	const uint32_t *right,
	int width,
	int height,
	uint16_t p1,
	uint16_t p2,
	int min_disp,
	int max_disp,
	cudaStream_t stream)
{
	const unsigned int SUBGROUP_SIZE = max_disp /DP_BLOCK_SIZE;
	const unsigned int PATHS_PER_BLOCK = BLOCK_SIZE / SUBGROUP_SIZE;

	const int gdim = (width + height + PATHS_PER_BLOCK - 2) / PATHS_PER_BLOCK;
	const int bdim = BLOCK_SIZE;
	if(max_disp == 256){
		aggregate_oblique_path_kernel<256><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1, -1);
	}
	else if(max_disp == 128){
		aggregate_oblique_path_kernel<128><<<gdim, bdim, 0, stream>>>(dest, left, right, width, height, p1, p2, min_disp, max_disp, 1, -1);
	}
	else{
		return cudaErrorInvalidValue;
	}
	cudaError_t err = cudaGetLastError();
	CUDA_CHECK(err);
	return err;
}

} // namespace oblique

} // namespace cost_aggregation

namespace cuda_kernels_api
{

int cost_aggregation(
    const DeviceImage& srcL, 
    const DeviceImage& srcR, 
    DeviceImage& dst, 
    uint16_t P1, 
    uint16_t P2, 
    int path_num, 
    int min_disp, 
    int max_disp)
{
	const int width = srcL.cols;
	const int height = srcL.rows;

	const uint32_t* left = srcL.ptr<uint32_t>();
	const uint32_t* right = srcR.ptr<uint32_t>();

	cudaStream_t streams[8];
	for (int i = 0; i < path_num; i++)
		cudaStreamCreate(&streams[i]);

	int err = 0;
	err |= cost_aggregation::vertical::aggregate_up2down(dst.ptr<uint8_t>(0), left, right, width, height, P1, P2, min_disp, max_disp, streams[0]);
	err |= cost_aggregation::vertical::aggregate_down2up(dst.ptr<uint8_t>(1), left, right, width, height, P1, P2, min_disp, max_disp, streams[1]);
	err |= cost_aggregation::horizontal::aggregate_left2right(dst.ptr<uint8_t>(2), left, right, width, height, P1, P2, min_disp, max_disp, streams[2]);
	err |= cost_aggregation::horizontal::aggregate_right2left(dst.ptr<uint8_t>(3), left, right, width, height, P1, P2, min_disp, max_disp, streams[3]);
	if (path_num == 8) {
		err |= cost_aggregation::oblique::aggregate_upleft2downright(dst.ptr<uint8_t>(4), left, right, width, height, P1, P2, min_disp, max_disp, streams[4]);
		err |= cost_aggregation::oblique::aggregate_upright2downleft(dst.ptr<uint8_t>(5), left, right, width, height, P1, P2, min_disp, max_disp, streams[5]);
		err |= cost_aggregation::oblique::aggregate_downright2upleft(dst.ptr<uint8_t>(6), left, right, width, height, P1, P2, min_disp, max_disp, streams[6]);
		err |= cost_aggregation::oblique::aggregate_downleft2upright(dst.ptr<uint8_t>(7), left, right, width, height, P1, P2, min_disp, max_disp, streams[7]);
	}

	for (int i = 0; i < path_num; i++)
		cudaStreamSynchronize(streams[i]);
	for (int i = 0; i < path_num; i++)
		cudaStreamDestroy(streams[i]);
	return err;
}
} // namespace details
} // namespace listereo
} // namespace robosense
