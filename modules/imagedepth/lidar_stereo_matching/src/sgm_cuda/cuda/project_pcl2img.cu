#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#include <math.h>
#include <stdint.h>
#include <device_atomic_functions.h>

namespace robosense {
namespace listereo
{

namespace cuda_kernels_api
{
    // 核函数：将三维点投影到图像平面
__global__ void project_pcl2img_kernel(
    const float* points3d,      // 三维点云数据 (X, Y, Z, ...)
    float* p2d_img,             // 深度图像数据
    const int point_num,        // 点云中点的数量
    const float* K,             // 相机内参矩阵
    const int img_width,        // 图像宽度
    const int img_height        // 图像高度
    ) 
{
    // 获取当前线程的全局索引
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= point_num) {
        return;
    }

    // 从点云中读取三维点 (X, Y, Z)
    float X = points3d[idx * 3 + 0];
    float Y = points3d[idx * 3 + 1];
    float Z = points3d[idx * 3 + 2];    

    // 如果Z <= 0，点在相机后面，跳过
    if (Z <= 0.0f) {
        return;
    }

    // 从相机内参矩阵K中读取参数
    float fx = 1.f / K[0];
    float fy = 1.f / K[1];
    float cx = -fx * K[2];
    float cy = -fy * K[3];

    // 计算投影坐标 (u, v)
    float u_proj = fx * X / Z + cx;
    float v_proj = fy * Y / Z + cy;

    // 转换为整数像素坐标
    int u = static_cast<int>(u_proj + 0.5f);
    int v = static_cast<int>(v_proj + 0.5f);

    // 检查是否在图像范围内
    if (u >= 0 && u < img_width && v >= 0 && v < img_height) {
        int img_idx = v * img_width + u;
        //printf("pt: %f, %f, %f, uv: %d, %d, %f\n", X, Y, Z, u, v, Z);
        p2d_img[img_idx] = p2d_img[img_idx] == 0 ? Z : min(p2d_img[img_idx], Z);
    }
}

    int project_pcl2img(
        const DeviceData& points3d,  
        DeviceImage& p2d_img,
        const int point_num,
        const DeviceData& K
        )
    {
        cudaError_t err = cudaSuccess;  
        const int img_width = p2d_img.cols;
        const int img_height = p2d_img.rows;
        // 配置CUDA启动参数
        int block_size = 256;
        int grid_size = (point_num + block_size - 1) / block_size;

        // 调用核函数
        project_pcl2img_kernel<<<grid_size, block_size>>>(
            points3d.ptr<float>(),
            p2d_img.ptr<float>(),
            point_num,
            K.ptr<float>(),
            img_width,
            img_height
        );     

        // 检查错误
        err = cudaGetLastError();
        if (err != cudaSuccess) {
            printf("Failed to launch project_pcl2img_kernel (error code %s)!\n", cudaGetErrorString(err));
            return -1;
        }

        return err;
    }
}

} // namespace listereo
} // namespace robosense