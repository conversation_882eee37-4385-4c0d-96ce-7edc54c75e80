#define DEPTH_FIXED_SCALE 65536.0f

#include <cuda_runtime.h>
#include <device_functions.h>
#include <climits>
#include "sgm_cuda/cuda_kernels_api.h"
#include "sgm_cuda/host_utility.h"

typedef unsigned short uint16_t;
typedef unsigned int uint32_t;

namespace robosense {
namespace listereo
{
__global__ void visoin_disp2depth_kernel(
    float* d_depth,
    const uint16_t* d_disp,
    int width,
    int height,
    float baseline,
    float focal_length,
    float doffs,
    float sub_pixel_scale)
{
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height)
        return;

    int idx = y * width + x;
    uint16_t disp_val = d_disp[idx];
    if (disp_val > 0) {
        float d = (float)disp_val * sub_pixel_scale;
        float bf = baseline * focal_length;
        float z_vis = bf / (d + doffs);
        d_depth[idx] = z_vis; // Convert to fixed-point representation
    }
}

__global__ void disp2depth_kernel(
    float* d_depth,
    const uint16_t* d_disp,
    const float* d_prior_depth,
    int width,
    int height,
    float baseline,
    float focal_length,
    float doffs,
    float sub_pixel_scale,
    int enable_lidar, 
    float semi_rel_error,
    float thres_cross_check)
{
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height)
        return;

    int idx = y * width + x;
    uint16_t disp_val = d_disp[idx];
    //uint32_t z_semi_fixed = d_prior_depth[idx];
    float z_semi = d_prior_depth[idx]; // m

    //if (enable_lidar == 1 && z_semi_fixed < UINT_MAX) {
        // d_depth[idx] = z_semi;
    //}
    
    if (disp_val > 0) {
        float d = (float)disp_val * sub_pixel_scale;
        float bf = baseline * focal_length;
        float z_vis = bf / (d + doffs); // m

        if (enable_lidar == 1) {
            if (z_semi > 0) {
                float delta_z = fabs(z_semi - z_vis);
                if (delta_z > semi_rel_error * z_semi && bf * z_semi - z_vis > thres_cross_check * z_semi * z_vis) {
                    z_vis = 0.f;
                }
            }
        }
        d_depth[idx] = z_vis; // Convert to fixed-point representation
    } else {
        d_depth[idx] = 0;
    }
}

namespace cuda_kernels_api
{
    int vision_disp2depth(
        const DeviceImage& disp, 
        DeviceImage& depth, 
        float baseline, 
        float focal_length,
        float doffs,
        float sub_pixel_scale)
    {
        const int w = disp.cols;
        const int h = disp.rows;

        const dim3 block(16, 16);
	    const dim3 grid(divUp(w, block.x), divUp(h, block.y));

        visoin_disp2depth_kernel<<<grid, block>>>(
            depth.ptr<float>(),
            disp.ptr<uint16_t>(),
            w,
            h,
            baseline,
            focal_length,
            doffs,
            sub_pixel_scale);

        cudaError_t err = cudaGetLastError();
        CUDA_CHECK(err);
        return err;
    
    }

    int disp2depth(
    const DeviceImage& disp, 
    const DeviceImage& prior_depth, 
    DeviceImage& depth, 
    float baseline, 
    float focal_length,
    float doffs,
    float sub_pixel_scale,
    int enable_lidar, 
    float semi_rel_error,
    float thres_cross_check)
    {
        const int w = disp.cols;
        const int h = disp.rows;

        const dim3 block(16, 16);
        const dim3 grid(divUp(w, block.x), divUp(h, block.y));

        disp2depth_kernel<<<grid, block>>>(
            depth.ptr<float>(),
            disp.ptr<uint16_t>(),
            prior_depth.ptr<float>(),
            w,
            h,
            baseline,
            focal_length,
            doffs,
            sub_pixel_scale,
            enable_lidar, 
            semi_rel_error,
            thres_cross_check);

        cudaError_t err = cudaGetLastError();
        CUDA_CHECK(err);
        return err;
    }

}

} // namespace listereo
} // namespace robosense