#include "sgm_cuda/cuda_kernels_api.h"
#include <cuda_runtime.h>
#include "types.h"
#include "sgm_cuda/host_utility.h"
#include <math.h>
#include <stdint.h>
#include <device_atomic_functions.h>

namespace robosense {
namespace listereo
{

namespace cuda_kernels_api
{

struct NeighborPoint {
    float3 p3d; // 3D坐标
    float3 p_uv_d;
    float dist2d_sq; // 与待插值点的2D距离平方
    float dist3d;
};

// 向量和矩阵操作辅助函数
__device__ inline float3 make_float3_from_pt(int u, int v, float z) {
    return make_float3(static_cast<float>(u), static_cast<float>(v), z);
}
__device__ inline float3 cross(float3 a, float3 b) {
    return make_float3(a.y * b.z - a.z * b.y, a.z * b.x - a.x * b.z, a.x * b.y - a.y * b.x);
}
__device__ inline float dot(float3 a, float3 b) {
    return a.x * b.x + a.y * b.y + a.z * b.z;
}

__device__ inline float3 operator-(const float3 &a, const float3 &b) {
    return make_float3(a.x - b.x, a.y - b.y, a.z - b.z);
}

__device__ inline float3 operator*(const float3 &a, float b) {
    return make_float3(a.x * b, a.y * b, a.z * b);
}

__device__ bool normal_points_to_camera(float3 p1, float3 p2, float3 p3) {
    float3 v1 = p2 - p1;
    float3 v2 = p3 - p1;
    float3 normal = cross(v1, v2);
    // 相机在(0,0,0)处，指向相机的向量是-p1
    float3 to_camera = make_float3(-p1.x, -p1.y, -p1.z);
    return dot(normal, to_camera) > 0;
}


// CUDA 核函数：在稀疏深度图上进行局部平面插值
template <int SEARCH_RADIUS>
__global__ void get_dense_depth_kernel(
    const float* sparse_prior_depth,
    float* dense_prior_depth,
    int width,
    int height,
    const float distance_jitter_thresh,
    const float *K)
    {
        const int u = blockIdx.x * blockDim.x + threadIdx.x;
        const int v = blockIdx.y * blockDim.y + threadIdx.y;

        if (u >= width || v >= height) {
            return;
        }

        const int idx = v * width + u;
        // 如果当前像素已经有深度值，则直接复制到输出
        if (sparse_prior_depth[idx] > 0.0f) {
            dense_prior_depth[idx] = (sparse_prior_depth[idx] * 1000); //mm
            return;
        }        
        constexpr int max_neighbors = SEARCH_RADIUS * SEARCH_RADIUS;
        NeighborPoint neighbors[max_neighbors];
        // 从相机内参矩阵K中读取参数
        float fx = 1.f / K[0];
        float fy = 1.f / K[1];
        float cx = -fx * K[2];
        float cy = -fy * K[3];
        float avg_depth = 0;
        float avg_dist = 0;
        int count = 0;
#pragma unroll
        for (int dv = -SEARCH_RADIUS / 2; dv < SEARCH_RADIUS / 2; ++dv) {
            for (int du = -SEARCH_RADIUS / 2; du < SEARCH_RADIUS / 2; ++du) {
                int neighbor_u = u + du;
                int neighbor_v = v + dv;
                
                if (neighbor_u >= 0 && neighbor_u < width && neighbor_v >= 0 && neighbor_v < height) {
                    int neighbor_idx = neighbor_v * width + neighbor_u;
                    float neighbor_depth = sparse_prior_depth[neighbor_idx];                    
                    if (neighbor_depth > 0.0f) {
                        neighbors[count].dist2d_sq = du * du + dv * dv;
                        neighbors[count].p_uv_d = make_float3(neighbor_u, neighbor_v, neighbor_depth);
                        float depth_err = neighbor_depth - avg_depth;
                        count++;  
                        // 和动态均值比较，深度变化太大则不进行插值                      
                        if(count > 1 && abs(depth_err) > distance_jitter_thresh)
                        {
                            return;
                        }
                        avg_depth = avg_depth + depth_err / count;                        
                    }
                }
            }
        }
        // 排序找到2D距离最近的三个点
        for (int i = 0; i < 3; ++i) {
            for (int j = i + 1; j < count; ++j) {
                if (neighbors[j].dist2d_sq < neighbors[i].dist2d_sq) {
                    NeighborPoint temp = neighbors[i];
                    neighbors[i] = neighbors[j];
                    neighbors[j] = temp;
                }
            }
        }
        float depth_sum = 0.0f;
        int avg_count = 0;
        int max_count = min(count, 3);
        for (int i = 0; i < max_count; ++i) {
            depth_sum += neighbors[i].p_uv_d.z;
            avg_count++;
        }
        avg_depth = (depth_sum * (avg_count > 0)) / (avg_count + (avg_count == 0));

        //利用最终的均值再过滤一次
        for (int i = 0; i < avg_count; ++i) {
            float diff = abs(neighbors[i].p_uv_d.z - avg_depth);
            if(diff > distance_jitter_thresh){
                dense_prior_depth[idx] = 0;
                return;
            }
        }
        dense_prior_depth[idx] = avg_depth; // m
    }


    int get_dense_depth(
        const DeviceImage& sparse_prior_depth,
        DeviceImage& dense_prior_depth,
        const int search_radius,
        const float distance_jitter_thresh,
        const DeviceData &K
        )
    {
        const int width = sparse_prior_depth.cols;
        const int height = sparse_prior_depth.rows;

        // 配置CUDA启动参数
        dim3 block_size(32, 32);
        dim3 grid_size(
            (width + block_size.x - 1) / block_size.x,
            (height + block_size.y - 1) / block_size.y
        );
        
        // 调用核函数
        if(search_radius == 8)
            get_dense_depth_kernel<8><<<grid_size, block_size>>>(
                sparse_prior_depth.ptr<float>(),
                dense_prior_depth.ptr<float>(),
                width,
                height,
                distance_jitter_thresh,
                K.ptr<float>()
            );

        if(search_radius == 10)
            get_dense_depth_kernel<10><<<grid_size, block_size>>>(
                sparse_prior_depth.ptr<float>(),
                dense_prior_depth.ptr<float>(),
                width,
                height,
                distance_jitter_thresh,
                K.ptr<float>()
            );
        if(search_radius == 14)
            get_dense_depth_kernel<14><<<grid_size, block_size>>>(
                sparse_prior_depth.ptr<float>(),
                dense_prior_depth.ptr<float>(),
                width,
                height,
                distance_jitter_thresh,
                K.ptr<float>()
            ); 
        if(search_radius == 18)
            get_dense_depth_kernel<18><<<grid_size, block_size>>>(
                sparse_prior_depth.ptr<float>(),
                dense_prior_depth.ptr<float>(),
                width,
                height,
                distance_jitter_thresh,
                K.ptr<float>()
            );       
        cudaError_t err = cudaGetLastError();
        if (err != cudaSuccess) {
            return err;
        }
        
        // 等待核函数执行完毕
        cudaDeviceSynchronize();
        return cudaSuccess;
    }
}

} // namespace listereo
} // namespace robosense