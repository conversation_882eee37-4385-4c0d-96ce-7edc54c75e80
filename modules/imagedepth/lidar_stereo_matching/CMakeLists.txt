cmake_minimum_required(VERSION 3.16)

set(<PERSON><PERSON><PERSON>E_CUDA ON CACHE BOOL "Build with cuda.")
set(ENABLE_OPENCL OFF CACHE BOOL  "Build with opencl.")
set(ENA<PERSON>E_ROSBAG OFF CACHE BOOL  "Load bag data for debug.")

if(<PERSON>NABL<PERSON>_CUDA)
  project(samples LANGUAGES CXX CUDA VERSION 1.0.1)
else()
  project(samples LANGUAGES CXX VERSION 1.0.1)
endif()

set(CMAKE_BUILD_TYPE Release)
set(CMAKE_CXX_STANDARD 17)
set( CMAKE_CXX_FLAGS "-std=c++17 -O3" )

if(ENABLE_OPENCL)
  set(CL_TARGET_OPENCL_VERSION 120 CACHE STRING "OpenCL target version")
endif()

# 定义宏
add_definitions(
  -DLIBSGM_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
  -DLIBSGM_VERSION_MINOR=${PROJECT_VERSION_MINOR}
  -DLIBSGM_VERSION_PATCH=${PROJECT_VERSION_PATCH}
  -DLIBSGM_VERSION="${PROJECT_VERSION}"
  -DALG_RUNNING_MODE=1 # 0: CPU, 1: CUDA, 2: OpenCL
)
find_package(yaml-cpp REQUIRED)
find_package(OpenCV REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED)

if(ENABLE_ROSBAG)
find_package(catkin REQUIRED COMPONENTS
  roscpp
  sensor_msgs
  pcl_conversions
  pcl_ros
  cv_bridge
  image_transport
)
add_definitions(-DDEBUG_ROS_BAG)
endif()

if(ENABLE_CUDA)
  message(STATUS "setting cuda files...")
  find_package(CUDA REQUIRED)
  file(GLOB CUDA_SRCS src/sgm_cuda/cuda/*.cu src/sgm_cuda/*.cpp)
endif()

if(ENABLE_OPENCL)
  message(STATUS "setting opencl files...")
  find_package(OpenCL REQUIRED)
  # 添加ocl文件
  include(CMakeRC.cmake)
  set(OCL_FILES
      src/sgm_ocl/ocl/census.cl
      src/sgm_ocl/ocl/sobel.cl
      src/sgm_ocl/ocl/vision_cost.cl
      src/sgm_ocl/ocl/cost_aggregation.cl
      src/sgm_ocl/ocl/disp_from_agg.cl
      src/sgm_ocl/ocl/remap_agg_left2right.cl
      src/sgm_ocl/ocl/vision_cost_and_aggregation.cl
      src/sgm_ocl/ocl/vertical_cost_aggregation.cl
      src/sgm_ocl/ocl/horizontal_cost_aggregation.cl
      src/sgm_ocl/ocl/median_filter.cl
      src/sgm_ocl/ocl/check_consistency.cl
      src/sgm_ocl/ocl/correct_range.cl
      src/sgm_ocl/ocl/depth2disp.cl
      src/sgm_ocl/ocl/disp2depth.cl
      src/sgm_ocl/ocl/triangle2depth.cl
      src/sgm_ocl/ocl/average_normals.cl
      src/sgm_ocl/ocl/filter_bad_prior_depth.cl
      src/sgm_ocl/ocl/filter_bad_prior_depth_rectify.cl
  )
  cmrc_add_resource_library(rc_ocl_sgm NAMESPACE "ocl_sgm" ${OCL_FILES})
  file(GLOB OCL_SRCS src/sgm_ocl/*.cpp)
endif()

file(GLOB CPU_SRCS src/sgm_cpu/*.cpp)
add_library(lisgm STATIC ${CPU_SRCS} src/depth_estimation_impl.cpp)

if(ENABLE_CUDA)
  target_sources(lisgm PRIVATE ${CUDA_SRCS})
  target_link_libraries(lisgm 
        ${OpenCV_LIBS} 
        ${CUDA_LIBRARIES} 
        ${PCL_LIBRARIES}
        yaml-cpp
        )
  target_include_directories(lisgm
        PUBLIC
        ${EIGEN3_INCLUDE_DIR}
        ${OpenCV_INCLUDE_DIRS}
        ${CUDA_INCLUDE_DIRS}
        ${PCL_INCLUDE_DIRS}
        include
        ) 
endif()

if(ENABLE_OPENCL)
  target_sources(lisgm PRIVATE ${OCL_SRCS})
  target_compile_definitions(lisgm PUBLIC -DCL_TARGET_OPENCL_VERSION=${CL_TARGET_OPENCL_VERSION})
  target_link_libraries(lisgm 
        ${OpenCV_LIBS} 
        OpenCL::OpenCL 
        $<BUILD_INTERFACE:rc_ocl_sgm>
        yaml-cpp
        ${PCL_LIBRARIES}
        )
    target_include_directories(lisgm
        PUBLIC
        ${EIGEN3_INCLUDE_DIR}
        ${OpenCV_INCLUDE_DIRS}
        ${PCL_INCLUDE_DIRS}
        include
        ) 
endif()

# add_executable(demo_fpga src/demo_fpga.cpp)
# target_include_directories(demo_fpga PRIVATE ${PCL_INCLUDE_DIRS})
# target_link_libraries(demo_fpga lisgm ${PCL_LIBRARIES})
# target_include_directories(demo_fpga PUBLIC include) 

add_executable(offline_sample example/offline_sample.cpp include/depth_estimation_interface.h)
target_include_directories(offline_sample PRIVATE ${PCL_INCLUDE_DIRS})
target_link_libraries(offline_sample lisgm ${PCL_LIBRARIES})
if(ENABLE_ROSBAG)
target_include_directories(offline_sample PRIVATE ${catkin_INCLUDE_DIRS})
target_link_libraries(offline_sample lisgm ${catkin_LIBRARIES})
endif()

#安装到install目录下
install(TARGETS offline_sample DESTINATION ${CMAKE_SOURCE_DIR}/install/bin)
install(DIRECTORY ./example/data DESTINATION ${CMAKE_SOURCE_DIR}/install/)
install(DIRECTORY ./config DESTINATION ${CMAKE_SOURCE_DIR}/install/)


# 安装时自动拷贝非系统依赖库
install(CODE [[
  # 获取可执行文件路径
  set(_exe_path "$<TARGET_FILE:offline_sample>")
  # message(STATUS "可执行文件路径: ${_exe_path}")
  
  # 调用 ldd 获取依赖库列表（过滤掉系统库）
  execute_process(
    COMMAND bash -c "ldd ${_exe_path} | grep '=>' | awk '{print $3}' | grep -vE '/usr/lib'"
    OUTPUT_VARIABLE _deps
    OUTPUT_STRIP_TRAILING_WHITESPACE
  )
  # message(STATUS "ldd 输出:\n${_ldd_output}")

  # 创建目标目录
  file(MAKE_DIRECTORY "${CMAKE_SOURCE_DIR}/../install/lib")
  
  # 拷贝每个依赖库
  separate_arguments(_deps_list UNIX_COMMAND ${_deps})
  foreach(_dep IN LISTS _deps_list)
    if(EXISTS "${_dep}")
      # message(STATUS "Installing dependency: ${_dep}")
      file(INSTALL "${_dep}" DESTINATION "${CMAKE_SOURCE_DIR}/../install/lib" FOLLOW_SYMLINK_CHAIN)
    endif()
  endforeach()
]])
