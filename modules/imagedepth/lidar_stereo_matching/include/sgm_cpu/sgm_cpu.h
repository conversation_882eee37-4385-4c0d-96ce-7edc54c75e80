#ifndef LISGM_H
#define LISGM_H
#include <cstdint>
#include <Eigen/Dense>
#include <opencv2/opencv.hpp>

#include "types.h"
namespace robosense {
namespace listereo
{
class StereoSGMCPU
{
public:
  StereoSGMCPU() = default;
  StereoSGMCPU(const LiSGMParam &_param);
  ~StereoSGMCPU() = default;
  void run(cv::Mat &_depth_out, cv::Mat &_depth_vis, cv::Mat &_depth_semi, const cv::Mat _gray_l, const cv::Mat _gray_r, const std::vector<Eigen::Vector3f> &_prior_p3d);
  void computeTriangles(   
    std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &triangles, 
    std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> &uv_p3d, 
    cv::Mat &idx_map,
    const std::vector<Eigen::Vector3f> &prior_p3d,  
    const Eigen::Matrix3d _K, 
    const int _height, 
    const int _width,
    const LiSGMParam &_param);
private:
  void computeDepthFromLidar(cv::Mat &_depth_semi, const std::vector<Eigen::Vector3i> &_prior_p3d, const Eigen::Matrix3d _K, const int _height, const int _width, const LiSGMParam &_param);
  std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> uv2Trianlges(std::vector<std::pair<cv::Point, Eigen::Vector3i>> &_uv_p3d) const;
  template <typename T>
  void filtInvalidTriangles(std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &_triangles, const std::vector<std::pair<cv::Point, Eigen::Vector3i>> &_uv_p3d, const cv::Mat _idx_map, const LiSGMParam &_param) const;
  template <typename T>
  void triangleToSemi(cv::Mat &_depth_semi, const std::vector<std::pair<cv::Point, Eigen::Vector3i>> &_uv_p3d, const std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &_tri_list, cv::Mat _idx_map, const Eigen::Matrix3d _K, const int _height, const int _width, const LiSGMParam &_param);
  void computePriorCost(CostType* &cost_prior_, const cv::Mat _disp_semi);

  void preProcess(cv::Mat _gray_l, cv::Mat _gray_r);
  void computeVisCost(CostType* &cost_vis_, const cv::Mat _gray_l, const cv::Mat _gray_r, const LiSGMParam &_param);

  void addWeightedCost(CostType* &_cost_sum, const CostType* const cost_vis_, const CostType* const cost_prior_, const LiSGMParam &_param);
  void costAggregation(CostType* &_cost_agg, const CostType* const _cost_sum, const cv::Mat _gray, const LiSGMParam &_param);
  void computeAggFromLeft(CostType* &_cost_agg_right, const CostType* const _cost_agg, const LiSGMParam &_param);
  void computeDispFromAgg(cv::Mat &_disp_vis, const CostType* const _cost_agg, const LiSGMParam &_param, const bool _is_left);
  void lrConsistencyCheck(cv::Mat &_disp_left, const cv::Mat _disp_right, const LiSGMParam &_param);
  void postProcess(cv::Mat &_depth_vis, cv::Mat &_disp_vis, const LiSGMParam &_param);
  void crossCheck(cv::Mat &_depth_out, const cv::Mat _depth_vis, const cv::Mat _depth_semi, const LiSGMParam &_param);

  LiSGMParam param_;
  int width_; // [px]
  int height_; // [px]
  Eigen::Matrix3d K_;
  double baseline_; // [m]
  double focal_length_; // [px]
  double doffs_; // [px]
  cv::Ptr<cv::CLAHE> clahe_ = cv::createCLAHE();
};
} // namespace listereo
} // namespace robosense
#endif // LISGM_H