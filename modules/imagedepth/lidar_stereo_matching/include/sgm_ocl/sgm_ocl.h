#pragma once
#if ALG_RUNNING_MODE == 2
#include <CL/cl.h>
#include <cstdint>
#include <regex>
#include <Eigen/Dense>
#include <opencv2/opencv.hpp>
#include "sgm_ocl/device_buffer.hpp"
#include "sgm_ocl/device_kernel.h"
#include "types.h"
namespace robosense {
namespace listereo
{
  enum class OCLImageType{
    DISP_IMAGE_LEFT,
    DISP_IMAGE_RIGHT,
    FILTERED_DISP_IMAGE_LEFT,
    FILTERED_DISP_IMAGE_RIGHT,
    RET_DISP,
    RET_DEPTH,
    PRIOR_DEPTH,
    PRIOR_DISP
  };

  class StereoSGMOpenCL
  {
    static const unsigned int BLOCK_DIM_X = 32;
    static const unsigned int BLOCK_DIM_Y = 32;

    static const unsigned int MAX_NUM_PATHS = 4;

    static const unsigned int VERTICAL_WARP_SIZE = 32;
    static const unsigned int VERTICAL_DP_BLOCK_SIZE = 16;
    static const unsigned int VERTICAL_WARPS_PER_BLOCK = 8;    
    static const unsigned int VERTICAL_BLOCK_SIZE = VERTICAL_WARP_SIZE * VERTICAL_WARPS_PER_BLOCK;

    static const unsigned int HORIZONTAL_WARP_SIZE = 32;
    static const unsigned int HORIZONTAL_DP_BLOCK_SIZE = 8;
    static const unsigned int HORIZONTAL_WARPS_PER_BLOCK = 4;    
    static const unsigned int HORIZONTAL_BLOCK_SIZE = HORIZONTAL_WARP_SIZE * HORIZONTAL_WARPS_PER_BLOCK;

    static const unsigned int DISP_WARP_SIZE = 32;
    static const unsigned int DISP_ACCUMULATION_PER_THREAD = 16;
    static const unsigned int DISP_WARPS_PER_BLOCK = 8;
    static const unsigned int DISP_BLOCK_SIZE = DISP_WARPS_PER_BLOCK * DISP_WARP_SIZE;

    static constexpr float DEPTH_FIXED_SCALE = 65536.0f;

  public: 
    StereoSGMOpenCL() = default;
    StereoSGMOpenCL(cl_context context, 
      cl_device_id device,    
      const listereo::LiSGMParam &params,
      Eigen::Matrix3d K = Eigen::Matrix3d::Identity(),
      float baseline = 0.0f,
      float focal_length = 0.0f,
      float doffs = 0.0f
    );
    ~StereoSGMOpenCL()=default;
    int run_lidar(const std::vector<std::pair<cv::Point2f, Eigen::Vector3f>> &uv_p3d, const std::vector<std::tuple<cv::Point,cv::Point,cv::Point>> &triangles, const cv::Mat &idx_mat, cl_command_queue stream);
    int run_vision(const cv::Mat &left_src, const cv::Mat &right_src, cl_command_queue stream);
    int get_ocl_image(cv::Mat &ret_img, OCLImageType image_type, cl_command_queue stream);

  private:
  
    int ComputeSobelOCL(cl_command_queue stream);

    int ComputeCensusOCL(cl_command_queue stream, int img_idx);
  
    int ComputeVisionCostAndAggOCL(cl_command_queue stream, int direction_x, int direction_y, int idx);

    int ComputeDisparityFromAggOCL(cl_command_queue stream);

    int MedianFilterOCL(cl_command_queue stream, int idx);

    int LRConsistencyCheckCL(cl_command_queue stream);

    int CorrectRangeOCL(cl_command_queue stream);

    int Disp2DepthOCL(cl_command_queue stream);

    int Depth2DispOCL(cl_command_queue stream);

    int ComputePriorDepthOCL(cl_command_queue stream, cl_mem &d_uv_p3d, cl_mem &d_triangles, cl_mem &d_idx_mat, int point_num, int triangle_num);

    int ComputeAverageNormalsOCL(cl_command_queue stream, int point_num);

    int FilterBadPriorDepthOCL(cl_command_queue stream, cl_mem &d_uv_p3d, cl_mem &d_idx_mat, int point_num);

    cl_context m_cl_ctx = nullptr;
    cl_device_id m_cl_device = nullptr;
    cl_kernel m_census_kernel = nullptr; 
    cl_kernel m_sobel_kernel = nullptr; 
    cl_kernel m_cost_and_aggregation_vertical_kernel = nullptr;
    cl_kernel m_cost_and_aggregation_horizontal_kernel = nullptr;
    cl_kernel m_disparity_from_agg_kernel = nullptr;
    cl_kernel m_median_filter_kernel = nullptr; 
    cl_kernel m_consistency_check_kernel = nullptr; 
    cl_kernel m_correct_range_kernel = nullptr; 
    cl_kernel m_disp2depth_kernel = nullptr;
    cl_kernel m_depth2disp_kernel = nullptr;
    cl_kernel m_triangle2depth_kernel = nullptr;
    cl_kernel m_average_normals_kernel = nullptr;
    cl_kernel m_filter_bad_prior_depth_kernel = nullptr;
    sgm::cl::DeviceProgram m_census_program_;
    sgm::cl::DeviceProgram m_sobel_program_;
    sgm::cl::DeviceProgram m_cost_and_aggregation_vertical_program_;
    sgm::cl::DeviceProgram m_cost_and_aggregation_horizontal_program_;
    sgm::cl::DeviceProgram m_disparity_from_agg_program_;
    sgm::cl::DeviceProgram m_median_filter_program_;
    sgm::cl::DeviceProgram m_consistency_check_program_;
    sgm::cl::DeviceProgram m_correct_range_program_;
    sgm::cl::DeviceProgram m_depth2disp_program_;
    sgm::cl::DeviceProgram m_disp2depth_program_;
    sgm::cl::DeviceProgram m_triangle2depth_program_;
    sgm::cl::DeviceProgram m_average_normals_program_;
    sgm::cl::DeviceProgram m_filter_bad_prior_depth_program_;

    cl_command_queue m_agg_streams_[MAX_NUM_PATHS];

    int mi_dst_pitch_ = -1;
    int mi_src_pitch_ = -1;

    float mf_baseline_; // [m]
    float mf_focal_length_; // [px]
    float mf_doffs_; // [px]
    float mf_ks_[4] = {1.0f, 1.0f, 0.0f, 0.0f}; // K matrix for depth conversion
    float mf_thres_cos_ = std::cos(85.0f/180.*M_PI);

    Eigen::Matrix3d mm_K_;

    listereo::LiSGMParam m_params_;

    cl_mem m_d_left_;
    cl_mem m_d_right_;
    cl_mem m_d_census_;
    cl_mem m_d_gx_census_;
    cl_mem m_d_sobel_;
    cl_mem m_d_disp_;
    cl_mem m_d_aggregation_census_cost_;
    //std::vector<cl_mem> m_d_sub_cost_;
    cl_mem m_d_filtered_disp_;
    cl_mem m_d_result_disp_;
    cl_mem m_d_result_depth_;
    cl_mem m_d_prior_depth_;
    cl_mem m_d_prior_disp_;
    cl_mem m_d_ks_;
    cl_mem m_d_normals_;
    cl_mem m_d_normals_fixed_;
    cl_mem m_d_normals_count_;
    
    sgm::cl::DeviceBuffer<uint8_t> m_d_left_buffer_;
    sgm::cl::DeviceBuffer<uint8_t> m_d_right_buffer_;
    std::vector<sgm::cl::DeviceBuffer<uint32_t>> m_d_census_buffer_;
    std::vector<sgm::cl::DeviceBuffer<uint32_t>> m_d_gx_census_buffer_;
    std::vector<sgm::cl::DeviceBuffer<uint8_t>> m_d_sobel_buffer_;
    std::vector<sgm::cl::DeviceBuffer<uint16_t>> m_d_disp_buffer_;
    sgm::cl::DeviceBuffer<uint8_t> m_d_aggregation_census_cost_buffer_;
    std::vector<sgm::cl::DeviceBuffer<uint8_t>> m_d_sub_cost_buffers_;
    std::vector<sgm::cl::DeviceBuffer<uint16_t>> m_d_filtered_disp_buffer_;
    sgm::cl::DeviceBuffer<uint32_t> m_d_census_buffer_all_;
    sgm::cl::DeviceBuffer<uint32_t> m_d_gx_census_buffer_all_;
    sgm::cl::DeviceBuffer<uint8_t> m_d_sobel_buffer_all_;
    sgm::cl::DeviceBuffer<uint16_t> m_d_disp_buffer_all_;
    sgm::cl::DeviceBuffer<uint16_t> m_d_filtered_disp_buffer_all_;
    sgm::cl::DeviceBuffer<uint16_t> m_d_result_disp_buffer_;
    sgm::cl::DeviceBuffer<uint16_t> m_d_result_depth_buffer_;
    sgm::cl::DeviceBuffer<uint32_t> m_d_prior_depth_buffer_;
    sgm::cl::DeviceBuffer<float> m_d_prior_disp_buffer_;
    sgm::cl::DeviceBuffer<float> m_d_ks_buffer_;
    sgm::cl::DeviceBuffer<int> m_d_normals_fixed_buffer_;
    sgm::cl::DeviceBuffer<float> m_d_normals_buffer_;
    sgm::cl::DeviceBuffer<int> m_d_normals_count_buffer_;
  };
} // namespace listereo
} // namespace robosense
#endif