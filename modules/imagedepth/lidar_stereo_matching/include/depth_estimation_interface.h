/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#pragma once

#include <vector>
#include <memory>
#include <functional>
#include <cstring>
#include <utility>

namespace robosense{
namespace listereo{
// --- 数据结构定义 ---
/**
 * @struct ColorPoint3D
 * @brief 着色3D点
 *
 * 包括点的坐标和颜色
 */
struct ColorPoint3D
{
  float x;
  float y;
  float z;
  unsigned char r;
  unsigned char g;
  unsigned char b;
  unsigned char a;
  ColorPoint3D() = default;
  ~ColorPoint3D() = default;
};

/**
 * @struct LidarData
 * @brief 存储Lidar原始数据
 *
 * 原始数据以字节流形式存储，包含时间戳和数据大小等关键信息。
 */
struct LidarData
{
    std::shared_ptr<std::vector<unsigned char>> buffer;        // 指向Lidar数据缓冲区的指针，内存由外部调用者管理
    unsigned int size = 0;                            // 缓冲区的大小（以字节为单位）
    unsigned long long timestamp = 0;                 // 数据的时间戳，单位ns
    LidarData() = default; 
    ~LidarData() = default;
};

/**
 * @struct StereoImage
 * @brief 存储双目相机图像数据
 *
 * 包含左右目图像数据、图像格式、尺寸以及时间戳。
 */
struct StereoImage
{
    std::shared_ptr<std::vector<unsigned char>>left_img_buffer;    // 指向左目图像数据缓冲区的指针
    std::shared_ptr<std::vector<unsigned char>>right_img_buffer;   // 指向右目图像数据缓冲区的指针
    unsigned int img_channel = 0;                       // 图像通道数（例如，灰度为1，RGB为3）
    unsigned int img_width = 0;                         // 图像宽度
    unsigned int img_height = 0;                        // 图像高度
    unsigned long long timestamp = 0;                   // 图像的时间戳，单位ns
    StereoImage() = default;    
    ~StereoImage() = default;
};

/**
 * @struct DepthImage
 * @brief 存储深度图数据
 *
 * 深度图是算法的输出结果之一，包含数据格式和尺寸等信息。
 */
struct DepthImage
{
    std::shared_ptr<std::vector<unsigned char>>depth_buffer;      // 指向深度图数据缓冲区的指针, 深度单位m
    unsigned int bits_size = 0;                          // 深度图中每个像素的位数
    unsigned int img_width = 0;                          // 深度图宽度
    unsigned int img_height = 0;                         // 深度图高度
    unsigned long long timestamp = 0;                    // 深度图的时间戳，单位ns
    DepthImage() = default;
    ~DepthImage() = default;
};

/**
 * @struct ColorPointCloud
 * @brief 存储彩色点云数据
 *
 * 包含点云数据缓冲区，其中每个点包含X、Y、Z坐标以及R、G、B颜色信息。
 */
struct ColorPointCloud {
    std::shared_ptr<std::vector<unsigned char>> point_cloud_buffer; // 指向彩色点云数据缓冲区的指针
    unsigned int point_num = 0;                            // 点云中的点数
    unsigned int point_step = 0;                           // 每个点的字节数 (X,Y,Z,R,G,B)
    unsigned long long timestamp = 0;                      // 点云的时间戳，单位ns
    ColorPointCloud() = default;
    ~ColorPointCloud() = default;
};

/**
 * @struct DepthEstimationStatus
 * @brief 算法运行状态参数
 *
 * 状态参数用于提示算法的当前状态，随着每次回调结果返回
 */

struct DepthEstimationStatus{
    int running_status;         //算法运行情况标记
    int sync_status;            //数据同步情况标记
    double time_delay;          //当前帧的延时情况
};

/**
 * @struct DepthEstimationResults
 * @brief 封装深度估计算法的所有输出结果
 *
 * 通过回调函数一次性返回所有相关结果。
 */
struct DepthEstimationResults {
    StereoImage stereo_image;            // 输入的双目图像
    LidarData lidar_data;                // 同步的Lidar数据
    DepthImage depth_image;              // 算法的输出：深度估计结果
    ColorPointCloud color_point_cloud;   // 算法的输出：彩色点云结果
    unsigned long long ret_timestamp;    // 算法结果时间戳，单位ns
    unsigned long long sys_timestamp;    // 系统时间戳，单位ns
    DepthEstimationStatus status;        // 算法状态
    DepthEstimationResults() = default;
    ~DepthEstimationResults() = default;
};

// --- 回调函数定义 ---

/**
 * @using DepthEstimationCallback
 * @brief 定义回调函数类型
 *
 * 当算法处理完数据后，会调用这个函数将结果返回。
 * 接收一个指向 DepthEstimationResults 结构体的智能指针。
 */
using DepthEstimationCallback = std::function<void(const std::shared_ptr<DepthEstimationResults>&)>;

class DepthEstimationInterface {
public:
    DepthEstimationInterface() = default;
    virtual ~DepthEstimationInterface() {};

    // 1. 读取初始化文件
    // @param config_file_path: 配置文件路径
    // @return: bool, true表示成功，false表示失败
    virtual bool initialize(const std::string& config_file_path) = 0;

    // 2. 设置回调函数
    // @param callback: 回调函数的函数指针
    // @return: bool, true表示成功，false表示失败
    virtual bool setResultsCallback(DepthEstimationCallback callback) = 0;

    // 3. 接收并处理Lidar数据和双目图像
    // 这个接口将接收数据并触发算法处理
    // @param lidar_data: Lidar数据
    // @param image_data: 双目图像数据
    virtual void onDataReceived(const LidarData& lidar_data, const StereoImage& image_data) = 0;
};

// --- 工厂函数声明, 用于创建算法实例 ---
std::unique_ptr<DepthEstimationInterface> createDepthEstimator();

} // namespace listereo
} //namespace robosense