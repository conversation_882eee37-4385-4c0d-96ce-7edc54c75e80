
#pragma once
#include "sgm_cuda/device_allocator.h"
namespace robosense {
namespace listereo
{

enum DeviceDataType
{
    DATA_8U,
    DATA_16U,
    DATA_32U,
    DATA_32S,
    DATA_32F,
	DATA_CUSTOM,
};

class DeviceData
{
public:

	DeviceData();
	DeviceData(int length, DeviceDataType type);
	DeviceData(void* data, int length, DeviceDataType type);

	void create(int length, DeviceDataType type, int custom_size = 0);
	void create(void* data, int length, DeviceDataType type, int custom_size = 0);

	void upload(const void* data, int custom_size = 0);
	void download(void* data, int custom_size = 0, int copy_size = 0) const;
	void fill_zero(int custom_size = 0);

	template <typename T> T* ptr() { return (T*)data; }
	template <typename T> const T* ptr() const { return (T*)data; }

	void* data;
	int length;
	DeviceDataType type;

private:

	DeviceAllocator allocator_;
};

} // namespace listereo
} // namespace robosense
