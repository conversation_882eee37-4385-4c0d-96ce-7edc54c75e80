/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "hyper_vision/imagedepth/imagedepth.h"

namespace robosense::imagedepth
{
  int ImageDepth::Init(const YAML::Node& cfg_node)
  {
    // 1. 创建深度估计接口
    impl_ = listereo::createDepthEstimator();
    if (not impl_)
    {
      RERROR << name() << "Failed to create depth estimation interface...";
      return -1;
    }

    // 2. 获取配置文件路径
    std::string config_file_path;
    if (cfg_node["config_file_path"])
    {
      config_file_path = cfg_node["config_file_path"].as<std::string>();
    }
    else
    {
      RERROR << name() << "No <config file path> specified in configuration...";
      return -2;
    }

    // 3. 初始化深度估计接口
    if (not impl_->initialize(config_file_path))
    {
      RERROR << name() << "Failed to initialize depth estimation...";
      return -3;
    }

    // 4. 设置结果回调函数
    auto callback = [this](const std::shared_ptr<listereo::DepthEstimationResults>& results) {
      this->OnDepthEstimationResults(results);
    };

    if (not impl_->setResultsCallback(callback))
    {
      RERROR << name() << "Failed to set results callback for depth estimation...";
      return -4;
    }

    RINFO << name() << "Initialization successful...";
    return 0;
  }

  int ImageDepth::Start()
  {
    RINFO << name() << "Started...";
    return 0;
  }

  void ImageDepth::Stop()
  {
    std::lock_guard<std::mutex> lock(data_state_mtx_);
    data_state_.reset();
    left_image_ptr_.reset();
    right_image_ptr_.reset();
    depth_frame_ptr_.reset();
    
    if (impl_)
    {
      impl_.reset();
    }
    
    RINFO << name() << "Stopped...";
  }

  int ImageDepth::AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null left image message";
      return -1;
    }

    std::lock_guard<std::mutex> lock(data_state_mtx_);
    data_state_.has_left_image = true;
    left_image_ptr_ = msg_ptr;

    if (data_state_.is_ready())
      TryProcess();

    return 0;
  }

  int ImageDepth::AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null right image message";
      return -1;
    }

    std::lock_guard<std::mutex> lock(data_state_mtx_);
    data_state_.has_right_image = true;
    right_image_ptr_ = msg_ptr;

    if (data_state_.is_ready())
      TryProcess();

    return 0;
  }

  int ImageDepth::AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr)
  {
    if (not msg_ptr)
    {
      RWARN << name() << "Received null depth message";
      return -1;
    }

    std::lock_guard<std::mutex> lock(data_state_mtx_);
    data_state_.has_depth = true;
    depth_frame_ptr_ = msg_ptr;

    if (data_state_.is_ready())
      TryProcess();

    return 0;
  }

  void ImageDepth::SetCallback(const std::function<void(const ImageDepthOutputMsg::Ptr& msg_ptr)>& callback)
  {
    std::lock_guard<std::mutex> lock(output_msg_mtx_);
    output_msg_cb_ = callback;
  }

  void ImageDepth::TryProcess()
  {
    // 调用此函数时已经持有 data_state_mutex_ 锁

    if (not data_state_.is_ready())
      return;

    if (not left_image_ptr_ or not right_image_ptr_ or not depth_frame_ptr_)
    {
      RWARN << name() << "Data pointers are null, cannot process";
      return;
    }

    if (not impl_)
    {
      RWARN << name() << "Depth estimation interface not initialized";
      return;
    }

    auto start = std::chrono::high_resolution_clock::now();

    // 转换图像数据为 StereoImage 格式
    listereo::StereoImage stereo_image = ConvertImageFramesToStereoImage(left_image_ptr_, right_image_ptr_);

    // 转换深度数据为 LidarData 格式
    listereo::LidarData lidar_data = ConvertDepthFrameToLidarData(depth_frame_ptr_);

    // 调用深度估计算法（异步处理，结果通过回调返回）
    impl_->onDataReceived(lidar_data, stereo_image);

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration = end - start;

    // 重置状态
    data_state_.reset();
    left_image_ptr_.reset();
    right_image_ptr_.reset();
    depth_frame_ptr_.reset();

    RINFO << name() << "Data sent to depth estimation algorithm, processing time: " << duration.count() << " ms";
  }

  void ImageDepth::OnDepthEstimationResults(const std::shared_ptr<listereo::DepthEstimationResults>& results)
  {
    if (not results)
    {
      RWARN << name() << "Received null depth estimation results";
      return;
    }

    RINFO << name() << "Received depth estimation results, processing...";

    // 创建输出消息
    ImageDepthOutputMsg::Ptr out_msg_ptr(new ImageDepthOutputMsg);

    // 设置头部时间戳
    out_msg_ptr->header.timestamp = results->ret_timestamp;

    // 转换深度图像数据
    out_msg_ptr->image_depth = ConvertListereoDepthImageToCommonDepthImage(results->depth_image);

    // 转换点云数据
    out_msg_ptr->point_cloud = ConvertListereoColorPointCloudToCommonPointCloud(results->color_point_cloud);

    // 调用回调函数
    std::function<void(const ImageDepthOutputMsg::Ptr&)> callback;
    {
      std::lock_guard<std::mutex> callback_lock(output_msg_mtx_);
      callback = output_msg_cb_;
    }

    if (callback)
    {
      callback(out_msg_ptr);
      RINFO << name() << "Depth estimation result processed and callback invoked";
    }
  }

  /**
   * @brief 将 DepthFrame 数据转换为 LidarData 格式
   * 将结构化的点云数据转换为原始字节流格式，以便与深度估计算法接口兼容
   */
  listereo::LidarData
  ImageDepth::ConvertDepthFrameToLidarData(const std::shared_ptr<common::DepthFrame>& depth_frame)
  {
    robosense::listereo::LidarData lidar_data;

    if (not depth_frame->points)
    {
      RWARN << name() << "Depth frame points is null";
      return lidar_data;
    }

    // 转换时间戳：从 timeval 转换为纳秒
    lidar_data.timestamp = static_cast<unsigned long long>(depth_frame->capture_time.tv_sec) * 1000000000ULL +
                           static_cast<unsigned long long>(depth_frame->capture_time.tv_usec) * 1000ULL;

    // 计算数据大小
    size_t point_size = sizeof(robosense::common::CloudPointXYZIRT_t);
    size_t total_size = static_cast<size_t>(depth_frame->point_nums) * point_size;
    lidar_data.size = static_cast<unsigned int>(total_size);

    // 创建缓冲区并复制数据
    lidar_data.buffer = std::make_shared<std::vector<unsigned char>>(total_size);

    if (total_size > 0 && depth_frame->point_nums > 0 && depth_frame->points)
    {
      // 将点云数据拷贝到字节缓冲区
      // 假设 points 指向一个包含 point_nums 个点的数组
      const char* source_ptr = reinterpret_cast<const char*>(depth_frame->points.get());
      std::memcpy(lidar_data.buffer->data(), source_ptr, total_size);
    }

    RINFO << name() << "Converted DepthFrame to LidarData: "
          << depth_frame->point_nums << " points, "
          << total_size << " bytes, timestamp: "
          << lidar_data.timestamp << "ns";

    return lidar_data;
  }

  /**
   * @brief 将左右 ImageFrame 数据转换为 StereoImage 格式
   * 将结构化的图像数据转换为双目图像格式，以便与深度估计算法接口兼容
   */
  listereo::StereoImage
  ImageDepth::ConvertImageFramesToStereoImage(const std::shared_ptr<common::ImageFrame>& left_frame,
                                              const std::shared_ptr<common::ImageFrame>& right_frame)
  {
    robosense::listereo::StereoImage stereo_image;

    if (not left_frame->data or not right_frame->data)
    {
      RWARN << name() << "Image frame data is null";
      return stereo_image;
    }

    // 验证左右图像尺寸一致
    if (left_frame->width != right_frame->width or left_frame->height != right_frame->height)
    {
      RWARN << name() << "Left and right image dimensions do not match";
      return stereo_image;
    }

    // 设置图像基本信息
    stereo_image.img_width = left_frame->width;
    stereo_image.img_height = left_frame->height;

    // 根据图像格式确定通道数
    switch (left_frame->frame_format)
    {
      case common::FRAME_FORMAT_RGB24:
        stereo_image.img_channel = 3;
        break;
      case common::FRAME_FORMAT_NV12:
      case common::FRAME_FORMAT_YUYV422:
      case common::FRAME_FORMAT_YUV420P:
        stereo_image.img_channel = 1; // 对于YUV格式，我们可能需要转换为灰度
        break;
      case common::FRAME_FORMAT_H265:
        RWARN << name() << "H265 compressed format not supported for stereo processing";
        return stereo_image;
      default:
        stereo_image.img_channel = 3; // 默认RGB
        break;
    }

    // 转换时间戳：使用左图像的时间戳，从 timeval 转换为纳秒
    stereo_image.timestamp = static_cast<unsigned long long>(left_frame->capture_time.tv_sec) * 1000000000ULL +
                            static_cast<unsigned long long>(left_frame->capture_time.tv_usec) * 1000ULL;

    // 创建左图像缓冲区并复制数据
    stereo_image.left_img_buffer = std::make_shared<std::vector<unsigned char>>(left_frame->data_bytes);
    if (left_frame->data_bytes > 0)
    {
      const char* left_source_ptr = reinterpret_cast<const char*>(left_frame->data.get());
      std::memcpy(stereo_image.left_img_buffer->data(), left_source_ptr, left_frame->data_bytes);
    }

    // 创建右图像缓冲区并复制数据
    stereo_image.right_img_buffer = std::make_shared<std::vector<unsigned char>>(right_frame->data_bytes);
    if (right_frame->data_bytes > 0)
    {
      const char* right_source_ptr = reinterpret_cast<const char*>(right_frame->data.get());
      std::memcpy(stereo_image.right_img_buffer->data(), right_source_ptr, right_frame->data_bytes);
    }

    RINFO << name() << "Converted ImageFrames to StereoImage: "
          << stereo_image.img_width << "x" << stereo_image.img_height 
          << ", channels: " << stereo_image.img_channel
          << ", left size: " << left_frame->data_bytes << " bytes"
          << ", right size: " << right_frame->data_bytes << " bytes"
          << ", timestamp: " << stereo_image.timestamp << "ns";

    return stereo_image;
  }

  /**
   * @brief 将 listereo::DepthImage 转换为 common::DepthImage 格式
   * 将深度估计算法输出的深度图数据转换为通用深度图格式
   */
  common::DepthImage
  ImageDepth::ConvertListereoDepthImageToCommonDepthImage(const listereo::DepthImage& listereo_depth_image)
  {
    robosense::common::DepthImage common_depth_image;

    // 检查输入数据有效性
    if (not listereo_depth_image.depth_buffer)
    {
      RWARN << name() << "Input listereo depth image buffer is null";
      return common_depth_image;
    }

    if (listereo_depth_image.img_width == 0 or listereo_depth_image.img_height == 0)
    {
      RWARN << name() << "Invalid depth image dimensions: " 
            << listereo_depth_image.img_width << "x" << listereo_depth_image.img_height;
      return common_depth_image;
    }

    if (listereo_depth_image.bits_size == 0)
    {
      RWARN << name() << "Invalid depth image bits size: " << listereo_depth_image.bits_size;
      return common_depth_image;
    }

    // 设置基本信息
    common_depth_image.width = listereo_depth_image.img_width;
    common_depth_image.height = listereo_depth_image.img_height;
    common_depth_image.header.timestamp = listereo_depth_image.timestamp;
    common_depth_image.header.frame_id = "depth_estimation";

    // 计算期望的缓冲区大小（32位float = 4字节/像素）
    size_t pixel_count = static_cast<size_t>(listereo_depth_image.img_width) * 
                        static_cast<size_t>(listereo_depth_image.img_height);
    size_t expected_buffer_size = pixel_count * sizeof(float);

    if (listereo_depth_image.depth_buffer->size() < expected_buffer_size)
    {
      RWARN << name() << "Insufficient depth buffer size: expected " << expected_buffer_size 
            << " bytes, got " << listereo_depth_image.depth_buffer->size() << " bytes";
      return common_depth_image;
    }

    // 预分配输出数据向量
    common_depth_image.data_vec.resize(pixel_count);

    const unsigned char* buffer_ptr = listereo_depth_image.depth_buffer->data();

    // 只支持32位float深度数据
    if (listereo_depth_image.bits_size != 32)
    {
      RERROR << name() << "Unsupported depth image bits size: " << listereo_depth_image.bits_size 
             << ". Only 32-bit float depth data is supported.";
      return common_depth_image;
    }

    // 解析32位float深度数据
    for (size_t i = 0; i < pixel_count; ++i)
    {
      float depth_value;
      std::memcpy(&depth_value, buffer_ptr + i * sizeof(float), sizeof(float));
      common_depth_image.data_vec[i] = depth_value;
    }

    RINFO << name() << "Converted listereo::DepthImage to common::DepthImage: "
          << common_depth_image.width << "x" << common_depth_image.height 
          << ", bits_size: " << listereo_depth_image.bits_size
          << ", " << common_depth_image.data_vec.size() << " pixels"
          << ", timestamp: " << common_depth_image.header.timestamp << "ns";

    return common_depth_image;
  }

  /**
   * @brief 将 listereo::ColorPointCloud 转换为 common::PointCloud 格式
   * 将深度估计算法输出的彩色点云数据转换为通用点云格式
   */
  common::PointCloud
  ImageDepth::ConvertListereoColorPointCloudToCommonPointCloud(const listereo::ColorPointCloud& listereo_point_cloud)
  {
    robosense::common::PointCloud common_point_cloud;

    // 检查输入数据有效性
    if (not listereo_point_cloud.point_cloud_buffer)
    {
      RWARN << name() << "Input listereo point cloud buffer is null";
      return common_point_cloud;
    }

    if (listereo_point_cloud.point_num == 0)
    {
      RINFO << name() << "Input listereo point cloud has no points";
      return common_point_cloud;
    }

    if (listereo_point_cloud.point_step == 0)
    {
      RWARN << name() << "Invalid point step size: " << listereo_point_cloud.point_step;
      return common_point_cloud;
    }

    // 检查缓冲区大小
    size_t expected_buffer_size = static_cast<size_t>(listereo_point_cloud.point_num) * 
                                 static_cast<size_t>(listereo_point_cloud.point_step);
    
    if (listereo_point_cloud.point_cloud_buffer->size() < expected_buffer_size)
    {
      RWARN << name() << "Insufficient buffer size: expected " << expected_buffer_size 
            << " bytes, got " << listereo_point_cloud.point_cloud_buffer->size() << " bytes";
      return common_point_cloud;
    }

    // 预分配输出点云数据
    common_point_cloud.points_vec.resize(listereo_point_cloud.point_num);

    const unsigned char* buffer_ptr = listereo_point_cloud.point_cloud_buffer->data();

    // 解析数据：支持不同的点结构格式
    // ColorPoint3D 结构: X(4字节), Y(4字节), Z(4字节), R(1字节), G(1字节), B(1字节), A(1字节)
    
    for (unsigned int i = 0; i < listereo_point_cloud.point_num; ++i)
    {
      const unsigned char* point_ptr = buffer_ptr + i * listereo_point_cloud.point_step;
      TagPoint& tag_point = common_point_cloud.points_vec[i];

      // 根据 point_step 的大小来判断数据格式
      if (listereo_point_cloud.point_step == sizeof(listereo::ColorPoint3D))
      {
        // 直接按 ColorPoint3D 结构解析
        const auto* color_point = reinterpret_cast<const listereo::ColorPoint3D*>(point_ptr);
        
        tag_point.x = color_point->x;
        tag_point.y = color_point->y;
        tag_point.z = color_point->z;
        tag_point.r = color_point->r;
        tag_point.g = color_point->g;
        tag_point.b = color_point->b;
        tag_point.a = color_point->a;
      }
      else if (listereo_point_cloud.point_step >= 12)
      {
        // 通用解析方式：假设前12字节是XYZ坐标
        std::memcpy(&tag_point.x, point_ptr + 0, sizeof(float));
        std::memcpy(&tag_point.y, point_ptr + 4, sizeof(float));
        std::memcpy(&tag_point.z, point_ptr + 8, sizeof(float));

        // 解析颜色信息（如果存在）
        if (listereo_point_cloud.point_step >= 15)
        {
          tag_point.r = *(point_ptr + 12);
          tag_point.g = *(point_ptr + 13);
          tag_point.b = *(point_ptr + 14);
        }
        else
        {
          tag_point.r = tag_point.g = tag_point.b = 255; // 默认白色
        }

        // 解析Alpha通道
        if (listereo_point_cloud.point_step >= 16)
        {
          tag_point.a = *(point_ptr + 15);
        }
        else
        {
          tag_point.a = 255; // 默认不透明
        }
      }
      else
      {
        RWARN << name() << "Point step too small: " << listereo_point_cloud.point_step 
              << ", expected at least 12 bytes for XYZ";
        tag_point.x = tag_point.y = tag_point.z = 0.0f;
        tag_point.r = tag_point.g = tag_point.b = tag_point.a = 255;
      }

      // 设置其他字段为默认值
      tag_point.nx = tag_point.ny = tag_point.nz = 0.0f; // 法向量
      tag_point.intensity = 0.0f; // 强度
      tag_point.label = 0; // 标签
      tag_point.timeStamp = listereo_point_cloud.timestamp; // 时间戳
    }

    RINFO << name() << "Converted listereo::ColorPointCloud to common::PointCloud: "
          << listereo_point_cloud.point_num << " points, "
          << listereo_point_cloud.point_step << " bytes per point, "
          << "timestamp: " << listereo_point_cloud.timestamp << "ns";

    return common_point_cloud;
  }



} // namespace robosense::imagedepth
