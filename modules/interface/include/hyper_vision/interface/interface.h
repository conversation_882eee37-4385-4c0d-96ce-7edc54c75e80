/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_INTERFACE_INTERFACE_H_
#define HYPER_VISION_INTERFACE_INTERFACE_H_

#if defined(_WIN32) // Windows 平台 (32位和64位)
#define EXPORT_API __declspec(dllexport)
#elif defined(__linux__) // Linux 平台
#define EXPORT_API __attribute__((visibility("default")))
#elif defined(__APPLE__) // macOS 平台
#define EXPORT_API __attribute__((visibility("default")))
#else // 其他平台，暂未定义
#define EXPORT_API
#endif

//....................初始化后端....................
/// <summary>
/// 初始化后端，程序启动时调用
/// </summary>
/// 输入参数: config_file: 配置文件路径
///          isSocketMode: 是否为Websocket通信模式，默认为false
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int OnInitialize(const char *config_file,
                                       const bool isSocketMode = false);

//....................重置后端....................
/// <summary>
/// 重置后端, 在设置播放模式时会调用(SetPlayMode())，一般无须外部调用
/// </summary>
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int Reset();

//....................重置后端....................
/// <summary>
/// 后端模块开始工作
/// <summary>
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int OnStart();

//....................销毁后端....................
/// <summary>
/// 销毁后端，一般程序退出时调用
/// </summary>
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int OnDestroy();

//....................获取版本信息....................
/// <summary>
/// 获取版本信息
/// </summary>
/// 输入参数：回调函数handler(TagTVersion*)
/// 返回值: 无
extern "C" EXPORT_API void GetVersionInfo(void(handler)(void *));

//....................设置PostProcess Output回调函数....................
/// <summary>
/// 设置PostProcess Output回调函数
/// </summary>
/// 输入参数: 回调函数handler(TagPointCloud*, TagRgbImage*, TagDepthImage*)
extern "C" EXPORT_API void
SetPostProcessMsgHandler(void (*handler)(void *, void *, void *));

//....................设置Slam Output回调函数....................
/// <summary>
/// 设置Slam回调函数 Output回调函数
/// </summary>
/// 输入参数: 回调函数handler(TagPosition*, TagPointCloud*, TagSlamStatus*)
extern "C" EXPORT_API void SetSlamMsgHandler(void (*handler)(void *, void *,
                                                             void *));

//....................设置深度图 Output回调函数....................
/// <summary>
/// 设置深度图 Output回调函数
/// </summary>
/// 输入参数: 回调函数handler(TagRgbImage*, TagRgbImage*, TagDepthImage*)
extern "C" EXPORT_API void
SetImageDepthMsgHandler(void (*handler)(void *, void *, void *));

//....................设置骨架检测 Output回调函数....................
/// <summary>
/// 设置骨架检测 Output回调函数
/// </summary>
/// 输入参数: 回调函数handler(TagSkeletonPoint*, TagRgbImage*, TagRgbImage*)
extern "C" EXPORT_API void SetSkeletonMsgHandler(void (*handler)(void *, void *,
                                                                 void *));

//....................设置播放模式模式....................
/// <summary>
/// 设置数据回放模式
/// </summary>
/// 输入参数: play_mode: 1表示在线; 2表示离线
/// 返回值: 0 表示成功; 其他表示失败
extern "C" EXPORT_API int SetPlayMode(int play_mode = 1);

//....................设置是否启用PostProcess模块....................
/// <summary>
/// 设置是否启用PostProcess模块
/// </summary>
/// 输入参数: is_enable: true表示启用PostProcess模块, 否则为关闭PostProcess模块
/// 返回值: 0 表示启用SLAM模块成功；其他表示失败
extern "C" EXPORT_API int SetEnablePostProcess(bool enable_postprocess);

//....................设置是否启用SLAM模块....................
/// <summary>
/// 设置是否启动SLAM
/// </summary>
/// 输入参数: is_enable: true表示启用SLAM模块, 否则为关闭SLAM模块
/// 返回值: 0 表示启用SLAM模块成功；其他表示失败
extern "C" EXPORT_API int SetEnableSlam(bool enable_slam);

//....................设置是否启用SLAM Relocalization功能....................
/// <summary>
/// 设置是否启动SLAM Relocalization
/// <summary>
/// 输入参数: enable_relocalization: true 表示启用SLAM Relocalization功能,
/// 否则为关闭Relocalization功能 返回值: 0 表示启用SLAM Rleocalization功能成功;
/// 其他表示失败
extern "C" EXPORT_API int
SetEnableRelocalization(bool enable_relocalization = false);

//....................设置是否启动Slam离线优化....................
/// <summary>
/// 设置是否启动Slam离线优化
/// <summary>
/// 输入参数: is_enable: true 表示启用SLAM离线优化, 否则为关闭 SLAM离线优化
/// 返回值: 0表示启用SLAM离线优化成功，其他表示失败
extern "C" EXPORT_API int SetEnableSlamOfflineOpt(bool is_enable);

//....................重启动SLAM模块....................
/// <summary>
/// 重启动SLAM模块
/// </summary>
/// 返回值: 0 表示调用成功，否则调用失败
extern "C" EXPORT_API int ResetSlam();

//....................设置是否启动深度图....................
/// <summary>
/// 设置是否启动深度图
/// </summary>
/// 输入参数: is_enable: true表示启用深度图模块, 否则表示关闭深度图模块
/// 返回值: 0 表示启用深度图模块成功，否则调用失败
extern "C" EXPORT_API int SetEnableImageDepth(bool is_enable);

//....................设置是否启动骨架检测模块....................
/// <summary>
/// 设置是否启动骨架检测模块
/// </summary>
/// 输入参数: is_enable: true表示启用骨架检测模块, 否则表示关闭骨架检测模块
/// 返回值: 0 表示启用骨架检测模块成功，否则调用失败
extern "C" EXPORT_API int SetEnableSkeleton(bool is_enable);

//....................设置是否显示Position....................
/// <summary>
/// 设置是否显示Position
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为显示
extern "C" EXPORT_API void SetEnablePositionShow(bool is_enable_show);

//....................设置是否显示着色RGB点云....................
/// <summary>
/// 设置是否显示着色RGB点云
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为显示
extern "C" EXPORT_API void SetEnablePointCloudShow(bool is_enable_show);

//....................设置是否显示Slam PointCloud....................
/// <summary>
/// 设置是否显示Slam PointCloud
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为显示
extern "C" EXPORT_API void SetEnablePointCloudSlamShow(bool is_enable_show);

//....................设置是否显示TriangleFacet....................
/// <summary>
/// 设置是否显示TriangleFacet
/// </summary>
/// 输入参数: is_enable_show: true表示显示;
/// 否则表示不显示，默认为不显示
extern "C" EXPORT_API void SetEnableTriangleFacetShow(bool is_enable_show);

//
//....................设置是否显示点云投影的RGB图像(AC1)....................
/// <summary>
/// 设置是否显示点云投影的RGB图像(AC1)
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为显示
extern "C" EXPORT_API void SetEnableRgbImageShow(bool is_enable_show);

//....................设置是否显示未着色点云....................
/// <summary>
/// 设置是否显示未着色点云
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void SetEnableDepthShow(bool is_enable_show);

//....................设置是否显示Left Image(AC2)....................
/// <summary>
/// 设置是否显示Left Image(AC2)
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void SetEnableLeftImageShow(bool is_enable_show);

//....................设置是否显示Right Image(AC2)....................
/// <summary>
/// 设置是否显示Right Image(AC2)
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void SetEnableRightImageShow(bool is_enable_show);

//....................设置是否显示Image Depth(AC2)....................
/// <summary>
/// 设置是否显示Image Depth(AC2)
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void SetEnableImageDepthShow(bool is_enable_show);

//....................设置是否显示Skeleton 3D点....................
/// <summary>
/// 设置是否显示Skeleton 3D点
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void SetEnableSkeleton3DPointsShow(bool is_enable_show);

//....................设置是否显示Skeleton 2D点左图像....................
/// <summary>
/// 设置是否显示Skeleton 2D点左图像
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void
SetEnableSkeleton2DPointsLeftImageShow(bool is_enable_show);

//....................设置是否显示Skeleton 2D点右图像....................
/// <summary>
/// 设置是否显示Skeleton 2D点右图像
/// </summary>
/// 输入参数: is_enable_show: true表示显示; 否则表示不显示，默认为不显示
extern "C" EXPORT_API void
SetEnableSkeleton2DPointsRightImageShow(bool is_enable_show);

//....................获取设备信息....................
/// <summary>
/// 获取设备信息
/// </summary>
/// 输出参数: 回调函数handler参数: TagDeviceEvent[] 和 TagDeviceEvent 长度
extern "C" EXPORT_API void GetDeviceInfo(void(handler)(void *, unsigned int));

//....................开/关传感器....................
/// <summary>
/// 开关传感器
/// </summary>
/// <param name="tag">参数</param>
/// <returns></returns>
extern "C" EXPORT_API int OperatorDevice(const char *uuid,
                                         unsigned char operator_type);

//....................设备OTA升级....................
/// <summary>
/// 设备OTA升级
/// </summary>
/// 输入参数: uuid 需要升级的设备UUID
///          operator_type: 0 表示开始升级; 1 表示取消升级; 2 表示结束升级; 3
///          表示检查是否结束; 4 表示升级是否成功 ota_bin_file_path:
///          用于升级的OTA文件的路径
///
/// 返回值: 接口返回值: 0 表示是否调用成功，但是，注意并不表示操作成功
///        ota_result_value: operator_type =
///        3/4时，返回值有效，分别表示检查的结果
/// 注意: 如果需要开始OTA时对应设备为启动状态，则需要先关闭
extern "C" EXPORT_API int OperatorOtaDevice(const char *uuid,
                                            unsigned char operator_type,
                                            const char *ota_bin_file_path,
                                            bool *ota_result_value);

//....................查询设备固件信息....................
/// <summary>
/// 查询设备固件信息:
/// </summary>
/// 输入参数: uuid: 表示需要查询的设备UUID
///          回调函数: handler(Tag_CSring*, TagCString2*)
/// 注意: 如果需要查询的设备(uuid)为启动状态，则需要先关闭
extern "C" EXPORT_API int QueryDeviceInfo(const char *uuid,
                                          void(handler)(void *, void *));

//....................获取数据保存支持的话题名称集合....................
/// <summary>
/// 获取数据保存支持的话题名称集合
/// </summary>
/// 输入参数: hander(TagTConfig[], TagTConfig 的长度)
extern "C" EXPORT_API int GetTopicConfig(void (*handler)(void *, unsigned int));

//....................获取数据保存支持的文件格式....................
/// <summary>
/// 获取数据保存支持的文件格式
/// </summary>
/// 输入参数: handler(TagCString[], TagCString的长度)
/// 注意: 实际上{".bag", ".db3"}, 分别对应ROS和ROS2保存格式
extern "C" EXPORT_API int GetFileFormat(void (*handler)(void *, unsigned int));

//....................设置APP运行时的根目录路径....................
/// <summary>
/// 设置APP运行时的根目录路径
/// </summary>
/// 输入参数: appRootPath: APP运行时的根目录路径
/// 因为安装包的路径，有可能和后端库的路径不一致，为了保证LOG/配置等路径能够正确获取，需要设置
extern "C" EXPORT_API void SetAppRootPath(const char *appRootPath);

//....................开始写ROS/ROS2数据....................
/// <summary>
/// 开始写ROS/ROS2数据
/// </summary>
/// 输入参数: dataDirPath: 数据保存的文件夹路径
///          dataFormat: 保存数据的格式: ".bag"/".db3"
///          tSetting: TagTSetting类型数组指针
///          tSettingCount: tSetting数组长度
/// 返回值: 0 表示成功, 否则表示失败
extern "C" EXPORT_API int StartWriter(const char *dataDirPath,
                                      const char *dataFormat,
                                      const void *tSetting,
                                      unsigned int tSettingCount);

//....................停止写ROS/ROS2数据....................
/// <summary>
/// 停止写ROS/ROS2数据
/// </summary>
/// 返回值: 0表示成功，否则表示失败
extern "C" EXPORT_API int StopWriter();

//....................获取ROS文件的话题信息....................
/// <summary>
/// 获取ROS文件的话题信息
/// </summary>
/// 输入参数: sourceDataFilePath: ROS格式文件路径
///         回调函数: handler(TagCString[], TagCString的长度,
///         开始纳秒时间戳，结束纳秒时间戳)
/// 返回值: 0 表示成功，否则表示失败
extern "C" EXPORT_API int
GetFileTopicsRos1(const char *sourceDataFilePath,
                  void (*handler)(void *, unsigned int, unsigned long long int,
                                  unsigned long long int));

//....................获取ROS2文件的话题信息....................
/// <summary>
/// 获取ROS文件的话题信息
/// </summary>
/// 输入参数: sourceDataFilePath: ROS2格式文件路径
///         回调函数: handler(TagCString[], TagCString的长度,
///         开始纳秒时间戳，结束纳秒时间戳)
/// 返回值: 0 表示成功，否则表示失败
extern "C" EXPORT_API int
GetFileTopicsRos2(const char *sourceDataFilePath,
                  void (*handler)(void *, unsigned int, unsigned long long int,
                                  unsigned long long int));

//....................打开ROS/ROS2数据....................
/// <summary>
/// 开始打开ROS/ROS2数据
/// </summary>
/// 输入参数: sourceDataFilePath: 需要读取的文件
///          mainSyncTopic: 用于主同步的话题名称
///          preLoadMainSyncCnt: 预先载入主同步话题的个数,
///          配置的值越大，内存消耗越大，默认为11帧 tCStrings:
///          Tag_CString类型数组指针，表示需要读取的话题名称 tCStringsCount:
///          tCStrings数组的长度 startTimestampNs/endTimestampNs:
///          读取数据的时间戳范围: -1 表示从头读取或读取到文件结束
/// 返回值: 0 表示成功，否则表示失败
extern "C" EXPORT_API int
StartReader(const char *sourceDataFilePath, const char *mainSyncTopicName,
            unsigned int preLoadMainSyncCnt, const void *tCStrings,
            unsigned int tCStringsCount, long long int startTimestampNs,
            long long int endTimestampNs);

//....................获取主同步帧的帧数....................
/// <summary>
/// 获取主同步帧的帧数
/// </summary>
/// 返回值: 0 >= 0 表示获取帧数正常，否则，获取失败
extern "C" EXPORT_API unsigned int GetTotalFrameCount();

//....................获取当前主同步帧的纳秒时间戳....................
/// <summary>
/// 获取当前主同步帧的纳秒时间戳
/// </summary>
/// 返回值: 获取的时间戳不等于0表示成功，否则获取失败
extern "C" EXPORT_API unsigned long long GetCurrentTimestamp();

//....................获取当前主同步帧的帧序号....................
/// <summary>
/// 获取当前主同步帧的帧序号，从0开始计数
/// </summary>
/// 返回值: 返回值 >= 0表示正常，否则，表示未开始播放或播放结束
extern "C" EXPORT_API int GetCurrentFrameIndex();

//....................获取当前初始化数据回放的进度....................
/// <summary>
/// 获取当前初始化数据回放的进度
/// </summary>
/// 返回值: 0-100取值，等于100时表示初始化完成，可以开始进行播放
extern "C" EXPORT_API unsigned int GetInitiProgress();

//....................检查当前是否为连续播放....................
/// <summary>
/// 检查当前是否为连续播放
/// </summary>
/// 返回值: true 表示当前为连续播放，否则为非连续播放
extern "C" EXPORT_API bool CheckIsPlaying();

//....................跳帧播放....................
/// <summary>
/// 跳帧播放
/// </summary>
/// 输入参数: skip: 表示跳帧的数量，例如-1表示播放前一帧，1表示播放下一帧
/// 返回值: 0 表示跳帧播放成功，否则表示失败
extern "C" EXPORT_API int SkipFrame(int skip);

//....................连续播放....................
/// <summary>
/// 连续播放
/// </summary>
/// 返回值: 0 表示开始连续播放成功，否则表示失败
extern "C" EXPORT_API int Play();

//....................暂停连续播放....................
/// <summary>
/// 暂停连续播放
/// </summary>
/// 返回值: 0 表示暂停连续播放成功，否则表示失败
extern "C" EXPORT_API int Pause();

//....................停止(退出)播放....................
/// <summary>
/// 停止(退出)播放
/// </summary>
/// 返回值: 0 表示停止(退出)播放成功，否则表示失败
extern "C" EXPORT_API int StopReader();

//....................获取AC1的统计帧率信息....................
/// <summary>
/// 获取AC1的统计帧率信息
/// </summary>
/// 输入参数: uuid 需要获取帧率信息的设备UUID
///          image_freq: 表示图像帧率
///          depth_freq: 表示点云帧率
///          imu_freq: 表示IMU帧率
/// 返回值: 0 表示接口调用成功，否则表示失败
extern "C" EXPORT_API int GetACDataFrequence(const char *uuid,
                                             float *image_freq,
                                             float *depth_freq,
                                             float *imu_freq);

//....................保存AC1的数据到文件....................
/// <summary>
/// 保存AC1的数据到文件
/// <summary>
/// 输入参数: save_dir_path: 表示保存的根目录
///          save_oprerator_type: 见SaveACDataOperatorType类型说明，
/// 即如果低位第一位为1表示保存图像
///   如果低位第二位为1表示保存点云
///   如果低位第三位为1表示保存IMU
/// 例如save_oprerator_type = 7，则表示三种类型数据都保存
/// 返回值: 0 表示保存AC1数据成功，否则表示失败
extern "C" EXPORT_API int SaveACDataToFile(const char *save_dir_path,
                                           const int save_oprerator_type);

//....................开始保存Slam数据到指定路径....................
/// <summary>
/// 开始保存Slam数据到指定路径
/// </summary>
/// 输入参数: slam_data_save_path: 表示SLAM数据保存路径
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int StartSlamDataWriter(const char *slam_data_save_path);

//....................结束保存Slam数据....................
/// <summary>
/// 结束保存Slam数据
/// </summary>
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int StopSlamDataWriter();

//....................开始将保存的Slam数据合并为地图....................
/// <summary>
/// 开始将保存的Slam数据合并为地图
/// </summary>
/// 输入参数: slam_data_read_path: 表示SLAM数据读取路径
///          slam_data_export_path: 表示SLAM数据导出路径
///          is_enable_segment: 表示是否分段保存
///          segment_size_bytes_th: 表示分段保存的阈值，单位byte
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int
StartSlamDataExporter(const char *slam_data_read_path,
                      const char *slam_data_export_path,
                      const bool is_enable_segment,
                      const unsigned long long int segment_size_bytes_th);

//....................获取Slam数据合并为地图的进度....................
/// <summary>
/// 获取Slam数据合并为地图的进度
/// </summary>
/// 输入参数: progress: 表示0-100的值, 表示导出进度
/// <returns>返回值: 0表示调用成功，否则调用失败,
extern "C" EXPORT_API int GetSlamDataExporterProgress(int *progress);

//....................获取Slam数据合并为地图的结果状态....................
/// <summary>
/// 获取Slam数据合并为地图的结果状态
/// </summary>
/// 输入参数: status: true表示正确完成，否则表示失败
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int CheckSlamDataExporterStatus(bool *status);

//....................停止将保存的Slam数据合并为地图....................
/// <summary>
/// 停止将保存的Slam数据合并为地图
/// </summary>
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int StopSlamDataExporter();

//....................获取工具对应的标定文件中的序列号....................
/// <summary>
/// 获取工具对应的标定文件中的序列号
/// </summary>
/// 输入参数: 回调函数: handler(Tag_CString*)
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int GetCalibrationUUID(void (*handler)(void *));

//....................拷贝指定标定文件到工具标定文件....................
/// <summary>
/// 拷贝指定标定文件到工具标定文件: 更新后软件进行重启
/// </summary>
/// 输入参数: calibaration_file_path: 表示需要拷贝的标定文件路径
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int
CopyCalibFileToConfigDirectory(const char *calibaration_file_path);

//....................导出AC硬件中的标定文件信息到YAML文件....................
/// <summary>
/// 导出AC硬件中的标定文件信息到YAML文件: 如果设备已经打开，则需要关闭
/// </summary>
/// 输入参数: uuid: 表示需要导出的设备的UUID
///          export_calib_dir_path: 导出标定文件信息的保存路径
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int
ExportCalibFileFromHardware(const char *uuid,
                            const char *export_calib_dir_path);

//....................将标定文件写入到AC硬件中....................
/// <summary>
/// 将标定文件写入到AC硬件中: 如果设备已经打开，则需要关闭
/// </summary>
/// 输入参数: uuid: 表示需要写入标定参数的设备的UUID
/// 返回值: 0表示调用成功，否则调用失败
extern "C" EXPORT_API int WriteCalibFileToHardware(const char *uuid);

#endif // HYPER_VISION_INTERFACE_INTERFACE_H_
