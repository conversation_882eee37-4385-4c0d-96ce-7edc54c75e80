cmake_minimum_required(VERSION 3.5)

#========================
# Project
#========================

set(CUR_LIB dataio)
project(${CUR_LIB})

set(CMAKE_CXX_STANDARD 17)  # 强制C++17
add_definitions(-std=c++17)
# Platform-specific settings
if(WIN32)
    # Windows-specific settings
    add_compile_options(/W4)  # Warning level 4 on Windows
elseif(UNIX AND NOT APPLE)
    # Linux/Ubuntu-specific settings
    add_compile_options(-Wall -Wextra -pedantic)  # Enable common warnings on Linux
endif()
add_compile_options(-std=c++17)
add_definitions(-DDEBUG_LEVEL=0)

include(cmake/srcs.cmake)

set(${CUR_LIB} PARENT_SCOPE)


