/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef SLAM_SLAM_H_
#define SLAM_SLAM_H_

#include "hyper_vision/common/common.h"
#include "laserMapping.h"
#include "preprocess.h"
#include "relocalization/relocalization.h"
#include "slam/message/message.h"


namespace robosense::slam {

class Slam {
public:
  using Ptr = std::shared_ptr<Slam>;

  Slam() { slam_ptr_.reset(new RsSlam); }

  ~Slam() { Stop(); }

  int Init(const YAML::Node& cfg_node);

  void AddData(const std::shared_ptr<robosense::common::MotionFrame> &msg_ptr);

  void AddData(const std::shared_ptr<robosense::common::DepthFrame> &msg_ptr);

  void AddData(const std::shared_ptr<robosense::common::ImageFrame> &msg_ptr);

  int Start() { return startSlam(); }

  void Stop() { stopSlam(); }

  void SetCallback(const std::function<void(const SlamOutputMsg::Ptr &msg_ptr)> &callback);

  void SetRelocationFlag(const bool is_enable)
  {
    enable_relocation_.store(is_enable);
  }

private:
  static std::string name() { return " Slam: "; }

  static long long timevalToNanoseconds(const struct timeval& tv)
  {
    return static_cast<long long>(tv.tv_sec) * 1000000000 +
           static_cast<long long>(tv.tv_usec) * 1000;
  }

private:
  int initSlam();

  int startSlam();

  int stopSlam();

  int initPreprocess();

  int initRelocation();

  void slamLIVOResultCallback(const LIVOResult &livo_res);

  void registerSlamCallback();

  void relocationCallback(const relocalization::RestartInfo &restart_info);

  void registerRelocationCallback();

  void resetSlamStatus();

  void resetCurMapId() { cur_map_id_ = -1; }

  void restartSlam();

private:
  std::string config_path_;
  shared_ptr<Preprocess> preprocess_ptr_;

  std::mutex slam_ptr_mtx_;
  RsSlam::Ptr slam_ptr_;

  // Slam 的状态
  std::mutex slam_status_mtx_;
  robosense::common::SlamStatus slam_status_;

  std::function<void(const SlamOutputMsg::Ptr &msg_ptr)> output_msg_callback_;

  // 自动Restart
  std::atomic<bool> need_restart_{false};
  std::atomic<bool> detected_restart_signal_{true};
  std::shared_ptr<relocalization::ReLocalization> relocalization_ptr_;
  std::atomic<int32_t> cur_map_id_ = -1;

  std::atomic<bool> enable_relocation_{false};

  const std::string RS_META_YAML_FILE_NAME = "RS_META.yaml";
};

} // namespace robosense::slam


#endif // SLAM_SLAM_H_
