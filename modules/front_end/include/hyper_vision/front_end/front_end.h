/******************************************************************************
 * Copyright 2017 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HYPER_VISION_FRONT_END_FRONT_END_H_
#define HYPER_VISION_FRONT_END_FRONT_END_H_


#include "hyper_vision/imagedepth/imagedepth.h"                  // Left图像+Right图像
#include "hyper_vision/postprocess/postprocess.h"                // 点云投影+着色
#include "slam/slam.h"                                           // SLAM
#include "hyper_vision/skeleton_detection/skeleton_detection.h"  // 骨架检测


namespace robosense::front_end
{

  class FrontEnd
  {
  public:
    using Ptr = std::shared_ptr<FrontEnd>;
    using ConstPtr = std::shared_ptr<const FrontEnd>;

    using FRONT_END_POSTPROCESS_OUTPUT_CALLBACK = std::function<void(const robosense::postprocess::PostprocessOutputMsg::Ptr&)>;
    using FRONT_END_SLAM_OUTPUT_CALLBACK        = std::function<void(const robosense::slam::SlamOutputMsg::Ptr&)>;
    using FRONT_END_IMAGE_DEPTH_OUTPUT_CALLBACK = std::function<void(const robosense::imagedepth::ImageDepthOutputMsg::Ptr&)>;
    using FRONT_END_SKELETON_OUTPUT_CALLBACK    = std::function<void(const robosense::skeleton::SkeletonOutputMsg::Ptr&)>;

  public:
    FrontEnd() = default;

    ~FrontEnd() { Stop(); }

    // 初始化
    int Init(const YAML::Node& cfg_node);

    // 开始功能
    int Start();

    // 结束功能
    int Stop();

    // 重置所有模块
    int Reset();

    // 重置 postprocess 模块
    int ResetPostProcess();

    // 重置 slam 模块
    int ResetSlam();

    // 重置 image depth 模块
    int ResetImageDepth();

    // 重置 skeleton detection 模块
    int ResetSkeleton();

    // 设置是否启用后处理模块 
    int SetEnablePostProcess(bool is_enable);

    // 设置是否启用Slam模块 
    int SetEnableSlam(bool is_enable); 

    // 设置Slam模块是否开启Re-localization功能
    int EnableSlamRelocalization(bool is_enable);

    // 设置Slam模块是否进行离线优化
    int EnableSlamOfflineOpt(bool isEnable);

    // 设置是否启用ImageDepth
    int SetEnableImageDepth(bool is_enable); 

    // 设置是否启用Skeleton 
    int SetEnableSkeleton(bool is_enable); 

    // 输入Imu数据
    void AddData(const std::shared_ptr<robosense::common::MotionFrame>& msg_ptr);

    // 输入点云数据
    void AddData(const std::shared_ptr<robosense::common::DepthFrame>& msg_ptr);

    // 输入AC1 图像数据
    void AddDataImage(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    // 输入AC2: Left 图像数据
    void AddDataImageLeft(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    // 输入AC2：Right 图像数据
    void AddDataImageRight(const std::shared_ptr<robosense::common::ImageFrame>& msg_ptr);

    // [注意] interface 只会在程序初始化的时候，调用一次 SetCallback

    // 设置 postprocess 回调函数
    void SetCallback(const std::function<void(const postprocess::PostprocessOutputMsg::Ptr&)>& callback);

    // 设置 slam 回调函数
    void SetCallback(const std::function<void(const slam::SlamOutputMsg::Ptr&)>& callback);

    // 设置 depth estimation 回调函数
    void SetCallback(const std::function<void(const imagedepth::ImageDepthOutputMsg::Ptr&)>& callback);

    // 设置 skeleton detection 回调函数
    void SetCallback(const std::function<void(const skeleton::SkeletonOutputMsg::Ptr&)>& callback); 

  private:
    static std::string name() { return " FrontEnd: "; }

    int ResetPostProcessInternal();
    int ResetSlamInternal();
    int ResetSkeletonInternal();
    int ResetImageDepthInternal();

  private:
    YAML::Node cfg_node_;

    // postprocess algorithm module
    mutable std::mutex post_mutex_;
    postprocess::MultiSensorPostprocess::Ptr post_algo_;
    std::function<void(const postprocess::PostprocessOutputMsg::Ptr&)> post_callback_;

    // slam algorithm module
    mutable std::mutex slam_mutex_;
    slam::Slam::Ptr slam_algo_;
    std::function<void(const slam::SlamOutputMsg::Ptr&)> slam_callback_;

    // depth estimation algorithm module
    mutable std::mutex depth_mtx_;
    imagedepth::ImageDepth::Ptr depth_algo_;
    std::function<void(const imagedepth::ImageDepthOutputMsg::Ptr&)> depth_callback_;

    // skeleton detection algorithm module
    mutable std::mutex skeleton_mutex_;
    skeleton::SkeletonDetection::Ptr skeleton_algo_;
    std::function<void(const skeleton::SkeletonOutputMsg::Ptr&)> skeleton_callback_;
  };

} // namespace robosense::front_end


#endif // HYPER_VISION_FRONT_END_FRONT_END_H_
